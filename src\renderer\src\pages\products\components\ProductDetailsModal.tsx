import { useEffect, useState } from 'react'
import {
  Modal,
  Form,
  InputNumber,
  message,
  Spin,
  Typography,
  Space,
  Descriptions,
  Button,
  Row,
  Col,
  Input
} from 'antd'
import { productApi } from '@/renderer/services'
import { ProductDetails } from '@/common/types'
import { formatCurrency } from '@/renderer/utils'

const { Title } = Typography

interface ProductDetailsModalProps {
  productId: string | null
  open: boolean
  onClose: () => void
  setRefreshTrigger: (trigger: any) => void
}

export const ProductDetailsModal = ({
  productId,
  open,
  onClose,
  setRefreshTrigger
}: ProductDetailsModalProps) => {
  const [form] = Form.useForm()
  const [loading, setLoading] = useState(false)
  const [product, setProduct] = useState<ProductDetails | null>(null)
  const [isEditing, setIsEditing] = useState(false)
  const [desiredProfit, setDesiredProfit] = useState<number | null>(null)

  useEffect(() => {
    if (productId && open) {
      fetchProductDetails()
    }
  }, [productId, open])

  const fetchProductDetails = async () => {
    if (!productId) return
    setLoading(true)
    const response = await productApi.getProduct(productId)
    setLoading(false)
    if (response.error.error || response.data.error) {
      message.error(response.error.message || response.data.error.message)
      return
    }
    setProduct(response.data.data)
    form.setFieldsValue({
      salePrice: response.data.data.salePrice,
      minStockLevel: response.data.data.minStockLevel,
      nature: response.data.data.nature,
      tag: response.data.data.tag
    })
  }

  const handleUpdate = async (values: any) => {
    if (!productId) return
    setLoading(true)

    const response = await productApi.updateProduct(productId, values)
    setLoading(false)

    if (response.error.error || response.data.error) {
      message.error(response.error.message || response.data.error.message)
      return
    }
    message.success('Product updated successfully')
    fetchProductDetails() // Refetch instead of closing
    setIsEditing(false)
    setRefreshTrigger((prev) => prev + 1)
  }

  const calculateCurrentProfit = () => {
    if (!product?.stockValue?.averagePurchasePrice || !product.salePrice) return null
    return (
      ((product.salePrice - product.stockValue.averagePurchasePrice) /
        product.stockValue.averagePurchasePrice) *
      100
    )
  }

  const calculateSuggestedPrice = () => {
    if (!product?.stockValue?.averagePurchasePrice || !desiredProfit) return null
    return product.stockValue.averagePurchasePrice * (1 + desiredProfit / 100)
  }

  const renderContent = () => {
    if (loading) {
      return (
        <div className="flex items-center justify-center py-8">
          <Spin />
        </div>
      )
    }

    if (!product) return null

    const currentProfit = calculateCurrentProfit()
    const suggestedPrice = calculateSuggestedPrice()

    return (
      <div className="flex flex-col gap-6">
        <Descriptions column={1} bordered size="small">
          <Descriptions.Item label="Product Name">{product.name}</Descriptions.Item>
          <Descriptions.Item label="Product ID">{product.productId}</Descriptions.Item>
          <Descriptions.Item label="Category">{product.category.name}</Descriptions.Item>
          {!isEditing && product.nature && (
            <Descriptions.Item label="Nature">{product.nature}</Descriptions.Item>
          )}
          {!isEditing && product.tag && (
            <Descriptions.Item label="Tag">{product.tag}</Descriptions.Item>
          )}
        </Descriptions>

        {product.stockValue && (
          <Row gutter={16}>
            <Col span={12}>
              <div className="rounded-lg border p-4">
                <Title level={5} className="!mb-4">
                  Stock Value Details
                </Title>
                <Descriptions column={1} size="small">
                  <Descriptions.Item label="Total Quantity">
                    {product.stockValue.totalQuantity}
                  </Descriptions.Item>
                  <Descriptions.Item label="Total Purchase Value">
                    {formatCurrency(product.stockValue.totalPurchaseValue)}
                  </Descriptions.Item>
                  <Descriptions.Item label="Average Purchase Price">
                    {formatCurrency(product.stockValue.averagePurchasePrice)}
                  </Descriptions.Item>
                </Descriptions>
              </div>
            </Col>
            <Col span={12}>
              <div className="rounded-lg border p-4">
                <Title level={5} className="!mb-4">
                  Profit Analysis
                </Title>
                <Descriptions column={1} size="small">
                  <Descriptions.Item label="Current Profit %">
                    {currentProfit ? `${currentProfit.toFixed(2)}%` : 'N/A'}
                  </Descriptions.Item>
                  <Descriptions.Item label="Desired Profit %">
                    <InputNumber
                      disabled={!isEditing}
                      style={{ width: '100%' }}
                      min={0}
                      precision={2}
                      value={desiredProfit}
                      onChange={(value) => setDesiredProfit(value)}
                      suffix="%"
                    />
                  </Descriptions.Item>
                  <Descriptions.Item label="Suggested Price">
                    {suggestedPrice ? formatCurrency(suggestedPrice) : 'N/A'}
                  </Descriptions.Item>
                </Descriptions>
              </div>
            </Col>
          </Row>
        )}

        <Form form={form} layout="vertical" onFinish={handleUpdate}>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="salePrice"
                label="Sale Price"
                rules={[{ required: true, message: 'Please enter sale price' }]}
              >
                <InputNumber
                  disabled={!isEditing}
                  style={{ width: '100%' }}
                  min={0}
                  precision={2}
                  prefix="Rs. "
                  formatter={(value) => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="minStockLevel" label="Minimum Stock Level">
                <InputNumber disabled={!isEditing} style={{ width: '100%' }} min={0} />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item name="nature" label="Nature">
                <Input disabled={!isEditing} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="tag" label="Tag">
                <Input disabled={!isEditing} />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </div>
    )
  }

  return (
    <Modal
      title="Product Details"
      open={open}
      onCancel={() => {
        setIsEditing(false)
        onClose()
      }}
      footer={
        isEditing ? (
          <Space>
            <Button
              onClick={() => {
                setIsEditing(false)
                form.setFieldsValue({
                  salePrice: product?.salePrice,
                  minStockLevel: product?.minStockLevel,
                  nature: product?.nature,
                  tag: product?.tag
                })
              }}
            >
              Cancel
            </Button>
            <Button type="primary" onClick={() => form.submit()} loading={loading}>
              Update
            </Button>
          </Space>
        ) : (
          <Space>
            <Button type="primary" onClick={() => setIsEditing(true)}>
              Edit
            </Button>
            <Button onClick={onClose}>Close</Button>
          </Space>
        )
      }
      width={800}
    >
      {renderContent()}
    </Modal>
  )
}
