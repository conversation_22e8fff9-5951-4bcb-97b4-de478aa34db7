import { AgeingReportParams, AuditReportParams, CashFlowParams, CustomerAnalyticsParams, DailyOperationsParams, FinancialOverviewParams, InventoryReportParams, IRequest, SalesReportParams } from '@/common/types';
import { reportsService } from '../services';

class ReportsController {
    async generateFinancialOverview(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const params = req.params as FinancialOverviewParams;
        if (!params.format) {
            throw new Error('Report format is required');
        }
        return await reportsService.generateFinancialOverview(params);
    }

    async generateInventoryValuation(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const params = req.params as InventoryReportParams;
        if (!params.format) {
            throw new Error('Report format is required');
        }
        return await reportsService.generateInventoryValuation(params);
    }

    async generateCashFlow(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const params = req.params as CashFlowParams;
        if (!params.format) {
            throw new Error('Report format is required');
        }
        return await reportsService.generateCashFlow(params);
    }

    async generateSalesPerformance(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const params = req.params as SalesReportParams;
        if (!params.format) {
            throw new Error('Report format is required');
        }
        return await reportsService.generateSalesPerformance(params);
    }

    async generateCustomerAnalytics(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const params = req.params as CustomerAnalyticsParams;
        if (!params.format) {
            throw new Error('Report format is required');
        }
        return await reportsService.generateCustomerAnalytics(params);
    }

    async generateAgeingReport(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const params = req.params as AgeingReportParams;
        if (!params.format) {
            throw new Error('Report format is required');
        }
        if (!params.partyType) {
            throw new Error('Party type is required');
        }
        return await reportsService.generateAgeingReport(params);
    }

    async generateDailyOperations(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const params = req.params as DailyOperationsParams;
        if (!params.format) {
            throw new Error('Report format is required');
        }
        return await reportsService.generateDailyOperations(params);
    }

    async generateAuditReport(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const params = req.params as AuditReportParams;
        if (!params.format) {
            throw new Error('Report format is required');
        }
        return await reportsService.generateAuditReport(params);
    }
}

export const reportsController = new ReportsController();
