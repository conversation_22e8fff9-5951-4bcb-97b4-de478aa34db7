import { prisma } from '../db';
import { CreateBankData, GetAllBanksFilter } from '../../common/types/banks';

// make sure the ui reflects the new changes in here and the types since now banks have pagination

class BankService {
    async createBank(data: CreateBankData) {
        return await prisma.$transaction(async (tx) => {

            if (data.openingBalance && data.openingBalance < 0) {
                throw new Error('Opening balance cannot be negative');
            }

            const bank = await tx.banks.create({
                data: {
                    name: data.name,
                    accountNo: data.accountNo,
                    balance: data.openingBalance ?? 0,
                    createdBy: { connect: { id: data.adminId } }
                }
            });

            // Create opening balance entry in main ledger if provided
            if (data.openingBalance) {
                await tx.ledger.create({
                    data: {
                        bankId: bank.id,
                        amount: data.openingBalance,
                        creditOrDebit: 'CREDIT',
                        description: 'Opening Balance',
                        referenceType: 'OpeningBalance',
                        date: new Date(),
                        status: 'ACTIVE',
                        createdById: data.adminId
                    }
                });
            }

            return bank;
        });
    }

    async getBankById(id: string) {
        const bank = await prisma.banks.findUnique({
            where: { id }
        });

        if (!bank) {
            throw new Error('Bank not found');
        }

        return bank;
    }

    async deleteBank(id: string) {
        return await prisma.$transaction(async (tx) => {
            // First check if bank exists
            const bank = await tx.banks.findUnique({
                where: { id }
            });

            if (!bank) {
                throw new Error('Bank not found');
            }

            // Check for any active transactions except opening balance
            const transaction = await tx.ledger.findFirst({
                where: {
                    bankId: id,
                    status: 'ACTIVE',
                    NOT: { referenceType: 'OpeningBalance' }
                }
            });

            if (transaction) {
                throw new Error('Cannot delete bank with existing transactions');
            }

            // Delete all ledger entries for this bank (including opening balance)
            await tx.ledger.deleteMany({
                where: { bankId: id }
            });

            // Finally delete the bank
            return await tx.banks.delete({
                where: { id }
            });
        });
    }

    async deactivateBank(id: string) {
        const bank = await prisma.banks.findUnique({
            where: { id }
        });

        if (!bank) throw new Error('Bank not found');

        if (bank.balance !== 0) throw new Error('Cannot deactivate bank with a balance');

        if (!bank.isActive) throw new Error('Bank is already inactive');

        return await prisma.banks.update({
            where: { id },
            data: { isActive: false }
        });
    }

    async reactivateBank(id: string) {

        const bank = await prisma.banks.findUnique({
            where: { id }
        });

        if (!bank) throw new Error('Bank not found');

        if (bank.isActive) throw new Error('Bank is already active');

        return await prisma.banks.update({
            where: { id },
            data: { isActive: true }
        });
    }

    async getBanksForSelect() {
        const banks = await prisma.banks.findMany({
            where: { isActive: true },
            select: {
                id: true,
                name: true,
            },
            orderBy: {
                name: 'asc'
            }
        });

        return banks.map(bank => ({
            value: bank.id,
            label: bank.name
        }));
    }

    async getAllBanks(filter: GetAllBanksFilter) {
        const page = filter.page || 1;
        const limit = filter.limit || 10;
        const skip = (page - 1) * limit;

        console.log(filter)

        const where = {
            ...(filter.isActive && { isActive: filter.isActive })
        };

        const [banks, total] = await Promise.all([
            prisma.banks.findMany({
                where,
                select: {
                    id: true,
                    name: true,
                    accountNo: true,
                    balance: true,
                    isActive: true
                },
                orderBy: {
                    name: 'asc'
                },
                skip,
                take: limit
            }),
            prisma.banks.count({ where })
        ]);

        return {
            banks,
            total,
            page,
            totalPages: Math.ceil(total / limit)
        };
    }
}

export const bankService = new BankService();