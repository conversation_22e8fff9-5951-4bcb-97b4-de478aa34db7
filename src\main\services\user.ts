import { prisma } from '../db';
import { hash, compare } from 'bcryptjs';
import { CreateUserData, UpdatePasswordData, ResetPasswordData, GetUsersParams, LoginData } from '@/common/types';

class UserService {
    // Helper method to check admin authorization
    private async checkAdminAuthorization(adminId: string) {
        const admin = await prisma.admin.findUnique({
            where: { id: adminId }
        });

        if (!admin || (admin.role !== 'DEVELOPER' && admin.role !== 'SUPER_ADMIN')) {
            throw new Error('Unauthorized: Only developers and super admins can perform this action');
        }

        return admin;
    }

    async login(data: LoginData) {
        // Find user by username
        const user = await prisma.admin.findUnique({
            where: { username: data.username.trim() }
        });

        if (!user) {
            throw new Error('Invalid username or password');
        }

        // Check if user is active
        if (!user.isActive) {
            throw new Error('Your account has been deactivated. Please contact an administrator.');
        }

        // Verify password
        const isValid = await compare(data.password.trim(), user.password);
        if (!isValid) {
            throw new Error('Invalid username or password');
        }

        // Return user data without sensitive information
        return {
            id: user.id,
            username: user.username,
            name: user.name,
            role: user.role,
            isActive: user.isActive
        };
    }

    async createUser(data: CreateUserData) {
        const existingUser = await prisma.admin.findUnique({
            where: { username: data.username.trim() }
        });

        if (existingUser) {
            throw new Error('Username already exists');
        }

        const hashedPassword = await hash(data.password.trim(), 10);

        return await prisma.admin.create({
            data: {
                ...data,
                username: data.username.trim(),
                password: hashedPassword
            },
            select: {
                id: true,
                username: true,
                name: true,
                role: true,
                isActive: true
            }
        });
    }

    async updatePassword(data: UpdatePasswordData) {
        const user = await prisma.admin.findUnique({
            where: { id: data.userId }
        });

        if (!user) {
            throw new Error('User not found');
        }

        const isValid = await compare(data.currentPassword.trim(), user.password);
        if (!isValid) {
            throw new Error('Current password is incorrect');
        }

        const hashedPassword = await hash(data.newPassword.trim(), 10);

        return await prisma.admin.update({
            where: { id: data.userId },
            data: { password: hashedPassword },
            select: {
                id: true,
                username: true,
                name: true,
                role: true
            }
        });
    }

    async resetPassword(data: ResetPasswordData) {
        const admin = await this.checkAdminAuthorization(data.adminId);

        const hashedPassword = await hash(data.newPassword.trim(), 10);

        return await prisma.admin.update({
            where: { id: data.userId },
            data: { password: hashedPassword },
            select: {
                id: true,
                username: true,
                name: true,
                role: true
            }
        });
    }

    async getUsers({ page, limit, includeInactive = false }: GetUsersParams) {
        const where = includeInactive ? {} : { isActive: true };

        const [users, total] = await Promise.all([
            prisma.admin.findMany({
                where,
                select: {
                    id: true,
                    username: true,
                    name: true,
                    role: true,
                    isActive: true,
                    createdAt: true
                },
                skip: (page - 1) * limit,
                take: limit
            }),
            prisma.admin.count({ where })
        ]);

        return { users, total, page, totalPages: Math.ceil(total / limit) };
    }

    async deactivateUser(userId: string, adminId: string) {
        // Check admin authorization
        await this.checkAdminAuthorization(adminId);

        // Get the user to deactivate
        const userToDeactivate = await prisma.admin.findUnique({
            where: { id: userId }
        });

        if (!userToDeactivate) {
            throw new Error('User not found');
        }

        // Check if trying to deactivate a DEVELOPER role
        if (userToDeactivate.role === 'DEVELOPER') {
            throw new Error('System administrators cannot be deactivated');
        }

        // If deactivating an admin or super admin, check if it's the last one
        if (userToDeactivate.role === 'SUPER_ADMIN') {
            const activeAdminCount = await prisma.admin.count({
                where: {
                    role: 'SUPER_ADMIN',
                    isActive: true,
                    NOT: { id: userId }
                }
            });

            if (activeAdminCount === 0) {
                throw new Error('Cannot deactivate the last active super admin');
            }
        }

        return await prisma.admin.update({
            where: { id: userId },
            data: { isActive: false }
        });
    }

    async activateUser(userId: string, adminId: string) {
        // Check admin authorization
        await this.checkAdminAuthorization(adminId);

        return await prisma.admin.update({
            where: { id: userId },
            data: { isActive: true }
        });
    }

    async deleteUser(userId: string, adminId: string) {
        // Check admin authorization
        await this.checkAdminAuthorization(adminId);

        // Check if user has any entries in the database
        const [createdPurchases, createdSales, createdExpenses, createdPayments, createdLedger] = await Promise.all([
            prisma.purchaseInvoice.count({ where: { createdById: userId } }),
            prisma.saleInvoice.count({ where: { createdById: userId } }),
            prisma.expense.count({ where: { createdById: userId } }),
            prisma.payments.count({ where: { createdById: userId } }),
            prisma.ledger.count({ where: { createdById: userId } })
        ]);

        const totalEntries = createdPurchases + createdSales + createdExpenses + createdPayments + createdLedger;

        if (totalEntries > 0) {
            throw new Error('Cannot delete user with existing entries in the database');
        }

        return await prisma.admin.delete({
            where: { id: userId }
        });
    }
}

export const userService = new UserService();