import { useState } from 'react'
import { Button, Input, Form, Row, Col, Card, Typography, Space, Divider } from 'antd'
import { CreditorList } from './components/CreditorList'
import { CreateCreditorDrawer } from './components/CreateCreditorDrawer'
import { CreditorDetailsModal } from './components/CreditorDetailsModal'
import { SearchOutlined, PlusOutlined, UserOutlined } from '@ant-design/icons'
import { PartyListPdfButton } from '@/renderer/components/partyListPdfButton'
import { PartyType } from '@/common/types/party'
import './Creditors.scss'
import { useTheme } from '@/renderer/contexts'

const { Title } = Typography

const Creditors = () => {
  const [isCreateDrawerOpen, setIsCreateDrawerOpen] = useState(false)
  const [selectedCreditor, setSelectedCreditor] = useState<string | null>(null)
  const [searchQuery, setSearchQuery] = useState('')
  const [refreshTrigger, setRefreshTrigger] = useState(0)
  const { isDarkMode } = useTheme()

  const handleCreateCreditor = () => {
    setIsCreateDrawerOpen(true)
  }

  const handleCreditorClick = (creditorId: string) => {
    setSelectedCreditor(creditorId)
  }

  const handleCreditorCreated = () => {
    setRefreshTrigger((prev) => prev + 1)
    setIsCreateDrawerOpen(false)
  }

  return (
    <div className="creditors-container">
      <Card
        className={`bg-[length:200%_200%] bg-[center] shadow-lg ${
          isDarkMode
            ? 'bg-gradient-to-bl from-indigo-950 via-black to-indigo-900 shadow-indigo-950'
            : 'bg-gradient-to-bl from-indigo-200 via-white to-indigo-100 shadow-indigo-200'
        }`}
      >
        <Space wrap className="flex justify-between gap-4">
          <Title level={4}>
            <UserOutlined /> Creditors Management
          </Title>

          <Space wrap className="gap-4">
            <Form.Item className="search-input" style={{ marginBottom: 0 }}>
              <Input
                placeholder="Search creditors..."
                prefix={<SearchOutlined />}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </Form.Item>

            <PartyListPdfButton partyType={PartyType.CREDITOR} search={searchQuery} />

            <Button type="primary" icon={<PlusOutlined />} onClick={handleCreateCreditor}>
              Add Creditor
            </Button>
          </Space>
        </Space>

        <Divider />

        <CreditorList
          onCreditorClick={handleCreditorClick}
          searchQuery={searchQuery}
          refreshTrigger={refreshTrigger}
        />
      </Card>

      <CreateCreditorDrawer
        open={isCreateDrawerOpen}
        onClose={() => setIsCreateDrawerOpen(false)}
        onCreditorCreated={handleCreditorCreated}
      />

      <CreditorDetailsModal
        creditorId={selectedCreditor}
        open={!!selectedCreditor}
        onClose={() => setSelectedCreditor(null)}
      />
    </div>
  )
}

export default Creditors
