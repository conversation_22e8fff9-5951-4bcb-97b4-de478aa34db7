/*
  Warnings:

  - Added the required column `createdById` to the `Banks` table without a default value. This is not possible if the table is not empty.

*/
-- AlterEnum
-- This migration adds more than one value to an enum.
-- With PostgreSQL versions 11 and earlier, this is not possible
-- in a single migration. This can be worked around by creating
-- multiple migrations, each migration adding only one value to
-- the enum.


ALTER TYPE "AdminRole" ADD VALUE 'DEVELOPER';
ALTER TYPE "AdminRole" ADD VALUE 'STAFF';

-- AlterEnum
-- This migration adds more than one value to an enum.
-- With PostgreSQL versions 11 and earlier, this is not possible
-- in a single migration. This can be worked around by creating
-- multiple migrations, each migration adding only one value to
-- the enum.


ALTER TYPE "CashLocation" ADD VALUE 'ACCOUNT';
ALTER TYPE "CashLocation" ADD VALUE 'OTHER';

-- AlterEnum
ALTER TYPE "LedgerType" ADD VALUE 'Account_Transfer';

-- AlterTable
ALTER TABLE "Banks" ADD COLUMN     "createdById" TEXT NOT NULL,
ADD COLUMN     "isActive" BOOLEAN NOT NULL DEFAULT true,
ADD COLUMN     "voidedAt" TIMESTAMP(3),
ADD COLUMN     "voidedById" TEXT,
ADD COLUMN     "voidingReason" TEXT;

-- CreateIndex
CREATE INDEX "Stock_productId_status_idx" ON "Stock"("productId", "status");

-- AddForeignKey
ALTER TABLE "Banks" ADD CONSTRAINT "Banks_createdById_fkey" FOREIGN KEY ("createdById") REFERENCES "Admin"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Banks" ADD CONSTRAINT "Banks_voidedById_fkey" FOREIGN KEY ("voidedById") REFERENCES "Admin"("id") ON DELETE SET NULL ON UPDATE CASCADE;
