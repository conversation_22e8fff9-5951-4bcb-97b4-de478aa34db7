import { IRequest } from '../../common';
import { stockService } from '../services';
import { StockStatus } from '@prisma/client';

class StockController {
    async getAvailableStock(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { page = 1, limit = 10, search, where } = req.query ?? {};
        return await stockService.getAvailableStock({ page, limit, search, where });
    }

    async getSoldStock(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { page = 1, limit = 10, search, where } = req.query ?? {};
        return await stockService.getSoldStock({ page, limit, search, where });
    }

    async getStockByProduct(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { productId } = req.params ?? {};
        const { page = 1, limit = 10, status } = req.query ?? {};

        if (!productId) throw new Error("Product ID is required");

        return await stockService.getStockByProduct(productId, { page, limit, status });
    }

    async getStockByVendor(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { vendorId } = req.params ?? {};
        const { page = 1, limit = 10, status } = req.query ?? {};

        if (!vendorId) throw new Error("Vendor ID is required");

        return await stockService.getStockByVendor(vendorId, { page, limit, status });
    }

    async getStockByPurchaseInvoice(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { purchaseInvoiceId } = req.params ?? {};
        const { page = 1, limit = 10, status } = req.query ?? {};

        if (!purchaseInvoiceId) throw new Error("Purchase Invoice ID is required");

        return await stockService.getStockByPurchaseInvoice(purchaseInvoiceId, { page, limit, status });
    }
}

export const stockController = new StockController();