import { Select, Space, App } from 'antd'
import { useDispatch, useSelector } from 'react-redux'
import { IRootState } from '@/renderer/redux'
import { draftInvoiceActions } from '@/renderer/redux'
import { DeleteOutlined } from '@ant-design/icons'
import { useState } from 'react'

interface Props {
  type: 'WALK_IN' | 'REGISTERED'
  onSelect: (draftName: string) => void
  disabled?: boolean
}

export const DraftSelector = ({ type, onSelect, disabled }: Props) => {
  const dispatch = useDispatch()
  const { message } = App.useApp()
  const [selectedDraft, setSelectedDraft] = useState<string | null>(null)

  const drafts = useSelector((state: IRootState) =>
    type === 'WALK_IN' ? state.draftInvoice.walkInDrafts : state.draftInvoice.registeredDrafts
  )

  const handleDelete = (draftName: string, e: React.MouseEvent) => {
    e.stopPropagation()
    try {
      if (type === 'WALK_IN') {
        dispatch(draftInvoiceActions.removeWalkInDraft(draftName))
      } else {
        dispatch(draftInvoiceActions.removeRegisteredDraft(draftName))
      }
      message.success('Draft deleted successfully')
    } catch (error: any) {
      message.error(error.message || 'Failed to delete draft')
    }
  }

  const handleSelect = (value: string) => {
    onSelect(value)
    setSelectedDraft(null) // Reset selection after loading
  }

  return (
    <Space>
      <Select
        disabled={disabled}
        placeholder="Select a draft invoice"
        style={{ width: 250 }}
        onChange={handleSelect}
        value={selectedDraft}
        options={drafts.map((draft) => ({
          label: (
            <Space className="w-full justify-between">
              <span>{draft.name}</span>
              <DeleteOutlined
                className="text-red-500 hover:text-red-600"
                onClick={(e) => handleDelete(draft.name, e)}
              />
            </Space>
          ),
          value: draft.name
        }))}
        optionRender={(option) => option.label}
        showSearch
        allowClear
        filterOption={(input, option) =>
          (option?.label as any)?.props?.children[0]?.props?.children
            .toLowerCase()
            .includes(input.toLowerCase())
        }
      />
    </Space>
  )
}
