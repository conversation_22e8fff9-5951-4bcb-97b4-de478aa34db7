import { Form, Input, Drawer, Button, message } from 'antd'
import { categoryApi } from '@/renderer/services'

interface CategoryDrawerProps {
  open: boolean
  onClose: () => void
  onCategoryCreated: () => void
}

export const CreateCategoryDrawer = ({ open, onClose, onCategoryCreated }: CategoryDrawerProps) => {
  const [form] = Form.useForm()
  const handleSubmit = async (values: any) => {
    const response = await categoryApi.createCategory(values)

    if (response.error.error || response.data.error) {
      message.error(response.error.message || response.data.error.message)
      return
    }

    message.success('Category created successfully')
    form.resetFields()
    onCategoryCreated()
    onClose()
  }
  return (
    <Drawer
      title="Create New Category"
      placement="right"
      onClose={onClose}
      open={open}
      width={400}
      extra={
        <Button
          className="bg-green-500 hover:!bg-green-600"
          type="primary"
          onClick={() => form.submit()}
        >
          Create
        </Button>
      }
    >
      <Form form={form} layout="vertical" onFinish={handleSubmit}>
        <Form.Item
          name="name"
          label="Category Name"
          rules={[{ required: true, message: 'Please enter category name' }]}
        >
          <Input />
        </Form.Item>
      </Form>
    </Drawer>
  )
}
