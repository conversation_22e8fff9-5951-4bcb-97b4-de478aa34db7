import jsPDF from 'jspdf'
import 'jspdf-autotable'
import dayjs from 'dayjs'
import { ProductQuantityReportResponse } from '@/common/types/product'

export const generateProductQuantityPDF = async (
    data: ProductQuantityReportResponse,
    shouldSave: boolean = false
): Promise<jsPDF | void> => {
    try {
        // Create a new PDF document
        const doc = new jsPDF({
            orientation: 'portrait',
            unit: 'mm',
            format: 'a4'
        })

        // Set up the document
        const pageWidth = doc.internal.pageSize.width
        const pageHeight = doc.internal.pageSize.height
        const margin = 10

        // Add header only to the first page
        // Add company name
        doc.setFont('helvetica', 'bold')
        doc.setFontSize(16)
        doc.text('SJ LACE', pageWidth / 2, 15, { align: 'center' })

        // Add report title
        doc.setFontSize(12)
        doc.text('Product Inventory Report', pageWidth / 2, 22, { align: 'center' })

        // Add date
        doc.setFontSize(10)
        doc.text(`Date: ${dayjs().format('DD/MM/YYYY')}`, pageWidth - margin, 15, { align: 'right' })

        // Add page number for first page
        doc.text(`Page 1`, pageWidth - margin, 20, { align: 'right' })

        // Add summary information
        doc.setFontSize(9)
        doc.text(`Total Products: ${data.summary.totalProducts}`, margin, 30)
        doc.text(`Total Value: Rs. ${data.summary.totalValue.toLocaleString('en-US', { minimumFractionDigits: 0, maximumFractionDigits: 2 })}`, margin, 35)
        doc.text(`Low Stock Items: ${data.summary.lowStockItems}`, pageWidth - margin - 50, 30, { align: 'left' })
        doc.text(`Out of Stock Items: ${data.summary.outOfStockItems}`, pageWidth - margin - 50, 35, { align: 'left' })

        // Starting Y position for table
        let startY = 45

        // Configure the table
        const tableColumns = [
            { header: 'Sr.', dataKey: 'serialNumber' },
            { header: 'Product ID', dataKey: 'productId' },
            { header: 'Name', dataKey: 'name' },
            { header: 'Category', dataKey: 'category' },
            { header: 'Qty', dataKey: 'quantity' },
            { header: 'Min Level', dataKey: 'minLevel' },
            { header: 'Sale Price', dataKey: 'salePrice' },
            { header: 'Avg. Cost', dataKey: 'avgCost' },
            { header: 'Value', dataKey: 'value' }
        ]

        const tableRows = data.products.map((product, index) => ({
            serialNumber: index + 1,
            productId: product.productId,
            name: `${product.name} ${product.tag ? `(${product.tag})` : ''} ${product.nature ? `[${product.nature}]` : ''}`,
            category: product.category.name,
            quantity: product.quantityInStock,
            minLevel: product.minStockLevel || '-',
            salePrice: product.salePrice.toLocaleString('en-US', { minimumFractionDigits: 0, maximumFractionDigits: 2 }),
            avgCost: product.averagePurchasePrice.toLocaleString('en-US', { minimumFractionDigits: 0, maximumFractionDigits: 2 }),
            value: product.purchaseValue.toLocaleString('en-US', { minimumFractionDigits: 0, maximumFractionDigits: 2 })
        }))

        // @ts-ignore (jspdf-autotable types are not properly recognized)
        doc.autoTable({
            columns: tableColumns,
            body: tableRows,
            startY,
            margin: { left: margin, right: margin },
            theme: 'grid',
            styles: {
                fontSize: 8,
                cellPadding: 1,
                lineColor: [0, 0, 0],
                lineWidth: 0.1,
                fontStyle: 'bold'
            },
            headStyles: {
                fillColor: [0, 0, 0],
                textColor: [255, 255, 255],
                fontStyle: 'bold',
                lineColor: [0, 0, 0],
                lineWidth: 0.1
            },
            columnStyles: {
                serialNumber: { cellWidth: 10, halign: 'center', textColor: [0, 0, 0] },
                productId: { cellWidth: 20, textColor: [0, 0, 0] },
                name: { cellWidth: 40, textColor: [0, 0, 0] },
                category: { cellWidth: 25, textColor: [0, 0, 0] },
                quantity: { cellWidth: 15, halign: 'right', textColor: [0, 0, 0] },
                minLevel: { cellWidth: 15, halign: 'right', textColor: [0, 0, 0] },
                salePrice: { cellWidth: 20, halign: 'right', textColor: [0, 0, 0] },
                avgCost: { cellWidth: 20, halign: 'right', textColor: [0, 0, 0] },
                value: { cellWidth: 25, halign: 'right', textColor: [0, 0, 0] }
            },
            didDrawPage: (data: any) => {
                // Add page number to each page (except first page which already has it)
                if (data.pageCount > 1) {
                    doc.setFontSize(10)
                    doc.text(`Page ${data.pageCount}`, pageWidth - margin, 20, { align: 'right' })
                }
            }
        })

        if (shouldSave) {
            // Save the PDF with a more readable filename format
            const date = dayjs().format('YYYY,MM,DD')
            const time = dayjs().format('hh,mm A')
            doc.save(`Product_Inventory_Date_${date}_Time_${time}.pdf`)
            return
        }

        return doc
    } catch (error) {
        console.error('Error generating product quantity PDF:', error)
        throw error
    }
}
