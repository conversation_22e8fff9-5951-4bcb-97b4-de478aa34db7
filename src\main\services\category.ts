import { prisma } from '../db';
import { Prisma } from '@prisma/client';

interface CreateCategoryData {
    name: string;
}

class CategoryService {

    async generatePrefix(name: string): Promise<string> {
        // Split by hyphen and generate prefix
        const parts = name.split('-').map(part => part.trim()); // Split by hyphen and trim spaces

        // POTENTIAL ISSUE: For single-word category names (without hyphens), this will generate
        // a single-character prefix, which might not be distinctive enough.
        // Consider using the first 2-3 characters for single words, or a different approach.
        const prefix = parts.map(part =>
            part.charAt(0).toUpperCase() // Get the first character of each part
        ).join('');

        // Check if prefix already exists in the database
        const existingCategory = await prisma.category.findFirst({
            where: { prefix }
        });

        if (existingCategory) {
            throw new Error(`Category prefix '${prefix}' already exists. Please choose a different name pattern.`);
        }
        return prefix;
    }

    async createCategory(data: CreateCategoryData) {
        const existingCategory = await prisma.category.findUnique({
            where: { name: data.name }
        });
        if (existingCategory) {
            throw new Error('Category with this name already exists');
        }
        const prefix = await this.generatePrefix(data.name);
        return await prisma.category.create({
            data: {
                name: data.name,
                prefix
            }
        });
    }

    async getAllCategories() {
        return await prisma.category.findMany({
            orderBy: { name: 'asc' }
        });
    }

    async getCategoriesForSelect() {
        const categories = await prisma.category.findMany({
            select: {
                id: true,
                name: true,
                productCount: true
            },
            orderBy: { name: 'asc' }
        });
        return categories.map(category => ({
            value: category.id,
            label: `${category.name} (${category.productCount} products)`
        }));
    }

    async generateProductId(categoryId: string) {
        const category = await prisma.category.findUnique({
            where: { id: categoryId }
        });

        if (!category) {
            throw new Error('Category not found');
        }

        // POTENTIAL ISSUE: If the sequence number gets very large, the product IDs will become
        // increasingly long, which could cause issues with display or storage.
        // Example: If a category has 10,000+ products, the IDs will be like "ABC10001", "ABC10002", etc.
        // Consider implementing a rolling sequence that resets periodically or has a maximum length.
        return `${category.prefix}${category.sequence + 1}`;
    }

    async deleteCategory(id: string) {
        try {
            // Check if category has products before attempting to delete
            const categoryWithProducts = await prisma.category.findUnique({
                where: { id },
                include: { _count: { select: { products: true } } }
            });

            if (!categoryWithProducts) {
                throw new Error('Category not found');
            }

            if (categoryWithProducts._count.products > 0) {
                throw new Error('Cannot delete category because it has products');
            }

            return await prisma.category.delete({
                where: { id }
            });
        } catch (error) {
            if (error instanceof Prisma.PrismaClientKnownRequestError) {
                if (error.code === 'P2003') {
                    throw new Error('Cannot delete category because it has products');
                }
            }
            throw error;
        }
    }
}

export const categoryService = new CategoryService();