import { configureStore, combineReducers } from '@reduxjs/toolkit';
import storage from 'redux-persist/lib/storage';
import {
  persistStore,
  persistReducer,
  FLUSH,
  REHYDRATE,
  PAUSE,
  PERSIST,
  PURGE,
  REGISTER,
} from 'redux-persist';
import { userReducer, draftInvoiceReducer, userActions } from './slices';

const persistConfig = {
  key: 'root',
  storage,
  whitelist: ['user'],
  blacklist: ['license']
};

const rootReducer = combineReducers({
  user: userReducer,
  draftInvoice: draftInvoiceReducer,
});

const persistedReducer = persistReducer(persistConfig, rootReducer);

export const store = configureStore({
  reducer: persistedReducer,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: [FLUSH, REHYDRATE, PAUSE, PERSIST, PURGE, REGISTER],
      },
    }),
});

export const persistor = persistStore(store);

export type IRootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;

// Clear license on app start
store.dispatch(userActions.initLicense(null));

// Listen for app closing event
window.electron?.ipcRenderer.on('app-closing', () => {
  // Clear the license first
  store.dispatch(userActions.initLicense(null));
  // Then clear persisted state
  persistor.purge();
  // Finally logout
  store.dispatch({ type: 'user/logout' });
});

export * from "./slices";
