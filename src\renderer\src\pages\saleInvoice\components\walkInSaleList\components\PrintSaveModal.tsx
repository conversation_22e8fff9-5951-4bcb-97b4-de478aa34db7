import { Modal, Space, Button } from 'antd'
import { PrinterOutlined, SaveOutlined } from '@ant-design/icons'
import { useTheme } from '@/renderer/contexts'

interface Props {
  open: boolean
  loading: boolean
  onSave: () => void
  onPrint: () => void
  onCancel: () => void
}

const PrintSaveModal = ({ open, loading, onSave, onPrint, onCancel }: Props) => {
  const { isDarkMode } = useTheme()

  return (
    <Modal title="Print or Save Invoice" open={open} onCancel={onCancel} footer={null}>
      <div className="flex flex-col gap-4">
        <p>Would you like to print or save this invoice?</p>
        <Space className="justify-end">
          <Button icon={<PrinterOutlined />} onClick={onPrint} loading={loading} type="primary">
            Print
          </Button>
          <Button icon={<SaveOutlined />} onClick={onSave} loading={loading}>
            Save
          </Button>
          <Button onClick={onCancel}>Cancel</Button>
        </Space>
      </div>
    </Modal>
  )
}

export default PrintSaveModal
