import { CustomerType, PaymentMethod, PartyType } from '@prisma/client'

export interface DailyOverview {
    totalAmount: number
    totalInvoices: number
    averageInvoiceValue: number
    comparisonWithPrevious: {
        amount: number
        invoices: number
        average: number
    }
}

export interface TimelineData {
    date: string
    totalAmount: number
    totalInvoices: number
}

export interface TopEntity {
    id: string
    name: string
    amount: number
    count: number
}

export interface ProductWithStock {
    id: string
    name: string
    productId: string
    quantityInStock: number
    minStockLevel: number
    category: {
        name: string
    }
}

export interface StockValueOverview {
    purchase: {
        totalValue: number
        averageValue: number
    }
    sale: {
        totalValue: number
        averageValue: number
    }
    totalProducts: number
    lowStockCount: number
}

export interface CashFlowOverview {
    bank: {
        totalBalance: number
        lastDayChange: number
    }
    vault: {
        balance: number
        lastDayChange: number
    }
    counter: {
        balance: number
        lastDayChange: number
    }
}

export interface AccountsOverview {
    totalAmount: number
    overdueAmount: number
    upcomingAmount: number
    aging: {
        thirtyDays: number
        sixtyDays: number
        ninetyDays: number
        moreDays: number
    }
}

export interface PartyOverview {
    customers: {
        total: number
        active: number
        newThisMonth: number
    }
    vendors: {
        total: number
        active: number
        newThisMonth: number
    }
    creditors: {
        total: number
        active: number
        newThisMonth: number
    }
}

export interface SalesDistribution {
    walkIn: {
        amount: number
        count: number
    }
    registered: {
        amount: number
        count: number
    }
}

export interface PaymentMethodDistribution {
    method: PaymentMethod
    amount: number
    count: number
}

export interface TimeRange {
    days: 1 | 7 | 14 | 30
}
