import { Button, Card, Space, Statistic, Typography } from 'antd'
import { useState } from 'react'
import { FaMoneyBillTransfer, FaScaleBalanced } from 'react-icons/fa6'
import { InitializeDrawer, ReconcileDrawer } from '.'
import { useTheme } from '@/renderer/contexts'

const { Title } = Typography

interface CashVaultHeaderProps {
  balance: number
  isInitialized: boolean
  onBalanceUpdate: () => void
  userId: string
}

export const CashVaultHeader = ({
  balance,
  isInitialized,
  onBalanceUpdate,
  userId
}: CashVaultHeaderProps) => {
  // Local state for drawers
  // const [depositDrawerOpen, setDepositDrawerOpen] = useState(false)
  // const [withdrawDrawerOpen, setWithdrawDrawerOpen] = useState(false)
  const [initializeDrawerOpen, setInitializeDrawerOpen] = useState(false)
  const [reconcileDrawerOpen, setReconcileDrawerOpen] = useState(false)

  const { isDarkMode } = useTheme()

  return (
    <Card
      size="small"
      className={`mb-2 bg-[length:200%_200%] bg-[center] shadow-lg ${
        isDarkMode
          ? 'bg-gradient-to-bl from-indigo-950 via-black to-indigo-900 shadow-indigo-950'
          : 'bg-gradient-to-bl from-indigo-200 via-white to-indigo-100 shadow-indigo-200'
      }`}
    >
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-6">
          <Title level={2} className="!mb-0">
            Cash Vault
          </Title>
          {isInitialized && (
            <Statistic
              title="Current Balance"
              value={balance}
              precision={2}
              prefix="Rs. "
              valueStyle={{ color: balance >= 0 ? '#3f8600' : '#cf1322' }}
            />
          )}
        </div>

        {isInitialized ? (
          <Button icon={<FaScaleBalanced />} onClick={() => setReconcileDrawerOpen(true)}>
            Reconcile
          </Button>
        ) : (
          <Button
            type="primary"
            icon={<FaMoneyBillTransfer />}
            onClick={() => setInitializeDrawerOpen(true)}
          >
            Initialize Vault
          </Button>
        )}
      </div>

      <InitializeDrawer
        open={initializeDrawerOpen}
        onClose={() => setInitializeDrawerOpen(false)}
        onBalanceUpdate={onBalanceUpdate}
        userId={userId}
      />

      <ReconcileDrawer open={reconcileDrawerOpen} onClose={() => setReconcileDrawerOpen(false)} />
    </Card>
  )
}
