import { useState } from 'react'
import { Card } from 'antd'
import { ManualEntryList } from './components/ManualEntryList'
import { CreateManualEntryDrawer } from './components/CreateManualEntryDrawer'
import { ManualEntryFilters } from './components/ManualEntryFilters'
import { ManualEntryStatus, ManualEntrySortOrder } from '@/renderer/services/manualEntry'
import type { ManualEntryFilters as FilterType } from '@/common/types/manualEntry'

const ManualEntry = () => {
  const [createDrawerOpen, setCreateDrawerOpen] = useState(false)
  const [refreshTrigger, setRefreshTrigger] = useState(0)
  const [filters, setFilters] = useState<FilterType>({
    status: ManualEntryStatus.ACTIVE,
    sortOrder: ManualEntrySortOrder.OLDEST_FIRST
  })

  const handleCreateSuccess = () => {
    setCreateDrawerOpen(false)
    setRefreshTrigger((prev) => prev + 1)
  }

  const handleFiltersChange = (newFilters: FilterType) => {
    setFilters(newFilters)
    setRefreshTrigger((prev) => prev + 1)
  }

  return (
    <div className="p-6">
      <div className="mb-6">
        <Card>
          <ManualEntryFilters
            filters={filters}
            onFiltersChange={handleFiltersChange}
            onCreateClick={() => setCreateDrawerOpen(true)}
          />
        </Card>
      </div>

      <Card>
        <ManualEntryList
          filters={filters}
          refreshTrigger={refreshTrigger}
          onRefresh={() => setRefreshTrigger((prev) => prev + 1)}
        />
      </Card>

      <CreateManualEntryDrawer
        open={createDrawerOpen}
        onClose={() => setCreateDrawerOpen(false)}
        onSuccess={handleCreateSuccess}
      />
    </div>
  )
}

export default ManualEntry
