import { useEffect, useState } from 'react'
import {
  Table,
  Card,
  DatePicker,
  Space,
  Button,
  Select,
  App,
  Typography,
  Modal,
  Input,
  Tooltip,
  Tag,
  Form,
  Avatar,
  Row,
  Col
} from 'antd'
import {
  FaSync,
  FaBoxOpen,
  FaTrash,
  FaUserAlt,
  FaShoppingBag,
  FaPrint,
  FaFilter
} from 'react-icons/fa'
import { FiEye } from 'react-icons/fi'
import type { ColumnsType } from 'antd/es/table'
import dayjs from 'dayjs'
import { stockReturnApi } from '@/renderer/services'
import { StockReturnStatus, StockReturnSortOrder } from '@/common/types/stockReturn'
import { useSelector } from 'react-redux'
import { IRootState } from '@/renderer/redux'
import { formatCurrency } from '@/renderer/utils'
import { StockReturnResponse } from '@/common/types/stockReturn'
import { usePartyContext } from '@/renderer/contexts/PartyContext'
import { useProductContext } from '@/renderer/contexts/ProductContext'

import {
  BarcodeOutlined,
  TagsOutlined,
  InfoCircleOutlined,
  EyeOutlined,
  PrinterOutlined,
  ClearOutlined
} from '@ant-design/icons'
import StockReturnDetails from './StockReturnDetails'
import PrintSaveModal from './PrintSaveModal'
import { generateStockReturnPDF } from '../utils/generateStockReturnPDf'

const { RangePicker } = DatePicker
const { Text } = Typography

enum Status {
  ACTIVE = 'ACTIVE',
  VOID = 'VOID'
}

interface ReturnedStock {
  id: string
  invoiceNumber: string
  originalInvoiceNumber: string
  date: string
  status: Status
  totalAmount: number
  customer: {
    id: string
    name: string
    phoneNumber: string | null
  } | null
  customerType: string
  type?: string
  items: Array<{
    id: string
    quantity: number
    purchasePrice: number
    product: {
      name: string
      productId: string
      tag?: string
      nature?: string
      category: {
        name: string
      }
    }
  }>
  createdBy: {
    name: string
  }
  voidingReason?: string
}

interface ReturnStockListProps {
  refreshTrigger: number
}

const ReturnStockList = ({ refreshTrigger }: ReturnStockListProps) => {
  const { message } = App.useApp()
  const { customers } = usePartyContext()
  const { products } = useProductContext()
  const [loading, setLoading] = useState(false)
  const [returnedStock, setReturnedStock] = useState<ReturnedStock[]>([])

  // Filter states
  const [dateRange, setDateRange] = useState<[dayjs.Dayjs, dayjs.Dayjs] | null>(null)
  const [status, setStatus] = useState<StockReturnStatus>(StockReturnStatus.ALL)
  const [sortOrder, setSortOrder] = useState<StockReturnSortOrder>(
    StockReturnSortOrder.NEWEST_FIRST
  )
  const [customerId, setCustomerId] = useState<string | null>(null)
  const [productId, setProductId] = useState<string | null>(null)
  const [search, setSearch] = useState<string>('')

  // Modal states
  const [voidModalVisible, setVoidModalVisible] = useState(false)
  const [selectedReturnId, setSelectedReturnId] = useState<string | null>(null)
  const [voidReason, setVoidReason] = useState('')
  const [detailsModalVisible, setDetailsModalVisible] = useState(false)
  const [selectedStockReturn, setSelectedStockReturn] = useState<ReturnedStock | null>(null)
  const [printSaveModalVisible, setPrintSaveModalVisible] = useState(false)
  const [printSaveLoading, setPrintSaveLoading] = useState(false)

  // Pagination
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0
  })

  const user = useSelector((state: IRootState) => state.user.data)

  const handleVoidClick = (returnId: string) => {
    setSelectedReturnId(returnId)
    setVoidModalVisible(true)
  }

  const handleVoidCancel = () => {
    setVoidModalVisible(false)
    setSelectedReturnId(null)
    setVoidReason('')
  }

  const handleViewDetails = (stockReturn: ReturnedStock) => {
    setSelectedStockReturn(stockReturn)
    setDetailsModalVisible(true)
  }

  const handleCloseDetails = () => {
    setDetailsModalVisible(false)
    setSelectedStockReturn(null)
  }

  const handlePrintSaveClick = (stockReturn: ReturnedStock) => {
    setSelectedStockReturn(stockReturn)
    setPrintSaveModalVisible(true)
  }

  const handlePrintSaveCancel = () => {
    setPrintSaveModalVisible(false)
    setSelectedStockReturn(null)
    setPrintSaveLoading(false)
  }

  const handlePrint = async () => {
    try {
      if (!selectedStockReturn) {
        message.error('No stock return selected')
        return
      }

      setPrintSaveLoading(true)
      const doc = await generateStockReturnPDF(selectedStockReturn)
      if (doc) {
        // Get PDF as base64 data
        const pdfData = doc.output('dataurlstring', { filename: 'stockreturn.pdf' })

        // Send to main process for printing
        const success = await window.electron.ipcRenderer.invoke('print-pdf', pdfData)

        if (success) {
          message.success('Stock return document sent to printer')
          setPrintSaveModalVisible(false)
          setSelectedStockReturn(null)
        }
        setPrintSaveLoading(false)
      } else {
        setPrintSaveLoading(false)
      }
    } catch (error) {
      console.error('Error printing stock return:', error)
      message.error('Failed to print stock return document')
      setPrintSaveLoading(false)
    }
  }

  const handleSave = async () => {
    try {
      if (!selectedStockReturn) {
        message.error('No stock return selected')
        return
      }

      setPrintSaveLoading(true)
      const doc = await generateStockReturnPDF(selectedStockReturn, true)
      if (doc) {
        message.success('Stock return PDF generated successfully')
      }
      setPrintSaveLoading(false)
      setPrintSaveModalVisible(false)
      setSelectedStockReturn(null)
    } catch (error) {
      console.error('Error generating PDF:', error)
      message.error('Failed to generate PDF')
      setPrintSaveLoading(false)
    }
  }

  const handleVoidConfirm = async () => {
    if (!selectedReturnId || !voidReason.trim()) {
      message.error('Please provide a reason for voiding')
      return
    }

    setLoading(true)
    const response = await stockReturnApi.voidReturnStock({
      returnInvoiceId: selectedReturnId,
      adminId: user?.id || '',
      reason: voidReason
    })
    setLoading(false)

    if (response.error.error || response.data.error) {
      message.error(response.error.message || response.data.error.message)
      return
    }

    message.success('Return stock voided successfully')
    handleVoidCancel()
    fetchReturnedStock()
  }

  const columns = [
    {
      title: 'Sr.',
      dataIndex: 'index',
      key: 'index',
      width: 60,
      render: (_, __, index) => (pagination.current - 1) * pagination.pageSize + index + 1
    },
    {
      title: 'Invoice',
      dataIndex: 'invoiceNumber',
      key: 'invoiceNumber',
      width: 160,
      render: (invoiceNumber: string, record: ReturnedStock) => (
        <Tooltip
          title={
            record.type === 'LEGACY_STOCK_RETURN'
              ? 'Legacy Return (No Original Invoice)'
              : `Original Invoice: ${record.originalInvoiceNumber}`
          }
        >
          <span>
            {invoiceNumber}
            {record.type === 'LEGACY_STOCK_RETURN' && (
              <Tag color="purple" className="ml-1" style={{ fontSize: '10px' }}>
                LEGACY
              </Tag>
            )}
          </span>
        </Tooltip>
      )
    },
    {
      title: 'Date',
      dataIndex: 'date',
      key: 'date',
      width: 150,
      render: (date: string) => dayjs(date).format('YYYY-MM-DD HH:mm')
    },
    {
      title: 'Customer',
      key: 'customer',
      width: 180,
      render: (_, record: ReturnedStock) => (
        <Space>
          <Avatar
            icon={<FaUserAlt />}
            size="small"
            style={{
              backgroundColor: record.customerType === 'REGISTERED' ? '#4338ca' : '#6d28d9'
            }}
          />
          <Tooltip
            title={
              <>
                <p>
                  <strong>Type:</strong> {record.customerType}
                </p>
                {record.customer && (
                  <>
                    <p>
                      <strong>ID:</strong> {record.customer.id}
                    </p>
                    {record.customer.phoneNumber && (
                      <p>
                        <strong>Phone:</strong> {record.customer.phoneNumber}
                      </p>
                    )}
                  </>
                )}
              </>
            }
          >
            <span>
              {record.customer ? record.customer.name : 'Walk-in Customer'}
              <InfoCircleOutlined className="ml-1 text-gray-400" />
            </span>
          </Tooltip>
        </Space>
      )
    },
    {
      title: 'Amount',
      dataIndex: 'totalAmount',
      key: 'totalAmount',
      width: 120,
      render: (amount: number) => (
        <Text strong className="text-indigo-600">
          {formatCurrency(amount)}
        </Text>
      )
    },
    {
      title: 'Created By',
      dataIndex: ['createdBy', 'name'],
      key: 'createdBy',
      width: 150
    },
    {
      title: 'Info',
      key: 'info',
      width: 120,
      render: (_, record: ReturnedStock) => (
        <Space size="small" className="gap-0">
          <Tag color={record.status === Status.ACTIVE ? 'green' : 'red'}>{record.status}</Tag>
          <Tooltip title={`${record.items.length} items returned`}>
            <Tag icon={<FaShoppingBag />}>{record.items.length}</Tag>
          </Tooltip>
          {record.type === 'LEGACY_STOCK_RETURN' && (
            <Tooltip title="Legacy Return (Pre-system purchase)">
              <Tag color="blue">L</Tag>
            </Tooltip>
          )}
        </Space>
      )
    },
    {
      title: 'Actions',
      key: 'actions',
      width: 150,
      render: (_, record: ReturnedStock) => (
        <Space>
          <Tooltip title="View Details">
            <Button icon={<EyeOutlined />} onClick={() => handleViewDetails(record)} size="small" />
          </Tooltip>

          <Tooltip title="Print/Save">
            <Button
              icon={<PrinterOutlined />}
              onClick={() => handlePrintSaveClick(record)}
              size="small"
            />
          </Tooltip>

          {record.status === Status.ACTIVE ? (
            <Tooltip title="Void Return">
              <Button
                danger
                icon={<FaTrash />}
                onClick={() => handleVoidClick(record.id)}
                size="small"
              />
            </Tooltip>
          ) : null}
        </Space>
      )
    }
  ]

  const fetchReturnedStock = async () => {
    setLoading(true)
    const params = {
      page: pagination.current,
      limit: pagination.pageSize,
      ...(dateRange && {
        startDate: dateRange[0].toDate(),
        endDate: dateRange[1].toDate()
      }),

      ...(status !== StockReturnStatus.ALL && { status }),
      ...(sortOrder !== StockReturnSortOrder.NEWEST_FIRST && { sortOrder }),
      ...(customerId && { customerId }),
      ...(productId && { productId }),
      ...(search.trim() && { search: search.trim() })
    }

    const response = await stockReturnApi.getStockReturn(params)
    setLoading(false)

    if (response.error.error || response.data.error) {
      message.error(response.error.message || response.data.error.message)
      return
    }

    const { data, total, page } = response.data.data as StockReturnResponse
    setReturnedStock(data as unknown as ReturnedStock[])
    setPagination({
      ...pagination,
      current: page,
      total
    })
  }

  const handleTableChange = (newPagination: any) => {
    setPagination({
      ...pagination,
      current: newPagination.current,
      pageSize: newPagination.pageSize
    })
  }

  useEffect(() => {
    fetchReturnedStock()
  }, [
    pagination.current,
    pagination.pageSize,
    refreshTrigger,
    dateRange,
    customerId,
    status,
    sortOrder,

    productId,
    search
  ])

  return (
    <>
      <Card className="shadow-md">
        <div className="mb-6 flex items-center justify-between">
          <div className="flex items-center gap-2">
            <FaBoxOpen className="text-2xl text-indigo-600" />
            <span className="text-lg font-semibold">Returned Stock History</span>
          </div>
          <Button type="primary" icon={<FaSync />} onClick={fetchReturnedStock} loading={loading}>
            Refresh
          </Button>
        </div>

        {/* Comprehensive Filters */}
        <Card className="mb-4 bg-gray-50" size="small">
          <Form layout="vertical">
            <Row gutter={[16, 16]}>
              <Col xs={24} sm={12} md={8} lg={6}>
                <Form.Item label="Date Range" className="!mb-0">
                  <RangePicker
                    value={dateRange}
                    onChange={(dates) => setDateRange(dates as [dayjs.Dayjs, dayjs.Dayjs])}
                    placeholder={['Start Date', 'End Date']}
                    className="w-full"
                  />
                </Form.Item>
              </Col>

              <Col xs={24} sm={12} md={8} lg={6}>
                <Form.Item label="Customer" className="!mb-0">
                  <Select
                    allowClear
                    showSearch
                    value={customerId}
                    onChange={setCustomerId}
                    placeholder="Select customer"
                    className="w-full"
                    options={customers}
                    filterOption={(input, option) =>
                      String(option?.label ?? '')
                        .toLowerCase()
                        .includes(input.toLowerCase())
                    }
                  />
                </Form.Item>
              </Col>

              <Col xs={24} sm={12} md={8} lg={6}>
                <Form.Item label="Status" className="!mb-0">
                  <Select
                    value={status}
                    onChange={setStatus}
                    className="w-full"
                    options={[
                      { label: 'All Status', value: StockReturnStatus.ALL },
                      { label: 'Active', value: StockReturnStatus.ACTIVE },
                      { label: 'Voided', value: StockReturnStatus.VOIDED }
                    ]}
                  />
                </Form.Item>
              </Col>

              <Col xs={24} sm={12} md={8} lg={6}>
                <Form.Item label="Sort Order" className="!mb-0">
                  <Select
                    value={sortOrder}
                    onChange={setSortOrder}
                    className="w-full"
                    options={[
                      { label: 'Newest First', value: StockReturnSortOrder.NEWEST_FIRST },
                      { label: 'Oldest First', value: StockReturnSortOrder.OLDEST_FIRST },
                      {
                        label: 'Amount High to Low',
                        value: StockReturnSortOrder.AMOUNT_HIGH_TO_LOW
                      },
                      {
                        label: 'Amount Low to High',
                        value: StockReturnSortOrder.AMOUNT_LOW_TO_HIGH
                      }
                    ]}
                  />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={[16, 16]} className="mt-4">
              <Col xs={24} sm={12} md={8} lg={6}>
                <Form.Item label="Product" className="!mb-0">
                  <Select
                    allowClear
                    showSearch
                    value={productId}
                    onChange={setProductId}
                    placeholder="Select product"
                    className="w-full"
                    options={products}
                    filterOption={(input, option) =>
                      String(option?.label ?? '')
                        .toLowerCase()
                        .includes(input.toLowerCase())
                    }
                  />
                </Form.Item>
              </Col>

              <Col xs={24} sm={12} md={8} lg={6}>
                <Form.Item label="Search" className="!mb-0">
                  <Input
                    value={search}
                    onChange={(e) => setSearch(e.target.value)}
                    placeholder="Search invoice, customer..."
                    allowClear
                  />
                </Form.Item>
              </Col>

              <Col xs={24} sm={12} md={8} lg={6}>
                <Form.Item label="Actions" className="!mb-0">
                  <Space>
                    <Button
                      icon={<ClearOutlined />}
                      onClick={() => {
                        setDateRange(null)
                        setStatus(StockReturnStatus.ALL)
                        setSortOrder(StockReturnSortOrder.NEWEST_FIRST)
                        setCustomerId(null)
                        setProductId(null)
                        setSearch('')
                      }}
                    >
                      Clear
                    </Button>
                  </Space>
                </Form.Item>
              </Col>
            </Row>
          </Form>
        </Card>
        <Table
          columns={columns}
          dataSource={returnedStock}
          rowKey="id"
          sticky
          loading={loading}
          scroll={{ y: 400 }}
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: pagination.total,
            showSizeChanger: true,
            pageSizeOptions: ['10', '20', '50', '100', '200', '500'],
            showTotal: (total) => `Total ${total} items`,
            showPrevNextJumpers: true,
            showQuickJumper: true,
            position: ['topRight']
          }}
          onChange={handleTableChange}
        />
      </Card>

      {/* Void Modal */}
      <Modal
        title="Void Return Stock"
        open={voidModalVisible}
        onOk={handleVoidConfirm}
        onCancel={handleVoidCancel}
        confirmLoading={loading}
      >
        <Form layout="vertical">
          <Form.Item
            label="Reason for voiding"
            required
            rules={[{ required: true, message: 'Please provide a reason' }]}
          >
            <Input.TextArea
              rows={4}
              value={voidReason}
              onChange={(e) => setVoidReason(e.target.value)}
              placeholder="Enter reason for voiding this return stock"
            />
          </Form.Item>
        </Form>
      </Modal>

      {/* Details Modal */}
      <StockReturnDetails
        visible={detailsModalVisible}
        onClose={handleCloseDetails}
        stockReturn={selectedStockReturn}
      />

      {/* Print/Save Modal */}
      <PrintSaveModal
        open={printSaveModalVisible}
        loading={printSaveLoading}
        onPrint={handlePrint}
        onSave={handleSave}
        onCancel={handlePrintSaveCancel}
      />
    </>
  )
}

export default ReturnStockList
