import { useState } from 'react'
import { Modal, Form, Input, App } from 'antd'
import { expenseApi } from '@/renderer/services'
import { useSelector } from 'react-redux'
import { IRootState } from '@/renderer/redux'
import { VoidExpenseParams } from '@/common/types/expense'

// This interface is only used as props between components, so it stays here
interface VoidExpenseModalProps {
  expenseId: string | null
  open: boolean
  onClose: () => void
  onVoidSuccess: () => void
}

export const VoidExpenseModal = ({
  expenseId,
  open,
  onClose,
  onVoidSuccess
}: VoidExpenseModalProps) => {
  const [form] = Form.useForm()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const { message } = App.useApp()

  const user = useSelector((state: IRootState) => state.user.data)

  const handleSubmit = async () => {
    if (!expenseId || !user?.id) {
      message.error('Missing required information')
      return
    }

    setIsSubmitting(true)
    const values = await form.validateFields()

    const voidParams: VoidExpenseParams = {
      id: expenseId,
      voidedById: user.id,
      reason: values.reason
    }

    const response = await expenseApi.voidExpense(voidParams)

    setIsSubmitting(false)

    if (response.error.error || response.data.error) {
      message.error(response.error.message || response.data.error.message)
      return
    }

    message.success('Expense deleted successfully')

    form.resetFields()
    onVoidSuccess()
  }

  const handleCancel = () => {
    form.resetFields()
    onClose()
  }

  return (
    <Modal
      title="Void Expense"
      open={open}
      onOk={handleSubmit}
      onCancel={handleCancel}
      confirmLoading={isSubmitting}
      okText="Void"
      cancelText="Cancel"
    >
      <Form form={form} layout="vertical">
        <p>Are you sure you want to void this expense? This action cannot be undone.</p>
        <Form.Item
          name="reason"
          label="Reason for voiding"
          rules={[{ required: true, message: 'Please provide a reason' }]}
        >
          <Input.TextArea rows={4} placeholder="Enter reason for voiding this expense" />
        </Form.Item>
      </Form>
    </Modal>
  )
}
