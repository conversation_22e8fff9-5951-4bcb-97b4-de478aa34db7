import { <PERSON><PERSON>, <PERSON>, Col, Row, Table, Spin } from 'antd'
import { useTheme } from '@/renderer/contexts'
import { useApi } from '@/renderer/hooks'
import { reportsApi } from '@/renderer/services'
import { InventoryValuationReport, ReportFormat } from '@/common/types'
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell
} from 'recharts'
import { useEffect } from 'react'

interface Props {
  format: ReportFormat
  shouldGenerate: boolean
  onGenerateComplete: () => void
  currentReportId: string
  categoryId?: string
  minStockLevel?: number
}

const InventoryValuation = ({
  format,
  shouldGenerate,
  onGenerateComplete,
  currentReportId,
  categoryId,
  minStockLevel
}: Props) => {
  const { isDarkMode } = useTheme()
  const { request, data, isLoading, errorMessage } = useApi<
    InventoryValuationReport,
    [{ format: ReportFormat; categoryId?: string; minStockLevel?: number }]
  >(reportsApi.generateInventoryValuation)

  useEffect(() => {
    if (shouldGenerate && currentReportId === 'inventory-valuation') {
      request({
        format,
        categoryId,
        minStockLevel
      }).finally(() => {
        onGenerateComplete()
      })
    }
  }, [shouldGenerate])

  if (isLoading) {
    return <Spin size="large" className="flex w-full justify-center p-8" />
  }

  if (errorMessage) {
    return <Alert type="error" message={errorMessage} className="mb-4" />
  }

  if (!data) return null

  const COLORS = ['#6366f1', '#8b5cf6', '#d946ef', '#f43f5e', '#f97316']

  return (
    <div className="space-y-6">
      {/* Summary Cards */}
      <Row gutter={[16, 16]}>
        <Col span={6}>
          <Card className={isDarkMode ? 'bg-black' : 'bg-white'}>
            <div className="text-center">
              <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Total Products
              </p>
              <h2 className={`text-2xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-800'}`}>
                {data.summary.totalProducts}
              </h2>
            </div>
          </Card>
        </Col>
        <Col span={6}>
          <Card className={isDarkMode ? 'bg-black' : 'bg-white'}>
            <div className="text-center">
              <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Total Value
              </p>
              <h2 className={`text-2xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-800'}`}>
                ₹{data.summary.totalValue.toLocaleString()}
              </h2>
            </div>
          </Card>
        </Col>
        <Col span={6}>
          <Card className={isDarkMode ? 'bg-black' : 'bg-white'}>
            <div className="text-center">
              <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Average Value
              </p>
              <h2 className={`text-2xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-800'}`}>
                ₹{data.summary.averageValue.toLocaleString()}
              </h2>
            </div>
          </Card>
        </Col>
        <Col span={6}>
          <Card className={isDarkMode ? 'bg-black' : 'bg-white'}>
            <div className="text-center">
              <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Low Stock Items
              </p>
              <h2 className={`text-2xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-800'}`}>
                {data.summary.lowStockItems}
              </h2>
            </div>
          </Card>
        </Col>
      </Row>

      {/* Charts */}
      <Row gutter={[16, 16]}>
        <Col span={12}>
          <Card title="Category Distribution" className={isDarkMode ? 'bg-black' : 'bg-white'}>
            <div style={{ height: 300 }}>
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={data.categoryDistribution}
                    dataKey="totalValue"
                    nameKey="category"
                    cx="50%"
                    cy="50%"
                    outerRadius={80}
                    label
                  >
                    {data.categoryDistribution.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <Tooltip />
                </PieChart>
              </ResponsiveContainer>
            </div>
          </Card>
        </Col>
        <Col span={12}>
          <Card title="Ageing Analysis" className={isDarkMode ? 'bg-black' : 'bg-white'}>
            <div style={{ height: 300 }}>
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={data.ageingAnalysis}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="ageGroup" />
                  <YAxis />
                  <Tooltip />
                  <Bar dataKey="value" fill="#6366f1" />
                </BarChart>
              </ResponsiveContainer>
            </div>
          </Card>
        </Col>
      </Row>

      {/* Stock Details Table */}
      <Card title="Stock Details" className={isDarkMode ? 'bg-black' : 'bg-white'}>
        <Table
          dataSource={data.stockDetails}
          columns={[
            {
              title: 'Product ID',
              dataIndex: 'productId',
              key: 'productId'
            },
            {
              title: 'Name',
              dataIndex: 'name',
              key: 'name'
            },
            {
              title: 'Category',
              dataIndex: 'category',
              key: 'category'
            },
            {
              title: 'Quantity',
              dataIndex: 'quantity',
              key: 'quantity'
            },
            {
              title: 'Value',
              dataIndex: 'value',
              key: 'value',
              render: (value) => `₹${value.toLocaleString()}`
            },
            {
              title: 'Last Purchase',
              dataIndex: 'lastPurchaseDate',
              key: 'lastPurchaseDate',
              render: (date) => new Date(date).toLocaleDateString()
            },
            {
              title: 'Last Sale',
              dataIndex: 'lastSaleDate',
              key: 'lastSaleDate',
              render: (date) => new Date(date).toLocaleDateString()
            }
          ]}
          pagination={false}
          scroll={{ x: true }}
        />
      </Card>
    </div>
  )
}

export default InventoryValuation
