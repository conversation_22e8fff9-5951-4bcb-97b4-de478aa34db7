import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>er, <PERSON>, Statistic, message } from 'antd'
import { useEffect, useState } from 'react'
import { FaScaleBalanced } from 'react-icons/fa6'
import { cashVaultApi } from '@/renderer/services'
import { CashVaultReconcileResponse } from '@/common/types'

interface ReconcileDrawerProps {
  open: boolean
  onClose: () => void
}

export const ReconcileDrawer = ({ open, onClose }: ReconcileDrawerProps) => {
  const [loading, setLoading] = useState(false)
  const [data, setData] = useState<CashVaultReconcileResponse>()

  const handleReconcile = async () => {
    setLoading(true)
    const response = await cashVaultApi.reconcileVault()
    setLoading(false)
    // console.log('reconcile response', response)

    if (response.error.error || response.data.error) {
      message.error(response.error.message || response.data.error.message)
      return
    }

    setData(response.data.data)
  }

  // Load data when drawer opens
  useEffect(() => {
    if (open) {
      handleReconcile()
    } else {
      setData(undefined)
    }
  }, [open])

  return (
    <Drawer
      title="Reconcile Cash Vault"
      placement="right"
      onClose={onClose}
      open={open}
      width={400}
      extra={<Button onClick={onClose}>Close</Button>}
    >
      {loading ? (
        <div className="flex h-full items-center justify-center">
          <Space direction="vertical" align="center">
            <FaScaleBalanced className="animate-bounce text-4xl" />
            <span>Reconciling...</span>
          </Space>
        </div>
      ) : data ? (
        <Space direction="vertical" className="w-full" size="large">
          <Alert
            type={data.isReconciled ? 'success' : 'error'}
            message={data.isReconciled ? 'Balances Match' : 'Balances Do Not Match'}
            description={
              data.isReconciled
                ? 'The current balance matches the calculated balance from transactions.'
                : 'There is a discrepancy between the current balance and calculated balance.'
            }
            showIcon
          />
          <Statistic
            title="Current Balance"
            value={data.currentBalance}
            precision={2}
            prefix="Rs. "
          />
          <Statistic
            title="Calculated Balance"
            value={data.calculatedBalance}
            precision={2}
            prefix="Rs. "
          />
          {!data.isReconciled && (
            <Statistic
              title="Difference"
              value={Math.abs(data.difference)}
              precision={2}
              prefix="Rs. "
              valueStyle={{ color: '#cf1322' }}
            />
          )}
        </Space>
      ) : null}
    </Drawer>
  )
}
