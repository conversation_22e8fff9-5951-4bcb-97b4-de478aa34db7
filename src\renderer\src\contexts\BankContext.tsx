import { createContext, useContext, useState, useEffect } from 'react'
import { bankApi } from '../services'
import { useApi } from '../hooks'

export interface BankOption {
  id: string
  label: string
}

interface BankContextType {
  banks: BankOption[]
  loading: boolean
  error: any
  errorMessage: string | null
  refreshBanks: () => Promise<void>
}

const BankContext = createContext<BankContextType>({
  banks: [],
  loading: false,
  error: null,
  errorMessage: null,
  refreshBanks: async () => {}
})

export const useBankContext = () => useContext(BankContext)

export const BankProvider = ({ children }: { children: React.ReactNode }) => {
  const [banks, setBanks] = useState<BankOption[]>([])

  const {
    data: banksData,
    isLoading: loading,
    request: getBanks,
    error,
    errorMessage
  } = useApi<BankOption[], []>(bankApi.getBanksForSelect)

  const fetchBanks = async () => {
    if (!banks.length) {
      await getBanks()
    }
  }

  const refreshBanks = async () => {
    setBanks([])
    await getBanks()
  }

  useEffect(() => {
    fetchBanks()
  }, [])

  useEffect(() => {
    if (banksData) {
      setBanks(banksData)
    }
  }, [banksData])

  return (
    <BankContext.Provider value={{ banks, loading, error, errorMessage, refreshBanks }}>
      {children}
    </BankContext.Provider>
  )
}
