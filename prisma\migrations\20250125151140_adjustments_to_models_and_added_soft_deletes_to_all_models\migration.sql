/*
  Warnings:

  - Added the required column `createdById` to the `Ledger` table without a default value. This is not possible if the table is not empty.

*/
-- AlterTable
ALTER TABLE "Ledger" ADD COLUMN     "createdById" TEXT NOT NULL,
ADD COLUMN     "voidedById" TEXT;

-- CreateIndex
CREATE INDEX "Ledger_status_idx" ON "Ledger"("status");

-- AddForeignKey
ALTER TABLE "Ledger" ADD CONSTRAINT "Ledger_createdById_fkey" FOREIGN KEY ("createdById") REFERENCES "Admin"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Ledger" ADD CONSTRAINT "Ledger_voidedById_fkey" FOREIGN KEY ("voidedById") REFERENCES "Admin"("id") ON DELETE SET NULL ON UPDATE CASCADE;
