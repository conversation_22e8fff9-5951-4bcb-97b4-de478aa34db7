import { http } from './http';
import { Channels } from '@/common/constants';
import { CashVaultTransactionFilters, InitializeVaultData, VaultTransactionData, GetCashVaultStatementParams } from '@/common/types';

export const isInitialized = async () => {
    return await http.get(Channels.IS_VAULT_INITIALIZED);
};

export const getCurrentBalance = async () => {
    return await http.get(Channels.GET_VAULT_BALANCE);
};

export const initializeVault = async (data: InitializeVaultData) => {
    return await http.post(Channels.INITIALIZE_VAULT, {
        body: data
    });
};

export const depositToVault = async (data: VaultTransactionData) => {
    return await http.post(Channels.DEPOSIT_TO_VAULT, {
        body: data
    });
};

export const withdrawFromVault = async (data: VaultTransactionData) => {
    return await http.post(Channels.WITHDRAW_FROM_VAULT, {
        body: data
    });
};

export const getAllTransactions = async (filters: CashVaultTransactionFilters) => {
    return await http.get(Channels.GET_VAULT_LEDGER_ENTRIES, {
        query: filters
    });
};

export const reconcileVault = async () => {
    return await http.get(Channels.RECONCILE_VAULT);
};

export const generateCashVaultStatement = async (params: GetCashVaultStatementParams) => {
    return await http.get(Channels.GENERATE_CASH_VAULT_STATEMENT, {
        query: params
    });
};

