import { http } from './http';
import { Channels } from '@/common/constants';
import {
    GetBankLedgerParams,
    BankLedgerResponse,
    BankTransactionSummaryParams,
    BankTransactionSummary,
    DailyBankLedgerParams,
    DailyBankLedger,
    BankReconciliation,
    SearchBankTransactionsParams,
    DailyClosingBalancesParams,
    DailyClosingBalance
} from '@/common/types/bankLedger';

export const getBankLedgerEntries = async (params: GetBankLedgerParams) => {
    return await http.get(Channels.GET_BANK_LEDGER_ENTRIES, { body: params });
};

export const getBankTransactionSummary = async (params: BankTransactionSummaryParams) => {
    return await http.get(Channels.GET_BANK_TRANSACTION_SUMMARY, { body: params });
};

export const getDailyBankLedger = async (params: DailyBankLedgerParams) => {
    return await http.get(Channels.GET_DAILY_BANK_LEDGER, { body: params });
};

export const reconcileBank = async (bankId: string) => {
    return await http.get(Channels.RECONCILE_BANK, { body: { bankId } });
};

export const searchBankTransactions = async (params: SearchBankTransactionsParams) => {
    return await http.get(Channels.SEARCH_BANK_TRANSACTIONS, { body: params });
};

export const getDailyClosingBalances = async (params: DailyClosingBalancesParams) => {
    return await http.get(Channels.GET_DAILY_CLOSING_BALANCES, { body: params });
};






