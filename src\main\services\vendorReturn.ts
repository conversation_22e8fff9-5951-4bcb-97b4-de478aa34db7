import { prisma } from '../db'
import { Status, CreditDebit, LedgerType, PurchaseInvoiceType } from '@prisma/client'
import dayjs from 'dayjs'
import {
    ProcessVendorReturnParams,
    VoidVendorReturnParams,
    GetVendorReturnsParams
} from '@/common/types/vendorReturn'

class VendorReturnService {
    private generateReturnInvoiceNumber(vendorName: string): string {
        return `V_RET_${vendorName}_${dayjs().format('YYYY/MM/DD HH:mm:ss')}`
    }

    async processVendorReturn({ vendorId, items, adminId, paymentDetails, description }: ProcessVendorReturnParams) {
        try {
            // Validate items
            if (!items || items.length === 0) {
                throw new Error('No items provided for vendor return')
            }

            // Validate quantities and prices
            for (const item of items) {
                if (item.quantity <= 0) {
                    throw new Error(`Return quantity must be positive for product ${item.productId}`)
                }
                if (item.purchasePrice < 0) {
                    throw new Error(`Purchase price cannot be negative for product ${item.productId}`)
                }
            }

            return await prisma.$transaction(async (tx) => {
                // Get vendor
                const vendor = await tx.party.findUnique({
                    where: { id: vendorId }
                })
                if (!vendor) throw new Error('Vendor not found')

                // Validate total stock availability (regardless of vendor)
                for (const item of items) {
                    // Get total available quantity for this product
                    const availableStock = await tx.stock.aggregate({
                        where: {
                            productId: item.productId,
                            status: 'IN_STOCK'
                        },
                        _sum: {
                            quantity: true
                        }
                    })

                    const totalAvailable = availableStock._sum.quantity || 0

                    if (totalAvailable < item.quantity) {
                        const product = await tx.product.findUnique({
                            where: { id: item.productId },
                            select: { name: true }
                        })
                        throw new Error(`Cannot return more than available quantity for ${product?.name || item.productId}. Available: ${totalAvailable}, Requested: ${item.quantity}`)
                    }
                }

                // Calculate total return amount
                const totalAmount = items.reduce((sum, item) => sum + (item.quantity * item.purchasePrice), 0)

                // Create return invoice
                const returnInvoice = await tx.purchaseInvoice.create({
                    data: {
                        invoiceNumber: this.generateReturnInvoiceNumber(vendor.name),
                        type: PurchaseInvoiceType.VENDOR_RETURN,
                        totalAmount: totalAmount,
                        paidAmount: 0,
                        previousBalance: vendor.currentBalance,
                        newBalance: vendor.currentBalance - totalAmount,
                        date: new Date(),
                        vendorId: vendorId,
                        createdById: adminId,
                        status: Status.ACTIVE,
                        // description: description || 'Vendor Return'
                    }
                })

                // Create ledger entry for the return
                await tx.ledger.create({
                    data: {
                        date: new Date(),
                        amount: totalAmount,
                        creditOrDebit: CreditDebit.DEBIT,
                        description: `Return to vendor: ${vendor.name}, ${description || ''}`,
                        referenceType: LedgerType.PurchaseInvoice,
                        purchaseRef: returnInvoice.id,
                        status: Status.ACTIVE,
                        partyId: vendorId,
                        createdById: adminId
                    }
                })

                // Update vendor balance
                await tx.party.update({
                    where: { id: vendorId },
                    data: {
                        currentBalance: { decrement: totalAmount }
                    }
                })

                // If payment details are provided
                if (paymentDetails) {
                    // Create payment record
                    const payment = await tx.payments.create({
                        data: {
                            date: new Date(),
                            type: 'PAID',
                            amount: totalAmount,
                            paymentMethod: 'CASH',
                            sourceLocation: paymentDetails.source,
                            locationId: paymentDetails.bankId,
                            status: Status.ACTIVE,
                            createdById: adminId,
                            partyId: vendorId
                        }
                    })

                    // Create ledger entry for payment
                    await tx.ledger.create({
                        data: {
                            date: new Date(),
                            amount: totalAmount,
                            creditOrDebit: CreditDebit.CREDIT,
                            description: `Payment for vendor return #${returnInvoice.invoiceNumber}`,
                            referenceType: LedgerType.Payment,
                            paymentRef: payment.id,
                            status: Status.ACTIVE,
                            bankId: paymentDetails.bankId,
                            cashSource: paymentDetails.source,
                            createdById: adminId,
                            partyId: vendorId
                        }
                    })

                    // update vendor balance
                    await tx.party.update({
                        where: { id: vendorId },
                        data: { currentBalance: { increment: totalAmount } }
                    })

                    // Update cash location balance
                    switch (paymentDetails.source) {
                        case 'SMALL_COUNTER':
                            const smallCounter = await tx.smallCounter.findFirst()
                            if (!smallCounter) throw new Error('Small counter not found')
                            await tx.smallCounter.update({
                                where: { id: smallCounter.id },
                                data: { cashInShop: { increment: totalAmount } }
                            })
                            break
                        case 'CASH_VAULT':
                            const cashVault = await tx.cashVault.findFirst()
                            if (!cashVault) throw new Error('Cash vault not found')
                            await tx.cashVault.update({
                                where: { id: cashVault.id },
                                data: { balance: { increment: totalAmount } }
                            })
                            break
                        case 'BANK':
                            if (!paymentDetails.bankId) throw new Error('Bank ID is required for bank payment')
                            const bank = await tx.banks.findFirst({
                                where: { id: paymentDetails.bankId }
                            })
                            if (!bank) throw new Error('Bank not found')
                            await tx.banks.update({
                                where: { id: bank.id },
                                data: { balance: { increment: totalAmount } }
                            })
                            break
                    }
                }

                // Process each returned item
                for (const item of items) {
                    // Create purchase item record
                    await tx.purchaseItem.create({
                        data: {
                            quantity: -item.quantity, // Negative to indicate return
                            purchasePrice: item.purchasePrice,
                            total: -(item.quantity * item.purchasePrice),
                            purchaseInvoiceId: returnInvoice.id,
                            productId: item.productId
                        }
                    })

                    // Find stock entries to update (regardless of vendor)
                    // We'll use a greedy approach - update stock entries until we've accounted for all returned quantity
                    let remainingQuantity = item.quantity

                    // Get all available stock entries for this product, sorted by quantity (largest first)
                    // This minimizes the number of stock entries we need to update
                    const stockEntries = await tx.stock.findMany({
                        where: {
                            productId: item.productId,
                            status: 'IN_STOCK',
                            quantity: { gt: 0 }
                        },
                        orderBy: { quantity: 'desc' }
                    })

                    // Update stock entries until we've accounted for all returned quantity
                    for (const stockEntry of stockEntries) {
                        if (remainingQuantity <= 0) break

                        const quantityToDeduct = Math.min(remainingQuantity, stockEntry.quantity)

                        // Update this stock entry
                        await tx.stock.update({
                            where: { id: stockEntry.id },
                            data: {
                                quantity: { decrement: quantityToDeduct }
                            }
                        })

                        remainingQuantity -= quantityToDeduct
                    }

                    // Update product quantity
                    await tx.product.update({
                        where: { id: item.productId },
                        data: {
                            quantityInStock: { decrement: item.quantity }
                        }
                    })
                }

                return returnInvoice
            })
        } catch (error) {
            throw error
        }
    }

    async voidVendorReturn({ returnInvoiceId, adminId, reason }: VoidVendorReturnParams) {
        return await prisma.$transaction(async (tx) => {
            // Get return invoice
            const returnInvoice = await tx.purchaseInvoice.findUnique({
                where: { id: returnInvoiceId },
                include: {
                    items: true,
                    vendor: true
                }
            })

            if (!returnInvoice) throw new Error('Return invoice not found')
            if (returnInvoice.status === Status.VOID) throw new Error('Return invoice is already voided')
            if (returnInvoice.type !== PurchaseInvoiceType.VENDOR_RETURN) throw new Error('Invoice is not a vendor return')

            // Void the return invoice
            await tx.purchaseInvoice.update({
                where: { id: returnInvoiceId },
                data: {
                    status: Status.VOID,
                    voidedById: adminId,
                    voidedAt: new Date(),
                    voidingReason: reason
                }
            })

            // Void associated ledger entries
            await tx.ledger.updateMany({
                where: {
                    purchaseRef: returnInvoiceId,
                    status: Status.ACTIVE
                },
                data: {
                    status: Status.VOID,
                    voidedById: adminId,
                    // voidedAt: new Date(),
                    // voidingReason: reason
                }
            })

            // Find and void any associated payment
            const paymentLedger = await tx.ledger.findFirst({
                where: {
                    description: { contains: returnInvoice.invoiceNumber },
                    referenceType: LedgerType.Payment,
                    status: Status.ACTIVE
                },
                include: {
                    payment: true
                }
            })

            // If there was a payment, void it and restore cash balances
            if (paymentLedger?.payment) {
                const payment = paymentLedger.payment

                // Restore cash balance
                switch (payment.sourceLocation) {
                    case 'SMALL_COUNTER':
                        const smallCounter = await tx.smallCounter.findFirst()
                        if (!smallCounter) throw new Error('Small counter not found')
                        await tx.smallCounter.update({
                            where: { id: smallCounter.id },
                            data: { cashInShop: { decrement: payment.amount } }
                        })
                        break
                    case 'CASH_VAULT':
                        const cashVault = await tx.cashVault.findFirst()
                        if (!cashVault) throw new Error('Cash vault not found')
                        await tx.cashVault.update({
                            where: { id: cashVault.id },
                            data: { balance: { decrement: payment.amount } }
                        })
                        break
                    case 'BANK':
                        if (!payment.locationId) throw new Error('Bank ID not found')
                        const bank = await tx.banks.findFirst({
                            where: { id: payment.locationId }
                        })
                        if (!bank) throw new Error('Bank not found')
                        await tx.banks.update({
                            where: { id: bank.id },
                            data: { balance: { decrement: payment.amount } }
                        })
                        break
                }

                // Void the payment
                await tx.payments.update({
                    where: { id: payment.id },
                    data: {
                        status: Status.VOID,
                        voidedById: adminId,
                        voidedAt: new Date(),
                        voidingReason: reason
                    }
                })

                // void the ledger entry for the payment
                await tx.ledger.update({
                    where: { id: paymentLedger.id },
                    data: { status: Status.VOID, voidedById: adminId }
                })
            }

            // Restore vendor balance
            await tx.party.update({
                where: { id: returnInvoice.vendorId },
                data: {
                    currentBalance: { increment: returnInvoice.totalAmount }
                }
            })

            // Restore stock quantities - we need to add back the quantities to the stock
            // Since we don't track which specific stock entries were updated during the return,
            // we'll create new stock entries for the returned items
            for (const item of returnInvoice.items) {
                // Create a new stock entry for the returned quantity
                await tx.stock.create({
                    data: {
                        purchasePrice: Math.abs(item.purchasePrice),
                        quantity: Math.abs(item.quantity),
                        status: 'IN_STOCK',
                        productId: item.productId,
                        vendorId: returnInvoice.vendorId, // Associate with the vendor we returned to
                        purchaseInvoiceId: returnInvoiceId // Link to the voided return invoice for tracking
                    }
                })

                // Restore product quantity
                await tx.product.update({
                    where: { id: item.productId },
                    data: {
                        quantityInStock: { increment: Math.abs(item.quantity) }
                    }
                })
            }

            return returnInvoice
        })
    }

    async getVendorReturns({ startDate, endDate, vendorId, productId, status, page = 1, limit = 10 }: GetVendorReturnsParams) {
        const where = {
            type: PurchaseInvoiceType.VENDOR_RETURN,
            ...((status && status !== 'ALL' as Status) && { status }),
            ...(startDate && {
                date: {
                    gte: startDate
                }
            }),
            ...(endDate && {
                date: {
                    lte: endDate
                }
            }),
            ...(vendorId && {
                vendorId
            }),
            ...(productId && {
                items: {
                    some: {
                        productId
                    }
                }
            })
        }

        const [returns, total] = await Promise.all([
            prisma.purchaseInvoice.findMany({
                where,
                include: {
                    items: {
                        include: {
                            product: {
                                include: {
                                    category: true
                                }
                            }
                        }
                    },
                    vendor: true,
                    createdBy: true,
                    voidedBy: true
                },
                orderBy: {
                    date: 'desc'
                },
                skip: (page - 1) * limit,
                take: limit
            }),
            prisma.purchaseInvoice.count({ where })
        ])

        return {
            returns,
            total,
            page,
            totalPages: Math.ceil(total / limit)
        }
    }
}

/**
 * Note on vendor returns:
 * 
 * The current implementation allows returning stock to any vendor, regardless of which vendor
 * originally supplied the stock. This matches real-world shop operations where:
 * 
 * 1. Shop owners may not track exactly which vendor supplied which physical items
 * 2. Items are picked randomly from shelves during sales, not in FIFO order
 * 3. When returning items, shop owners decide which vendor to return to based on relationships,
 *    current prices, or other business factors
 * 
 * The system only verifies that:
 * - The total quantity being returned doesn't exceed what's available in stock
 * - The vendor exists and is active
 * - The return quantities are positive
 * 
 * This approach prioritizes flexibility and real-world usability over strict inventory tracking.
 */

export const vendorReturnService = new VendorReturnService()
