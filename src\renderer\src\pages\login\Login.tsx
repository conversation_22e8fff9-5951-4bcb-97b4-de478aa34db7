import { useState, useEffect } from 'react'
import { userActions } from '@/renderer/redux'
import { Button, Form, Input, message, Upload, Modal, Alert } from 'antd'
import { useDispatch } from 'react-redux'
import { userApi, backupApi } from '@/renderer/services'
import './login.scss'
import { App_Routes, RequiredRule } from '@/common/constants'
import type { LoginData } from '@/common/types'
import { useNavigate } from 'react-router-dom'
import { ExclamationCircleOutlined, UploadOutlined } from '@ant-design/icons'

const { confirm } = Modal

const Login = () => {
  const dispatch = useDispatch()
  const navigate = useNavigate()

  const [loading, setLoading] = useState(false)
  const [isRestoring, setIsRestoring] = useState(false)
  const [isSchemaInitialized, setIsSchemaInitialized] = useState(true)

  useEffect(() => {
    // Check if the database schema is initialized
    const checkSchema = async () => {
      try {
        const initialized = await window.api.checkSchemaInitialized()
        setIsSchemaInitialized(initialized)
      } catch (error) {
        console.error('Failed to check schema status:', error)
        // Default to assuming schema is not initialized to be safe
        setIsSchemaInitialized(false)
      }
    }

    checkSchema()
  }, [])

  const handleLogin = async (values: LoginData) => {
    // If schema is not initialized, don't allow login attempts
    if (!isSchemaInitialized) {
      message.error('Please restore a database backup before logging in')
      return
    }

    setLoading(true)
    const response = await userApi.login(values)
    setLoading(false)

    if (response.error.error || response.data.error) {
      message.error(response.error.message || response.data.error.message)
      return
    }

    const userData = response.data.data
    dispatch(userActions.init(userData))

    // Navigate to dashboard after successful verification
    navigate(App_Routes.DASHBOARD)
  }

  const handleRestore = async (file: File) => {
    try {
      setIsRestoring(true)
      const response = await backupApi.restoreBackup(file.path)
      setIsRestoring(false)

      if (response.error.error || response.data.error) {
        message.error(response.error.message || response.data.error.message)
      } else {
        message.success('Database restored successfully')
        // After successful restore, we should consider the schema as initialized
        setIsSchemaInitialized(true)
      }
    } catch (error: any) {
      message.error(error.message)
      setIsRestoring(false)
    }
  }

  return (
    <div className="container">
      <h3>Login</h3>

      {!isSchemaInitialized && (
        <Alert
          message="Database Schema Not Initialized"
          description="The database schema is not properly initialized. Please restore a database backup using the button at the bottom right."
          type="warning"
          showIcon
          style={{ marginBottom: 20, width: '400px', maxWidth: '90%' }}
        />
      )}

      <Form layout="vertical" className="form" onFinish={handleLogin}>
        <Form.Item name="username" label="Username" rules={RequiredRule}>
          <Input placeholder="Enter your username" size="large" />
        </Form.Item>
        <Form.Item name="password" label="Password" rules={RequiredRule}>
          <Input.Password placeholder="Enter your password" size="large" />
        </Form.Item>
        <div className="form-actions">
          <Button
            type="primary"
            htmlType="submit"
            className="w-full"
            size="large"
            loading={loading}
            disabled={!isSchemaInitialized}
          >
            Login
          </Button>
        </div>
      </Form>

      <div className="restore-backup-container">
        <Upload
          accept=".sql"
          beforeUpload={(file) => {
            if (!file.name.endsWith('.sql')) {
              message.error('Please select a valid SQL backup file')
              return false
            }
            confirm({
              title: 'Restore Database',
              icon: <ExclamationCircleOutlined />,
              content: 'This will overwrite your current database. Are you sure?',
              okText: 'Yes',
              okType: 'danger',
              cancelText: 'No',
              onOk: () => handleRestore(file)
            })
            return false
          }}
          showUploadList={false}
          disabled={isRestoring}
        >
          <Button
            icon={<UploadOutlined />}
            loading={isRestoring}
            type={!isSchemaInitialized ? 'primary' : 'default'}
          >
            Restore from Backup
          </Button>
        </Upload>
      </div>
    </div>
  )
}

export default Login
