import { Form, Input, Drawer, Button, message, InputNumber } from 'antd'
import { partyApi } from '@/renderer/services'
import { useSelector } from 'react-redux'
import { IRootState } from '@/renderer/redux'

interface CreateCustomerDrawerProps {
  open: boolean
  onClose: () => void
  onCustomerCreated: () => void
}

export const CreateCustomerDrawer = ({
  open,
  onClose,
  onCustomerCreated
}: CreateCustomerDrawerProps) => {
  const [form] = Form.useForm()

  const user = useSelector((state: IRootState) => state.user.data)

  const handleSubmit = async (values: any) => {
    const response = await partyApi.createParty({
      ...values,
      type: 'CUSTOMER',
      createdById: user?.id
    })

    if (response.error?.error || response.data?.error) {
      message.error(response.error?.message || response.data?.error?.message)
      return
    }
    message.success('Customer created successfully')
    form.resetFields()
    onCustomerCreated()
  }

  return (
    <Drawer
      title="Add New Customer"
      placement="right"
      onClose={onClose}
      open={open}
      width={500}
      extra={
        <Button type="primary" onClick={() => form.submit()}>
          Create
        </Button>
      }
    >
      <Form form={form} layout="vertical" onFinish={handleSubmit}>
        <Form.Item
          name="name"
          label="Customer Name"
          rules={[{ required: true, message: 'Please enter customer name' }]}
        >
          <Input />
        </Form.Item>

        <Form.Item name="openingBalance" label="Opening Balance">
          <InputNumber className="w-full" addonBefore="PKR" />
        </Form.Item>

        <Form.Item name="contact" label="Contact Person">
          <Input />
        </Form.Item>

        <Form.Item name="phoneNumber" label="Phone Number">
          <Input />
        </Form.Item>

        <Form.Item name="address" label="Address">
          <Input.TextArea rows={4} />
        </Form.Item>
      </Form>
    </Drawer>
  )
}
