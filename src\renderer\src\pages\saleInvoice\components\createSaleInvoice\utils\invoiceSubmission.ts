import { App } from 'antd'
import { SaleInvoiceFormData } from '@/common/types'
import { saleInvoiceApi } from '@/renderer/services'
import { confirmInvoiceNumber } from './invoiceNumber'
import { handleDatePickerValue } from '@/renderer/utils'

export const validateInvoiceData = (
    formData: SaleInvoiceFormData,
    type: 'REGISTERED' | 'WALK_IN',
    message: any
): boolean => {
    if (type === 'REGISTERED' && !formData.customerId) {
        message.error('Please select a customer')
        return false
    }

    if (formData.items.length === 0) {
        message.error('Please add at least one item')
        return false
    }

    const totalAmount = formData.items.reduce(
        (sum, item) => sum + item.totalQuantity * item.salePrice,
        0
    )
    const netAmount = totalAmount - formData.discountAmount

    if (type === 'WALK_IN' && formData.paidAmount !== netAmount) {
        message.error('Walk-in sales require full payment')
        return false
    }

    return true
}

export const submitInvoice = async (
    formData: SaleInvoiceFormData,
    setFormData: (data: SaleInvoiceFormData) => void,
    type: 'REGISTERED' | 'WALK_IN',
    userId: string,
    message: any
): Promise<boolean> => {
    try {
        if (!validateInvoiceData(formData, type, message)) {
            return false
        }

        let response

        if (type === 'REGISTERED') {
            const registeredSaleData: SaleInvoiceFormData = {
                customerId: formData.customerId!,
                date: handleDatePickerValue(formData.date),
                items: formData.items,
                discountAmount: formData.discountAmount,
                paidAmount: formData.paidAmount,
                paymentMethod: formData.paymentMethod || 'CASH',
                bankId: formData.bankId,
                source: formData.source,
                createdById: userId,
                invoiceNumber: formData.invoiceNumber
            }

            response = await saleInvoiceApi.createRegisteredSale(registeredSaleData)
        } else {
            const walkInSaleData: SaleInvoiceFormData = {
                customerName: formData.customerName || '',
                date: handleDatePickerValue(formData.date),
                items: formData.items,
                discountAmount: formData.discountAmount,
                paidAmount: formData.items.reduce(
                    (sum, item) => sum + item.totalQuantity * item.salePrice,
                    0
                ) - formData.discountAmount,
                paymentMethod: formData.paymentMethod || 'CASH',
                bankId: formData.bankId,
                source: formData.source,
                createdById: userId,
                invoiceNumber: formData.invoiceNumber
            }

            response = await saleInvoiceApi.createWalkInSale(walkInSaleData)

        }

        if (response.error.error || response.data.error) {
            throw new Error(response.error.message || response.data.error.message)
        }

        console.log('running confirmInvoiceNumber', formData.invoiceNumber)
        // Confirm invoice number after successful creation
        await confirmInvoiceNumber(formData.invoiceNumber)

        console.log('setting invoice number to empty')
        // Create a new object with all the previous data but empty invoice number
        const updatedFormData: SaleInvoiceFormData = {
            ...formData,
            invoiceNumber: ''
        }
        setFormData(updatedFormData)
        console.log('invoice number cleared')

        message.success('Sale invoice created successfully')
        return true
    } catch (error: any) {
        console.error('Error creating sale invoice:', error)
        message.error(error.message || 'Failed to create sale invoice')
        return false
    }
} 