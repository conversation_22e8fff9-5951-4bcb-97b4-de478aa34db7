export enum App_Routes {
  DASHBOARD = '/',
  USERS = '/users',
  // LICENSE = '/license',
  // PRICING = '/pricing',
  // PRICING_ID = '/pricing/:id',
  // COUPONS = '/coupons',
  // PROFILE = '/profile',
  SETTING = '/setting',
  LOGIN = '/login',
  STAFF = '/staff',
  EXPENSE = '/expense',
  PRODUCTS = '/products',
  VENDORS = '/vendors',
  CUSTOMERS = '/customers',
  CREDITORS = '/creditors',
  LEDGER = '/ledger',
  STOCK = '/stock',
  LOGOUT = '/logout',
  ANY = '*',
  PURCHASE_INVOICE = '/purchase-invoice',
  SALE_INVOICE = '/sale-invoice',
  BANKS = '/banks',
  CASH_VAULT = '/cash-vault',
  PAYMENTS = '/payments',
  LICENSE = '/license',
  RESET = '/reset',
  SMALL_COUNTER = '/small-counter',
  OPENING_STOCK = '/opening-stock',
  STOCK_RETURN = '/stock-return',
  RETURN_TO_VENDOR = '/return-to-vendor',
  REPORTS = '/reports',
  MANUAL_ENTRY = '/manual-entry'
}
