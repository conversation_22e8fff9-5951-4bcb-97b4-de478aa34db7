import { useState } from 'react'
import {
  Form,
  Input,
  InputNumber,
  Button,
  Space,
  App,
  Select,
  Card,
  Divider,
  Table,
  Tag,
  Typography,
  Tooltip,
  Modal,
  Input as AntInput
} from 'antd'
import { FaSearch, FaPlus, FaSave, FaBoxOpen, FaListAlt } from 'react-icons/fa'
import { MdOutlinePointOfSale } from 'react-icons/md'
import type { ColumnsType } from 'antd/es/table'
import { stockReturnApi } from '@/renderer/services'
import { useSelector } from 'react-redux'
import { IRootState } from '@/renderer/redux'
import { usePartyContext, useTheme, useBankContext } from '@/renderer/contexts'
import {
  DollarCircleOutlined,
  InfoCircleOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined,
  TagOutlined,
  ShopOutlined,
  UserOutlined,
  ClockCircleOutlined,
  PrinterOutlined,
  DeleteOutlined,
  BankOutlined,
  BarcodeOutlined,
  TagsOutlined
} from '@ant-design/icons'

interface SaleInvoiceItem {
  id: string
  productId: string
  productName: string
  totalQuantity: number
  salePrice: number
  total: number
  returnedQuantity?: number
  product: {
    name: string
    productId: string
    tag: string
    nature: string
    category: { name: string }
  }
}

interface SaleInvoiceDetails {
  id: string
  invoiceNumber: string
  date: Date
  customerType: 'REGISTERED' | 'WALK_IN'
  customerName?: string
  discountAmount: number
  totalAmount: number
  items: SaleInvoiceItem[]
}

const { Text } = Typography

interface AddStockReturnProps {
  setRefreshTrigger: (trigger: any) => void
}

const AddStockReturn = ({ setRefreshTrigger }: AddStockReturnProps) => {
  const { message } = App.useApp()
  const [form] = Form.useForm()
  const [invoiceForm] = Form.useForm()
  const [loading, setLoading] = useState(false)
  const [saleInvoice, setSaleInvoice] = useState<SaleInvoiceDetails | null>(null)
  const [selectedItems, setSelectedItems] = useState<any[]>([])
  const [totalReturnAmount, setTotalReturnAmount] = useState(0)
  const { banks } = useBankContext()
  const [searchProductId, setSearchProductId] = useState('')
  const [selectedItemsModalVisible, setSelectedItemsModalVisible] = useState(false)

  const user = useSelector((state: IRootState) => state.user.data)

  console.log('saleInvoice', saleInvoice)

  // Filter items based on product ID search
  const filteredItems = saleInvoice?.items.filter(
    (item) =>
      !searchProductId ||
      item.product.productId.toLowerCase().includes(searchProductId.toLowerCase())
  )

  // Get corresponding invoice items for selected products
  const getSelectedInvoiceItems = () => {
    if (!saleInvoice) return []

    return saleInvoice.items
      .filter((item) =>
        selectedItems.some((selectedItem) => selectedItem.productId === item.productId)
      )
      .map((item) => {
        const selectedItem = selectedItems.find((si) => si.productId === item.productId)
        return {
          ...item,
          returnQuantity: selectedItem?.quantity || 0,
          returnPrice: selectedItem?.purchasePrice || 0
        }
      })
  }

  const columns: ColumnsType<SaleInvoiceItem> = [
    {
      title: 'Product Details',
      dataIndex: ['product'],
      key: 'product',
      width: 400,
      render: (product) => (
        <Space direction="vertical" size="small">
          <Space wrap>
            <Tag>{product.name}</Tag>
            <Tooltip title="Product ID">
              <Tag color="blue" icon={<BarcodeOutlined />}>
                {product.productId}
              </Tag>
            </Tooltip>
            <Tooltip title="Product Tag">
              <Tag color="cyan" icon={<TagsOutlined />}>
                {product.tag || 'N/A'}
              </Tag>
            </Tooltip>
            <Tooltip title="Product Nature">
              <Tag color="purple">{product.nature || 'N/A'}</Tag>
            </Tooltip>
            <Tooltip title="Product Category">
              <Tag color="gold">{product.category.name || 'N/A'}</Tag>
            </Tooltip>
          </Space>
        </Space>
      )
    },
    {
      title: 'Original Quantity',
      dataIndex: 'totalQuantity',
      key: 'totalQuantity'
    },
    {
      title: 'Already Returned',
      dataIndex: 'returnedQuantity',
      key: 'returnedQuantity',
      render: (qty: number) => <Tag color={qty > 0 ? 'orange' : 'default'}>{qty || 0}</Tag>
    },
    {
      title: 'Available Quantity',
      key: 'availableQuantity',
      render: (_, record) => (
        <Typography.Text
          type={record.totalQuantity - (record.returnedQuantity || 0) > 0 ? 'success' : 'danger'}
        >
          {record.totalQuantity - (record.returnedQuantity || 0)}
        </Typography.Text>
      )
    },
    {
      title: 'Original Sale Price',
      dataIndex: 'salePrice',
      key: 'salePrice',
      render: (price: number) => `Rs. ${price.toFixed(2)}`
    },
    {
      title: 'Return Price',
      key: 'returnPrice',
      render: (_, record) => (
        <InputNumber
          min={0}
          onChange={(value) => handlePriceChange(record, value)}
          style={{ width: 100 }}
          prefix="Rs."
          placeholder="Enter price"
          disabled={record.totalQuantity - (record.returnedQuantity || 0) <= 0}
        />
      )
    },
    {
      title: 'Return Quantity',
      key: 'returnQuantity',
      render: (_, record) => {
        const availableQty = record.totalQuantity - (record.returnedQuantity || 0)
        return (
          <InputNumber
            min={0}
            max={availableQty}
            onChange={(value) => handleQuantityChange(record, value)}
            style={{ width: 100 }}
            disabled={availableQty <= 0}
          />
        )
      }
    }
  ]

  const selectedItemsColumns: ColumnsType<any> = [
    {
      title: 'Product Details',
      dataIndex: ['product'],
      key: 'product',
      width: 400,
      render: (product) => (
        <Space direction="vertical" size="small">
          <Space wrap>
            <Tag>{product.name}</Tag>
            <Tooltip title="Product ID">
              <Tag color="blue" icon={<BarcodeOutlined />}>
                {product.productId}
              </Tag>
            </Tooltip>
          </Space>
        </Space>
      )
    },
    {
      title: 'Return Quantity',
      dataIndex: 'returnQuantity',
      key: 'returnQuantity'
    },
    {
      title: 'Return Price',
      dataIndex: 'returnPrice',
      key: 'returnPrice',
      render: (price: number) => `Rs. ${price.toFixed(2)}`
    },
    {
      title: 'Total',
      key: 'total',
      render: (_, record) => `Rs. ${(record.returnQuantity * record.returnPrice).toFixed(2)}`
    }
  ]

  const handlePriceChange = (item: SaleInvoiceItem, price: number | null) => {
    const existingIndex = selectedItems.findIndex((i) => i.productId === item.productId)

    // If price is null or 0, and either no quantity is set or quantity is 0, remove item
    if (!price || price === 0) {
      if (existingIndex >= 0) {
        const existingItem = selectedItems[existingIndex]
        if (!existingItem.quantity || existingItem.quantity === 0) {
          const newItems = selectedItems.filter((_, index) => index !== existingIndex)
          setSelectedItems(newItems)
          setTotalReturnAmount(
            newItems.reduce(
              (sum, item) => sum + (item.quantity || 0) * (item.purchasePrice || 0),
              0
            )
          )
          return
        }
      }
    }

    const newItem = {
      productId: item.productId,
      purchasePrice: price || 0,
      salePrice: item.salePrice,
      originalInvoiceId: saleInvoice?.id,
      originalInvoiceType: saleInvoice?.customerType,
      quantity: existingIndex >= 0 ? selectedItems[existingIndex].quantity || 0 : 0
    }

    if (existingIndex >= 0) {
      const newItems = [...selectedItems]
      newItems[existingIndex] = {
        ...newItems[existingIndex],
        purchasePrice: price || 0
      }
      setSelectedItems(newItems)
      setTotalReturnAmount(
        newItems.reduce((sum, item) => sum + (item.quantity || 0) * (item.purchasePrice || 0), 0)
      )
    } else if (price && price > 0) {
      setSelectedItems([...selectedItems, newItem])
    }
  }

  const handleQuantityChange = (item: SaleInvoiceItem, quantity: number | null) => {
    const existingIndex = selectedItems.findIndex((i) => i.productId === item.productId)

    // If quantity is null or 0, and either no price is set or price is 0, remove item
    if (!quantity || quantity === 0) {
      if (existingIndex >= 0) {
        const existingItem = selectedItems[existingIndex]
        if (!existingItem.purchasePrice || existingItem.purchasePrice === 0) {
          const newItems = selectedItems.filter((_, index) => index !== existingIndex)
          setSelectedItems(newItems)
          setTotalReturnAmount(
            newItems.reduce(
              (sum, item) => sum + (item.quantity || 0) * (item.purchasePrice || 0),
              0
            )
          )
          return
        }
      }
    }

    const newItem = {
      productId: item.productId,
      quantity: quantity || 0,
      purchasePrice: existingIndex >= 0 ? selectedItems[existingIndex].purchasePrice || 0 : 0,
      salePrice: item.salePrice,
      originalInvoiceId: saleInvoice?.id,
      originalInvoiceType: saleInvoice?.customerType
    }

    if (existingIndex >= 0) {
      const newItems = [...selectedItems]
      newItems[existingIndex] = {
        ...newItems[existingIndex],
        quantity: quantity || 0
      }
      setSelectedItems(newItems)
      setTotalReturnAmount(
        newItems.reduce((sum, item) => sum + (item.quantity || 0) * (item.purchasePrice || 0), 0)
      )
    } else if (quantity && quantity > 0) {
      setSelectedItems([...selectedItems, newItem])
    }
  }

  const fetchSaleInvoice = async (values: any) => {
    setLoading(true)
    const response = await stockReturnApi.getSaleInvoiceByNumber({
      invoiceNumber: values.invoiceNumber,
      customerType: values.customerType
    })
    setLoading(false)
    if (response.error.error || response.data.error) {
      message.error(response.error.message || response.data.error.message)
      return
    }
    setSaleInvoice(response.data.data)
    setSelectedItems([])
  }

  const handleSubmit = async () => {
    try {
      // Validate items have both price and quantity
      const invalidItems = selectedItems.filter(
        (item) =>
          !item.purchasePrice || item.purchasePrice === 0 || !item.quantity || item.quantity === 0
      )

      if (invalidItems.length > 0) {
        message.error('Please enter both return price and quantity for all selected items')
        return
      }

      if (!selectedItems.length) {
        message.error('Please select items to return')
        return
      }

      if (saleInvoice?.customerType === 'WALK_IN') {
        const values = await form.validateFields()
        if (!values.source) {
          message.error('Please select payment source for walk-in sale return')
          return
        }
        if (values.source === 'BANK' && !values.bankId) {
          message.error('Please select bank for bank payment')
          return
        }
      }

      setLoading(true)
      const values = await form.validateFields()
      const response = await stockReturnApi.processStockReturn({
        items: selectedItems,
        originalInvoiceNumber: saleInvoice!.invoiceNumber,
        adminId: user?.id || '',
        ...(saleInvoice?.customerType === 'WALK_IN' || values.source
          ? {
              paymentDetails: {
                source: values.source,
                bankId: values.source === 'BANK' ? values.bankId : undefined
              }
            }
          : {})
      })

      setLoading(false)
      if (response.error.error || response.data.error) {
        message.error(response.error.message || response.data.error.message)
        return
      }
      message.success('Stock return processed successfully')
      setSaleInvoice(null)
      setSelectedItems([])
      setTotalReturnAmount(0)
      form.resetFields()
      invoiceForm.resetFields()
      setRefreshTrigger((prev: number) => prev + 1)
    } catch (error: any) {
      message.error(error.message)
      setLoading(false)
    }
  }

  return (
    <div className="space-y-6">
      <Card className="shadow-md">
        <div className="mb-4 flex items-center gap-2">
          <MdOutlinePointOfSale className="text-2xl text-indigo-600" />
          <span className="text-lg font-semibold">Find Sale Invoice</span>
        </div>
        <Form form={invoiceForm} layout="inline" onFinish={fetchSaleInvoice}>
          <Form.Item
            name="invoiceNumber"
            rules={[{ required: true, message: 'Please enter invoice number' }]}
          >
            <Input
              prefix={<FaSearch className="text-gray-400" />}
              placeholder="Enter Invoice Number"
            />
          </Form.Item>
          <Form.Item
            name="customerType"
            rules={[{ required: true, message: 'Please select customer type' }]}
          >
            <Select
              style={{ width: 200 }}
              placeholder="Select Customer Type"
              options={[
                { label: 'Registered Customer', value: 'REGISTERED' },
                { label: 'Walk-in Customer', value: 'WALK_IN' }
              ]}
            />
          </Form.Item>
          <Form.Item>
            <Button type="primary" htmlType="submit" icon={<FaSearch />} loading={loading}>
              Find Invoice
            </Button>
          </Form.Item>
        </Form>
      </Card>

      {saleInvoice && (
        <Card className="shadow-md">
          <div className="mb-4 flex items-center gap-2">
            <FaBoxOpen className="text-2xl text-indigo-600" />
            <span className="text-lg font-semibold">Invoice Details</span>
          </div>
          <div className="mb-4 grid grid-cols-2 gap-4">
            <div>
              <p>
                <strong>Invoice Number:</strong> {saleInvoice.invoiceNumber}
              </p>
              <p>
                <strong>Customer:</strong> {saleInvoice.customerName || 'Walk-in Customer'}
              </p>
              <p>
                <strong>Date:</strong> {new Date(saleInvoice.date).toLocaleDateString()}
              </p>
            </div>
            <div>
              <p>
                <strong>Total Amount:</strong> Rs. {saleInvoice.totalAmount}
              </p>
              <p>
                <strong>Discount:</strong> Rs. {saleInvoice.discountAmount}
              </p>
              <p>
                <strong>Customer Type:</strong> {saleInvoice.customerType}
              </p>
            </div>
          </div>
          <Divider />

          <div className="mb-4 flex items-center justify-between">
            <Space>
              <AntInput
                placeholder="Search by Product ID"
                prefix={<FaSearch />}
                value={searchProductId}
                onChange={(e) => setSearchProductId(e.target.value)}
                style={{ width: 250 }}
                allowClear
              />
            </Space>
            <Button
              type="primary"
              icon={<FaListAlt />}
              onClick={() => setSelectedItemsModalVisible(true)}
              disabled={selectedItems.length === 0}
            >
              View Selected Items ({selectedItems.length})
            </Button>
          </div>

          <Table
            sticky
            virtual
            columns={columns}
            dataSource={filteredItems}
            rowKey="id"
            pagination={false}
          />

          {(saleInvoice.customerType === 'WALK_IN' || totalReturnAmount > 0) && (
            <>
              <Divider />
              <div className="mb-4">
                <div className="mb-4 flex items-center gap-2">
                  <DollarCircleOutlined className="text-2xl text-indigo-600" />
                  <span className="text-lg font-semibold">
                    Payment Details{' '}
                    {saleInvoice.customerType === 'WALK_IN' ? '(Required)' : '(Optional)'}
                  </span>
                </div>
                <div className="mb-4">
                  <Typography.Text strong>Total Return Amount: </Typography.Text>
                  <Typography.Text className="text-lg">
                    Rs. {totalReturnAmount.toFixed(2)}
                  </Typography.Text>
                </div>
                <Form form={form} layout="vertical" className="grid grid-cols-2 gap-4">
                  <Form.Item
                    name="source"
                    label="Payment Source"
                    rules={[
                      {
                        required: saleInvoice.customerType === 'WALK_IN',
                        message: 'Please select payment source'
                      }
                    ]}
                  >
                    <Select
                      placeholder="Select payment source"
                      allowClear
                      options={[
                        { label: 'Small Counter', value: 'SMALL_COUNTER' },
                        { label: 'Cash Vault', value: 'CASH_VAULT' },
                        { label: 'Bank', value: 'BANK' }
                      ]}
                    />
                  </Form.Item>
                  <Form.Item
                    noStyle
                    shouldUpdate={(prevValues, currentValues) =>
                      prevValues?.source !== currentValues?.source
                    }
                  >
                    {({ getFieldValue }) =>
                      getFieldValue('source') === 'BANK' ? (
                        <Form.Item
                          name="bankId"
                          label="Bank"
                          rules={[
                            {
                              required: true,
                              message: 'Please select bank'
                            }
                          ]}
                        >
                          <Select placeholder="Select bank" options={banks} />
                        </Form.Item>
                      ) : null
                    }
                  </Form.Item>
                </Form>
              </div>
            </>
          )}

          <div className="mt-4 flex justify-end">
            <Button
              type="primary"
              icon={<FaSave />}
              onClick={handleSubmit}
              loading={loading}
              disabled={!selectedItems.length}
            >
              Process Return
            </Button>
          </div>
        </Card>
      )}

      {/* Selected Items Modal */}
      <Modal
        title="Selected Items for Return"
        open={selectedItemsModalVisible}
        onCancel={() => setSelectedItemsModalVisible(false)}
        width={1000}
        footer={[
          <Button key="close" onClick={() => setSelectedItemsModalVisible(false)}>
            Close
          </Button>
        ]}
      >
        <Table
          columns={selectedItemsColumns}
          dataSource={getSelectedInvoiceItems()}
          rowKey="id"
          pagination={false}
          summary={() => (
            <Table.Summary fixed>
              <Table.Summary.Row>
                <Table.Summary.Cell index={0} colSpan={3} align="right">
                  <Typography.Text strong>Total Return Amount:</Typography.Text>
                </Table.Summary.Cell>
                <Table.Summary.Cell index={1}>
                  <Typography.Text strong>Rs. {totalReturnAmount.toFixed(2)}</Typography.Text>
                </Table.Summary.Cell>
              </Table.Summary.Row>
            </Table.Summary>
          )}
        />
      </Modal>
    </div>
  )
}

export default AddStockReturn
