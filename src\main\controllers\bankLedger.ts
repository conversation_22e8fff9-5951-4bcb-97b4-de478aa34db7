import { IRequest } from '../../common';
import { bankLedgerService } from '../services';

class BankLedgerController {
    getBankLedgerEntries = async (_event: Electron.IpcMainInvokeEvent, req: IRequest) => {
        const { bankId, page, limit, startDate, endDate, creditOrDebit, referenceType, status } = req.body || {};
        return await bankLedgerService.getBankLedgerEntries({
            bankId,
            page,
            limit,
            startDate: startDate ? new Date(startDate) : undefined,
            endDate: endDate ? new Date(endDate) : undefined,
            creditOrDebit,
            referenceType,
            status
        });
    };

    getBankTransactionSummary = async (_event: Electron.IpcMainInvokeEvent, req: IRequest) => {
        const { bankId, startDate, endDate } = req.body || {};

        if (!startDate || !endDate) {
            throw new Error("Start date and end date are required for transaction summary");
        }

        return await bankLedgerService.getBankTransactionSummary({
            bankId,
            startDate: new Date(startDate),
            endDate: new Date(endDate)
        });
    };

    getDailyBankLedger = async (_event: Electron.IpcMainInvokeEvent, req: IRequest) => {
        const { bankId, date } = req.body || {};

        if (!date) {
            throw new Error("Date is required for daily bank ledger");
        }

        return await bankLedgerService.getDailyBankLedger({
            bankId,
            date: new Date(date)
        });
    };

    reconcileBank = async (_event: Electron.IpcMainInvokeEvent, req: IRequest) => {
        const { bankId } = req.body || {};

        if (!bankId) {
            throw new Error("Bank ID is required for reconciliation");
        }

        return await bankLedgerService.reconcileBank(bankId);
    };

    searchBankTransactions = async (_event: Electron.IpcMainInvokeEvent, req: IRequest) => {
        const { bankId, searchQuery, transactionType, page, limit } = req.body || {};

        if (!searchQuery) {
            throw new Error("Search query is required");
        }

        return await bankLedgerService.searchBankTransactions({
            bankId,
            searchQuery,
            transactionType,
            page,
            limit
        });
    };

    getDailyClosingBalances = async (_event: Electron.IpcMainInvokeEvent, req: IRequest) => {
        const { bankId, startDate, endDate } = req.body || {};

        if (!startDate || !endDate) {
            throw new Error("Start date and end date are required for daily closing balances");
        }

        return await bankLedgerService.getDailyClosingBalances({
            bankId,
            startDate: new Date(startDate),
            endDate: new Date(endDate)
        });
    };
}

export const bankLedgerController = new BankLedgerController();
