// import { But<PERSON>, Drawer, Form, Input, InputNumber, Space, message } from 'antd'
// import { useState } from 'react'
// import { Gi<PERSON>ay<PERSON><PERSON> } from 'react-icons/gi'
// import { cashVaultApi } from '@/renderer/services'

// interface WithdrawDrawerProps {
//   open: boolean
//   onClose: () => void
//   onBalanceUpdate: () => void
//   userId: string
//   currentBalance: number
// }

// interface WithdrawFormValues {
//   amount: number
//   description: string
// }

// export const WithdrawDrawer = ({
//   open,
//   onClose,
//   onBalanceUpdate,
//   userId,
//   currentBalance
// }: WithdrawDrawerProps) => {
//   const [form] = Form.useForm()
//   const [loading, setLoading] = useState(false)

//   const handleWithdraw = async (values: WithdrawFormValues) => {
//     setLoading(true)
//     const response = await cashVaultApi.withdrawFromVault({
//       amount: values.amount,
//       description: values.description,
//       adminId: userId
//     })
//     setLoading(false)

//     if (response.error.error || response.data.error) {
//       message.error(response.error.message || response.data.error.message)
//       return
//     }

//     message.success('Amount withdrawn successfully')
//     form.resetFields()
//     onBalanceUpdate()
//     onClose()
//   }

//   return (
//     <Drawer
//       title="Withdraw from Vault"
//       placement="right"
//       onClose={onClose}
//       open={open}
//       width={400}
//       extra={
//         <Space>
//           <Button onClick={onClose}>Cancel</Button>
//           <Button
//             type="primary"
//             icon={<GiPayMoney />}
//             loading={loading}
//             onClick={() => form.submit()}
//           >
//             Withdraw
//           </Button>
//         </Space>
//       }
//     >
//       <Form form={form} layout="vertical" onFinish={handleWithdraw}>
//         <Form.Item
//           label="Amount"
//           name="amount"
//           rules={[
//             { required: true, message: 'Please enter amount' },
//             {
//               type: 'number',
//               min: 0.01,
//               message: 'Amount must be greater than 0'
//             },
//             {
//               type: 'number',
//               max: currentBalance,
//               message: 'Amount cannot exceed current balance'
//             }
//           ]}
//         >
//           <InputNumber
//             style={{ width: '100%' }}
//             prefix="Rs. "
//             precision={2}
//             placeholder="Enter amount to withdraw"
//           />
//         </Form.Item>

//         <Form.Item
//           label="Description"
//           name="description"
//           rules={[{ required: true, message: 'Please enter description' }]}
//         >
//           <Input.TextArea rows={4} placeholder="Enter transaction description" />
//         </Form.Item>
//       </Form>
//     </Drawer>
//   )
// }
