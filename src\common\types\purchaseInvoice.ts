import { Status, Product } from '@prisma/client'

export interface PurchaseInvoiceItem {
    productId: string
    quantity: number
    purchasePrice: number
    total: number
}

export interface CreatePurchaseInvoiceData {
    invoiceNumber: string
    vendorId: string
    totalAmount: number
    paidAmount: number
    previousBalance: number
    newBalance: number
    date: Date
    createdById: string
    paymentMethod?: 'cash' | 'vault' | 'bank' | null
    bankId?: string | null
    items: PurchaseInvoiceItem[]
}

export interface GetPurchaseInvoicesParams {
    page?: number
    limit?: number
    search?: string
    vendorId?: string
    startDate?: Date
    endDate?: Date
    status?: Status
}

export interface PurchaseInvoiceData {
    id: string
    invoiceNumber: string
    date: string
    vendor: {
        id: string
        name: string
    }
    totalAmount: number
    paidAmount: number
    items: Array<{
        id: string
        product: Product
        quantity: number
        purchasePrice: number
        total: number
    }>
    status: Status
    voidingReason?: string | null
    voidedBy?: {
        name: string
    } | null
    voidedAt?: string | null
    createdBy?: {
        name: string
    } | null
}

export interface PurchaseInvoiceResponse {
    invoices: PurchaseInvoiceData[]
    total: number
    page: number
    totalPages: number
}

export interface VoidPurchaseInvoiceParams {
    id: string
    adminId: string
    reason: string
}
