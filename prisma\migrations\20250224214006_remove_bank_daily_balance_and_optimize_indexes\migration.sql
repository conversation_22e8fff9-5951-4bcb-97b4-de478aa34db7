/*
  Warnings:

  - You are about to drop the `BankDailyBalance` table. If the table is not empty, all the data it contains will be lost.

*/
-- DropForeignKey
ALTER TABLE "BankDailyBalance" DROP CONSTRAINT "BankDailyBalance_bankId_fkey";

-- DropTable
DROP TABLE "BankDailyBalance";

-- CreateIndex
CREATE INDEX "Admin_role_idx" ON "Admin"("role");

-- CreateIndex
CREATE INDEX "Admin_isActive_idx" ON "Admin"("isActive");

-- CreateIndex
CREATE INDEX "Admin_username_idx" ON "Admin"("username");

-- CreateIndex
CREATE INDEX "Banks_isActive_idx" ON "Banks"("isActive");

-- CreateIndex
CREATE INDEX "Banks_name_idx" ON "Banks"("name");

-- CreateIndex
CREATE INDEX "Banks_balance_idx" ON "Banks"("balance");

-- CreateIndex
CREATE INDEX "CashVault_balance_idx" ON "CashVault"("balance");

-- CreateIndex
CREATE INDEX "Expense_category_idx" ON "Expense"("category");

-- CreateIndex
CREATE INDEX "Expense_date_idx" ON "Expense"("date");

-- CreateIndex
CREATE INDEX "Expense_status_idx" ON "Expense"("status");

-- CreateIndex
CREATE INDEX "InvoiceNumber_invoiceNumber_idx" ON "InvoiceNumber"("invoiceNumber");

-- CreateIndex
CREATE INDEX "Ledger_date_status_idx" ON "Ledger"("date", "status");

-- CreateIndex
CREATE INDEX "Ledger_referenceType_idx" ON "Ledger"("referenceType");

-- CreateIndex
CREATE INDEX "Ledger_paymentRef_idx" ON "Ledger"("paymentRef");

-- CreateIndex
CREATE INDEX "Ledger_date_idx" ON "Ledger"("date");

-- CreateIndex
CREATE INDEX "Party_type_idx" ON "Party"("type");

-- CreateIndex
CREATE INDEX "Party_name_idx" ON "Party"("name");

-- CreateIndex
CREATE INDEX "Party_currentBalance_idx" ON "Party"("currentBalance");

-- CreateIndex
CREATE INDEX "Payments_partyId_idx" ON "Payments"("partyId");

-- CreateIndex
CREATE INDEX "Payments_type_idx" ON "Payments"("type");

-- CreateIndex
CREATE INDEX "Payments_paymentMethod_idx" ON "Payments"("paymentMethod");

-- CreateIndex
CREATE INDEX "Payments_status_idx" ON "Payments"("status");

-- CreateIndex
CREATE INDEX "Payments_date_idx" ON "Payments"("date");

-- CreateIndex
CREATE INDEX "Payments_sourceLocation_idx" ON "Payments"("sourceLocation");

-- CreateIndex
CREATE INDEX "Payments_destinationLocation_idx" ON "Payments"("destinationLocation");

-- CreateIndex
CREATE INDEX "Product_quantityInStock_idx" ON "Product"("quantityInStock");

-- CreateIndex
CREATE INDEX "Product_barcode_idx" ON "Product"("barcode");

-- CreateIndex
CREATE INDEX "Product_name_idx" ON "Product"("name");

-- CreateIndex
CREATE INDEX "Product_productId_idx" ON "Product"("productId");

-- CreateIndex
CREATE INDEX "PurchaseInvoice_vendorId_idx" ON "PurchaseInvoice"("vendorId");

-- CreateIndex
CREATE INDEX "PurchaseInvoice_status_idx" ON "PurchaseInvoice"("status");

-- CreateIndex
CREATE INDEX "PurchaseInvoice_date_idx" ON "PurchaseInvoice"("date");

-- CreateIndex
CREATE INDEX "PurchaseInvoice_type_idx" ON "PurchaseInvoice"("type");

-- CreateIndex
CREATE INDEX "PurchaseInvoice_invoiceNumber_idx" ON "PurchaseInvoice"("invoiceNumber");

-- CreateIndex
CREATE INDEX "PurchaseInvoice_totalAmount_idx" ON "PurchaseInvoice"("totalAmount");

-- CreateIndex
CREATE INDEX "PurchaseItem_purchaseInvoiceId_idx" ON "PurchaseItem"("purchaseInvoiceId");

-- CreateIndex
CREATE INDEX "PurchaseItem_productId_idx" ON "PurchaseItem"("productId");

-- CreateIndex
CREATE INDEX "SaleInvoice_status_idx" ON "SaleInvoice"("status");

-- CreateIndex
CREATE INDEX "SaleInvoice_date_idx" ON "SaleInvoice"("date");

-- CreateIndex
CREATE INDEX "SaleInvoice_invoiceNumber_idx" ON "SaleInvoice"("invoiceNumber");

-- CreateIndex
CREATE INDEX "SaleInvoice_totalAmount_idx" ON "SaleInvoice"("totalAmount");

-- CreateIndex
CREATE INDEX "SaleItem_saleInvoiceId_idx" ON "SaleItem"("saleInvoiceId");

-- CreateIndex
CREATE INDEX "SaleItem_productId_idx" ON "SaleItem"("productId");

-- CreateIndex
CREATE INDEX "SmallCounter_cashInShop_idx" ON "SmallCounter"("cashInShop");

-- CreateIndex
CREATE INDEX "Stock_vendorId_idx" ON "Stock"("vendorId");

-- CreateIndex
CREATE INDEX "Stock_purchaseInvoiceId_idx" ON "Stock"("purchaseInvoiceId");

-- CreateIndex
CREATE INDEX "StockEntry_stockId_idx" ON "StockEntry"("stockId");

-- CreateIndex
CREATE INDEX "StockEntry_saleItemId_idx" ON "StockEntry"("saleItemId");

-- CreateIndex
CREATE INDEX "StockEntry_walkInSaleItemId_idx" ON "StockEntry"("walkInSaleItemId");

-- CreateIndex
CREATE INDEX "WalkInSaleInvoice_status_idx" ON "WalkInSaleInvoice"("status");

-- CreateIndex
CREATE INDEX "WalkInSaleInvoice_date_idx" ON "WalkInSaleInvoice"("date");

-- CreateIndex
CREATE INDEX "WalkInSaleInvoice_invoiceNumber_idx" ON "WalkInSaleInvoice"("invoiceNumber");

-- CreateIndex
CREATE INDEX "WalkInSaleInvoice_totalAmount_idx" ON "WalkInSaleInvoice"("totalAmount");

-- CreateIndex
CREATE INDEX "WalkInSaleItem_saleInvoiceId_idx" ON "WalkInSaleItem"("saleInvoiceId");

-- CreateIndex
CREATE INDEX "WalkInSaleItem_productId_idx" ON "WalkInSaleItem"("productId");
