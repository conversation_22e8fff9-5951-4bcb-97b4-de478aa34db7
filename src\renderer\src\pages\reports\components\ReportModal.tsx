import { Modal, Select, DatePicker, Button, Radio } from 'antd'
import { useState } from 'react'
import { ReportModalProps } from '../types'
import { ReportFormat } from '@/common/types'
import { useTheme } from '@/renderer/contexts'
import FinancialOverview from './reports/FinancialOverview'
import InventoryValuation from './reports/InventoryValuation'
import CashFlow from './reports/CashFlow'
import SalesPerformance from './reports/SalesPerformance'
import CustomerAnalytics from './reports/CustomerAnalytics'
import AgeingReport from './reports/AgeingReport'
import DailyOperations from './reports/DailyOperations'
import AuditReport from './reports/AuditReport'

const { RangePicker } = DatePicker

const ReportModal = ({ report, open, onClose }: ReportModalProps) => {
  const { isDarkMode } = useTheme()
  const [format, setFormat] = useState<ReportFormat>(ReportFormat.SCREEN)
  const [dateRange, setDateRange] = useState<[Date, Date] | undefined>()
  const [partyType, setPartyType] = useState<'CUSTOMER' | 'VENDOR'>('CUSTOMER')
  const [shouldGenerate, setShouldGenerate] = useState(false)

  // Determine if this report needs date range
  const needsDateRange =
    report &&
    [
      'cash-flow',
      'sales-performance',
      'customer-analytics',
      'ageing',
      'daily-operations',
      'audit'
    ].includes(report.id)

  // Determine if this report needs party type
  const needsPartyType = report?.id === 'ageing'

  const handleGenerateReport = () => {
    setShouldGenerate(true)
  }

  const renderReport = () => {
    if (!report) return null

    switch (report.id) {
      case 'financial-overview':
        return (
          <FinancialOverview
            format={format}
            dateRange={dateRange}
            shouldGenerate={shouldGenerate}
            onGenerateComplete={() => setShouldGenerate(false)}
            currentReportId={report.id}
          />
        )
      case 'inventory-valuation':
        return (
          <InventoryValuation
            format={format}
            shouldGenerate={shouldGenerate}
            onGenerateComplete={() => setShouldGenerate(false)}
            currentReportId={report.id}
          />
        )
      case 'cash-flow':
        return (
          <CashFlow
            format={format}
            dateRange={dateRange}
            shouldGenerate={shouldGenerate}
            onGenerateComplete={() => setShouldGenerate(false)}
            currentReportId={report.id}
          />
        )
      case 'sales-performance':
        return (
          <SalesPerformance
            format={format}
            dateRange={dateRange}
            shouldGenerate={shouldGenerate}
            onGenerateComplete={() => setShouldGenerate(false)}
            currentReportId={report.id}
          />
        )
      case 'customer-analytics':
        return (
          <CustomerAnalytics
            format={format}
            dateRange={dateRange}
            shouldGenerate={shouldGenerate}
            onGenerateComplete={() => setShouldGenerate(false)}
            currentReportId={report.id}
          />
        )
      case 'ageing-report':
        return (
          <AgeingReport
            format={format}
            dateRange={dateRange}
            shouldGenerate={shouldGenerate}
            onGenerateComplete={() => setShouldGenerate(false)}
            currentReportId={report.id}
            partyType={partyType}
          />
        )
      case 'daily-operations':
        return (
          <DailyOperations
            format={format}
            dateRange={dateRange}
            shouldGenerate={shouldGenerate}
            onGenerateComplete={() => setShouldGenerate(false)}
            currentReportId={report.id}
          />
        )
      case 'audit-report':
        return (
          <AuditReport
            format={format}
            dateRange={dateRange}
            shouldGenerate={shouldGenerate}
            onGenerateComplete={() => setShouldGenerate(false)}
            currentReportId={report.id}
          />
        )
      default:
        return (
          <div className={`mt-6 rounded-lg p-4 ${isDarkMode ? 'bg-gray-800' : 'bg-gray-100'}`}>
            <p className={`text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
              Report implementation coming soon.
            </p>
          </div>
        )
    }
  }

  return (
    <Modal
      title={
        <span className={isDarkMode ? 'text-white' : 'text-gray-800'}>
          {report ? `Generate ${report.title}` : 'Generate Report'}
        </span>
      }
      open={open}
      onCancel={onClose}
      footer={null}
      width={1200}
    >
      {report && (
        <div className="space-y-6">
          {/* Controls Section */}
          <div className="flex items-end gap-4">
            <div className="flex-1">
              <div className="flex flex-wrap gap-4">
                <div className="min-w-[200px] flex-1">
                  <label className={`mb-2 block ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                    Report Format
                  </label>
                  <Select value={format} onChange={setFormat} className="w-full">
                    {Object.values(ReportFormat).map((fmt) => (
                      <Select.Option key={fmt} value={fmt}>
                        {fmt}
                      </Select.Option>
                    ))}
                  </Select>
                </div>

                {needsDateRange && (
                  <div className="min-w-[200px] flex-1">
                    <label
                      className={`mb-2 block ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}
                    >
                      Date Range
                    </label>
                    <RangePicker
                      className="w-full"
                      onChange={(dates) => {
                        if (dates && dates[0] && dates[1]) {
                          setDateRange([dates[0].toDate(), dates[1].toDate()])
                        } else {
                          setDateRange(undefined)
                        }
                      }}
                    />
                  </div>
                )}

                {needsPartyType && (
                  <div className="min-w-[200px] flex-1">
                    <label
                      className={`mb-2 block ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}
                    >
                      Party Type
                    </label>
                    <Radio.Group value={partyType} onChange={(e) => setPartyType(e.target.value)}>
                      <Radio.Button value="CUSTOMER">Customer</Radio.Button>
                      <Radio.Button value="VENDOR">Vendor</Radio.Button>
                    </Radio.Group>
                  </div>
                )}
              </div>
            </div>
            <Button type="primary" onClick={handleGenerateReport}>
              Generate Report
            </Button>
          </div>

          {/* Report Content */}
          <div className="mt-6">{renderReport()}</div>
        </div>
      )}
    </Modal>
  )
}

export default ReportModal
