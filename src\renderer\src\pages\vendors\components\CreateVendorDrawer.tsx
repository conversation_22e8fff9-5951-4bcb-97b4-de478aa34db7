import { Form, Input, Drawer, But<PERSON>, <PERSON>put<PERSON><PERSON><PERSON>, App } from 'antd'
import { partyApi } from '@/renderer/services'
import { usePartyContext } from '@/renderer/contexts'
import { useSelector } from 'react-redux'
import { IRootState } from '@/renderer/redux'

interface CreateVendorDrawerProps {
  open: boolean
  onClose: () => void
  onVendorCreated: () => void
}

export const CreateVendorDrawer = ({ open, onClose, onVendorCreated }: CreateVendorDrawerProps) => {
  const [form] = Form.useForm()

  const { refreshVendors } = usePartyContext()
  const { message } = App.useApp()
  const user = useSelector((state: IRootState) => state.user.data)

  const handleSubmit = async (values: any) => {
    console.log(values)

    const response = await partyApi.createParty({
      ...values,
      type: 'VENDOR',
      createdById: user?.id
    })

    if (response.error.error || response.data.error) {
      message.error(response.error.message || response.data.error.message)
      return
    }

    message.success('Vendor created successfully')
    refreshVendors()
    form.resetFields()
    onVendorCreated()
  }

  return (
    <Drawer
      title="Add New Vendor"
      placement="right"
      onClose={onClose}
      open={open}
      width={500}
      extra={
        <Button type="primary" onClick={() => form.submit()}>
          Create
        </Button>
      }
    >
      <Form form={form} layout="vertical" onFinish={handleSubmit}>
        <Form.Item
          name="name"
          label="Vendor Name"
          rules={[{ required: true, message: 'Please enter vendor name' }]}
        >
          <Input />
        </Form.Item>

        <Form.Item name="openingBalance" label="Opening Balance">
          <InputNumber className="w-full" addonBefore="PKR" />
        </Form.Item>

        <Form.Item name="contact" label="Contact Person">
          <Input />
        </Form.Item>

        <Form.Item name="phoneNumber" label="Phone Number">
          <Input />
        </Form.Item>

        <Form.Item name="address" label="Address">
          <Input.TextArea rows={2} />
        </Form.Item>
      </Form>
    </Drawer>
  )
}
