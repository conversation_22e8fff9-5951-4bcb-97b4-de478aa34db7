.create-purchase-invoice {
    padding: 24px;
  
    .animated-section {
      transition: max-height 0.5s ease-in-out, opacity 0.5s ease-in-out, margin 0.5s ease-in-out, padding 0.5s ease-in-out;
      overflow: hidden;
      max-height: 0;
      opacity: 0;
      margin: 0;
      padding: 0;
      
      &.visible {
        max-height: 500px;
        opacity: 1;
        margin: 16px 0;
        padding: 16px;
      }

      .ant-card {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      }
    }
  
    .invoice-data-entry {
      display: flex;
      align-items: center;
      margin: 16px 0;
      width: 100%;
    }
  
    .grand-total {
      text-align: right;
      font-size: 18px;
      font-weight: bold;
      margin: 16px 0;
    }
  }


  .delete-icon-wrapper {
    position: relative;
    cursor: pointer;
    display: inline-block;
    
    .delete-icon, .delete-icon-hover {
      color: #ff4d4f;
      font-size: 16px;
      transition: all 0.3s ease;
    }
    
    .delete-icon-hover {
      position: absolute;
      left: 0;
      opacity: 0;
    }
    
    &:hover {
      .delete-icon {
        opacity: 0;
      }
      
      .delete-icon-hover {
        opacity: 1;
      }
    }
  }


  .invoice-totals {
    margin: 24px 0;
    max-width: 200px;
    margin-left: auto;
    
    .ant-form-item {
      margin-bottom: 12px;
    }
    
    .ant-input-number {
      width: 100%;
    }
  }