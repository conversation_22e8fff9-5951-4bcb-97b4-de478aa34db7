import { Typo<PERSON>, Space, Button, Alert, Skeleton, Statistic, Row, Col, Progress } from 'antd'
import { useApi } from '@/renderer/hooks'
import { dashboardApi } from '@/renderer/services'
import { StockValueOverview as StockValueOverviewType } from '@/common/types/dashBoard'
import { MdInventory2, MdRefresh } from 'react-icons/md'
import { formatCurrency } from '@/renderer/utils'
import { useEffect } from 'react'
import { useTheme } from '@/renderer/contexts'

const { Title } = Typography

const StockValueOverview = () => {
  const { isDarkMode } = useTheme()

  const {
    data,
    isLoading,
    error,
    request: fetchStockValue
  } = useApi<StockValueOverviewType, []>(dashboardApi.getStockValueOverview)

  useEffect(() => {
    fetchStockValue()
  }, [])

  if (isLoading) {
    return (
      <div className="flex flex-col gap-4">
        <Space className="flex justify-between">
          <Space>
            <MdInventory2 className="text-2xl text-indigo-600" />
            <Title level={5} className="!mb-0">
              Stock Value Overview
            </Title>
          </Space>
          <Button icon={<MdRefresh />} loading />
        </Space>
        <Skeleton active paragraph={{ rows: 4 }} />
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex flex-col gap-4">
        <Space className="flex justify-between">
          <Space>
            <MdInventory2 className="text-2xl text-indigo-600" />
            <Title level={5} className="!mb-0">
              Stock Value Overview
            </Title>
          </Space>
          <Button icon={<MdRefresh />} onClick={() => fetchStockValue()} />
        </Space>
        <Alert
          message="Error"
          description="Failed to load stock value data"
          type="error"
          showIcon
        />
      </div>
    )
  }

  if (!data) return null

  const potentialProfit = data.sale.totalValue - data.purchase.totalValue
  const profitMargin = (potentialProfit / data.purchase.totalValue) * 100

  return (
    <div className="flex flex-col gap-4">
      <Space className="flex justify-between">
        <Space>
          <MdInventory2 className="text-2xl text-indigo-600" />
          <Title level={5} className="!mb-0">
            Stock Value Overview
          </Title>
        </Space>
        <Button icon={<MdRefresh />} onClick={() => fetchStockValue()} size="small" />
      </Space>

      <Row gutter={[16, 16]}>
        <Col span={12}>
          <div
            className={`rounded-lg border shadow-md ${isDarkMode ? 'border-gray-800 bg-gray-900' : 'border-gray-200 bg-slate-50'} p-4`}
          >
            <span className="text-sm font-medium">Purchase Value</span>
            <Statistic
              value={data.purchase.totalValue}
              formatter={(value) => formatCurrency(value as number)}
              valueStyle={{ fontSize: '1.25rem' }}
            />
            <div className="mt-2 text-xs text-gray-500">
              Avg. {formatCurrency(data.purchase.averageValue)} per product
            </div>
          </div>
        </Col>
        <Col span={12}>
          <div
            className={`rounded-lg border shadow-md ${isDarkMode ? 'border-gray-800 bg-gray-900' : 'border-gray-200 bg-slate-50'} p-4`}
          >
            <span className="text-sm font-medium">Sale Value</span>
            <Statistic
              value={data.sale.totalValue}
              formatter={(value) => formatCurrency(value as number)}
              valueStyle={{ fontSize: '1.25rem' }}
            />
            <div className="mt-2 text-xs text-gray-500">
              Avg. {formatCurrency(data.sale.averageValue)} per product
            </div>
          </div>
        </Col>
        <Col span={24}>
          <div
            className={`rounded-lg border shadow-md ${isDarkMode ? 'border-gray-800 bg-gray-900' : 'border-gray-200 bg-slate-50'} p-4`}
          >
            <Space className="mb-2 w-full justify-between">
              <span className="text-sm font-medium">Potential Profit</span>
              <span className="text-sm font-medium text-green-500">
                {formatCurrency(potentialProfit)}
              </span>
            </Space>
            <Progress
              percent={profitMargin}
              status={profitMargin >= 0 ? 'success' : 'exception'}
              showInfo={false}
              strokeWidth={8}
              className="mb-2"
            />
            <div className="flex justify-between text-xs">
              <span className="text-gray-500">Margin</span>
              <span className={profitMargin >= 0 ? 'text-green-500' : 'text-red-500'}>
                {profitMargin.toFixed(1)}%
              </span>
            </div>
          </div>
        </Col>
      </Row>
    </div>
  )
}

export default StockValueOverview
