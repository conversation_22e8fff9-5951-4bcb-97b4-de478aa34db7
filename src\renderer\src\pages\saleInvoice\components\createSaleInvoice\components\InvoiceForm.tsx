import { DatePicker, Form, Input, Select, Space, Spin } from 'antd'
import { usePartyContext } from '@/renderer/contexts'
import dayjs from 'dayjs'

interface Props {
  formData: {
    customerId?: string
    customerName?: string
    date: Date
    invoiceNumber: string
  }
  onChange: (data: Partial<Props['formData']>) => void
  type: 'REGISTERED' | 'WALK_IN'
  loading?: boolean
}

export const InvoiceForm = ({ formData, onChange, type, loading }: Props) => {
  const { customers } = usePartyContext()

  return (
    <Form layout="vertical" className="w-full">
      <Space size="middle" className="w-full">
        <Form.Item label="Invoice Number" className="w-48">
          <Input
            value={formData.invoiceNumber}
            disabled
            placeholder={loading ? 'Generating...' : 'Invoice number'}
            suffix={loading && <Spin size="small" />}
          />
        </Form.Item>

        {type === 'REGISTERED' ? (
          <Form.Item
            label="Customer"
            className="w-96"
            required
            validateStatus={formData.customerId ? 'success' : 'error'}
            help={!formData.customerId && 'Please select a customer'}
          >
            <Select
              showSearch
              allowClear
              placeholder="Select customer"
              value={formData.customerId || undefined}
              onChange={(value) => onChange({ customerId: value })}
              options={customers}
              filterOption={(input, option) =>
                (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
              }
            />
          </Form.Item>
        ) : (
          <Form.Item label="Customer Name" className="w-96">
            <Input
              placeholder="Enter customer name (optional)"
              value={formData.customerName}
              onChange={(e) => onChange({ customerName: e.target.value })}
            />
          </Form.Item>
        )}

        <Form.Item label="Date" required>
          <DatePicker
            value={dayjs(formData.date)}
            onChange={(date) => onChange({ date: date?.toDate() || new Date() })}
            format="DD/MM/YYYY"
          />
        </Form.Item>
      </Space>
    </Form>
  )
}
