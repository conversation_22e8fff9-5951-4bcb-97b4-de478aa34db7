// src/renderer/src/pages/banks/components/CreateBankDrawer.tsx
import { Form, Input, InputNumber, Drawer, Button, App } from 'antd'
import { useSelector } from 'react-redux'
import { bankApi } from '@/renderer/services'
import { CreateBankData } from '@/common/types'
import { IRootState } from '@/renderer/redux'
import { useBankContext } from '@/renderer/contexts'

interface CreateBankDrawerProps {
  open: boolean
  onClose: () => void
  onBankCreated: () => void
}

export const CreateBankDrawer = ({ open, onClose, onBankCreated }: CreateBankDrawerProps) => {
  const [form] = Form.useForm()
  const user = useSelector((state: IRootState) => state.user.data)

  const { message } = App.useApp()

  const { refreshBanks } = useBankContext()

  const handleSubmit = async (values: any) => {
    const data: CreateBankData = {
      ...values,
      adminId: user?.id
    }

    const response = await bankApi.createBank(data)

    if (response.error.error || response.data.error) {
      message.error(response.error.message || response.data.error.message)
      return
    }

    message.success('Bank created successfully')

    form.resetFields()
    refreshBanks()
    onBankCreated()
  }

  return (
    <Drawer title="Create New Bank" placement="right" onClose={onClose} open={open} width={400}>
      <Form form={form} layout="vertical" onFinish={handleSubmit}>
        <Form.Item
          name="name"
          label="Bank Name"
          rules={[{ required: true, message: 'Please enter bank name' }]}
        >
          <Input />
        </Form.Item>

        <Form.Item
          name="accountNo"
          label="Account Number"
          rules={[{ required: true, message: 'Please enter account number' }]}
        >
          <Input />
        </Form.Item>

        <Form.Item name="openingBalance" label="Opening Balance" initialValue={0}>
          <InputNumber
            style={{ width: '100%' }}
            formatter={(value) => `Rs. ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
            parser={(value) => value!.replace(/Rs\.\s?|(,*)/g, '')}
          />
        </Form.Item>

        <Form.Item>
          <Button type="primary" htmlType="submit" block>
            Create Bank
          </Button>
        </Form.Item>
      </Form>
    </Drawer>
  )
}
