import { useEffect } from 'react'
import { Alert, Card, Col, Row, Table, Spin } from 'antd'
import { useTheme } from '@/renderer/contexts'
import { useApi } from '@/renderer/hooks'
import { reportsApi } from '@/renderer/services'
import { CashFlowReport, ReportFormat } from '@/common/types'
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  Legend
} from 'recharts'

interface Props {
  format: ReportFormat
  shouldGenerate: boolean
  onGenerateComplete: () => void
  currentReportId: string
  dateRange?: [Date, Date]
  includeDetails?: boolean
}

const CashFlow = ({
  format,
  shouldGenerate,
  onGenerateComplete,
  currentReportId,
  dateRange,
  includeDetails
}: Props) => {
  const { isDarkMode } = useTheme()
  const { request, data, isLoading, errorMessage } = useApi<
    CashFlowReport,
    [{ format: ReportFormat; startDate?: Date; endDate?: Date; includeDetails?: boolean }]
  >(reportsApi.generateCashFlow)

  useEffect(() => {
    if (shouldGenerate && currentReportId === 'cash-flow') {
      request({
        format,
        startDate: dateRange?.[0],
        endDate: dateRange?.[1],
        includeDetails
      }).finally(() => {
        onGenerateComplete()
      })
    }
  }, [shouldGenerate])

  if (isLoading) {
    return <Spin size="large" className="flex w-full justify-center p-8" />
  }

  if (errorMessage) {
    return <Alert type="error" message={errorMessage} className="mb-4" />
  }

  if (!data) return null

  const COLORS = ['#6366f1', '#8b5cf6', '#d946ef', '#f43f5e', '#f97316']

  return (
    <div className="space-y-6">
      {/* Cash Flow Timeline */}
      <Card title="Cash Flow Timeline" className={isDarkMode ? 'bg-black' : 'bg-white'}>
        <div style={{ height: 400 }}>
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={data.timeline}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="date" tickFormatter={(date) => new Date(date).toLocaleDateString()} />
              <YAxis />
              <Tooltip
                labelFormatter={(date) => new Date(date).toLocaleDateString()}
                formatter={(value) => `₹${Number(value).toLocaleString()}`}
              />
              <Legend />
              <Line
                type="monotone"
                dataKey="inflow"
                stroke="#6366f1"
                name="Inflow"
                strokeWidth={2}
              />
              <Line
                type="monotone"
                dataKey="outflow"
                stroke="#f43f5e"
                name="Outflow"
                strokeWidth={2}
              />
              <Line
                type="monotone"
                dataKey="netFlow"
                stroke="#10b981"
                name="Net Flow"
                strokeWidth={2}
              />
            </LineChart>
          </ResponsiveContainer>
        </div>
      </Card>

      {/* Flow Distribution */}
      <Row gutter={[16, 16]}>
        <Col span={12}>
          <Card title="Inflow Sources" className={isDarkMode ? 'bg-black' : 'bg-white'}>
            <div style={{ height: 300 }}>
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={data.inflowSources}
                    dataKey="amount"
                    nameKey="source"
                    cx="50%"
                    cy="50%"
                    outerRadius={80}
                    label={({ source, percentage }) => `${source} (${percentage.toFixed(1)}%)`}
                  >
                    {data.inflowSources.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <Tooltip formatter={(value) => `₹${Number(value).toLocaleString()}`} />
                </PieChart>
              </ResponsiveContainer>
            </div>
          </Card>
        </Col>
        <Col span={12}>
          <Card title="Outflow Categories" className={isDarkMode ? 'bg-black' : 'bg-white'}>
            <div style={{ height: 300 }}>
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={data.outflowCategories}
                    dataKey="amount"
                    nameKey="category"
                    cx="50%"
                    cy="50%"
                    outerRadius={80}
                    label={({ category, percentage }) => `${category} (${percentage.toFixed(1)}%)`}
                  >
                    {data.outflowCategories.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <Tooltip formatter={(value) => `₹${Number(value).toLocaleString()}`} />
                </PieChart>
              </ResponsiveContainer>
            </div>
          </Card>
        </Col>
      </Row>

      {/* Transaction Details */}
      {data.details && (
        <Card title="Transaction Details" className={isDarkMode ? 'bg-black' : 'bg-white'}>
          <Table
            dataSource={data.details}
            columns={[
              {
                title: 'Date',
                dataIndex: 'date',
                key: 'date',
                render: (date) => new Date(date).toLocaleDateString()
              },
              {
                title: 'Description',
                dataIndex: 'description',
                key: 'description'
              },
              {
                title: 'Type',
                dataIndex: 'type',
                key: 'type'
              },
              {
                title: 'Inflow',
                dataIndex: 'inflow',
                key: 'inflow',
                render: (value) => (value ? `₹${value.toLocaleString()}` : '-')
              },
              {
                title: 'Outflow',
                dataIndex: 'outflow',
                key: 'outflow',
                render: (value) => (value ? `₹${value.toLocaleString()}` : '-')
              },
              {
                title: 'Balance',
                dataIndex: 'balance',
                key: 'balance',
                render: (value) => `₹${value.toLocaleString()}`
              }
            ]}
            pagination={false}
            scroll={{ x: true }}
          />
        </Card>
      )}
    </div>
  )
}

export default CashFlow
