import { productApi, categoryApi } from '@/renderer/services'
import { Modal, Form, Input, InputNumber, message, Button, Space, Descriptions, Select } from 'antd'
import { PlusOutlined } from '@ant-design/icons'
import { useState, useEffect } from 'react'
import { useProductContext } from '@/renderer/contexts'
import { CreateCategoryModal } from './CreateCategoryModal'

interface AddNewProductModalProps {
  open: boolean
  onClose: () => void
  setRefreshTrigger: (trigger: any) => void
}

interface ProductFormValues {
  name: string
  productId: string
  category: string
  nature?: string
  tag?: string
  minStockLevel?: number
  salePrice: number
}

interface ProductData extends Omit<ProductFormValues, 'category'> {
  categoryId: string
}

const AddNewProductModal = ({ open, onClose, setRefreshTrigger }: AddNewProductModalProps) => {
  const [form] = Form.useForm<ProductFormValues>()
  const [isConfirmationVisible, setIsConfirmationVisible] = useState(false)
  const [formValues, setFormValues] = useState<ProductFormValues | null>(null)
  const [isCategoryModalOpen, setIsCategoryModalOpen] = useState(false)
  const [categories, setCategories] = useState([])
  const [loading, setLoading] = useState(false)

  const { refreshProducts } = useProductContext()

  const fetchCategories = async () => {
    const response = await categoryApi.getCategoriesForSelect()

    if (response.error?.error || response.data.error) {
      message.error(response.error?.message || response.data.error.message)
      return
    }
    setCategories(response.data.data)
  }

  const handleCategoryChange = async (categoryId: string) => {
    const response = await categoryApi.generateProductId(categoryId)
    if (response.error?.error || response.data.error) {
      message.error(response.error?.message || response.data.error.message)
      return
    }
    form.setFieldValue('productId', response.data.data)
  }

  useEffect(() => {
    if (open) {
      fetchCategories()
    }
  }, [open])

  const handleFormSubmit = async () => {
    try {
      const values = await form.validateFields()
      setFormValues(values)
      setIsConfirmationVisible(true)
    } catch (error) {
      console.error('Form validation failed:', error)
    }
  }

  const handleCreateProduct = async () => {
    if (!formValues) return

    const { category, ...rest } = formValues
    const productData: ProductData = {
      ...rest,
      categoryId: category
    }

    const response = await productApi.createProduct(productData)

    if (response.error.error || response.data?.error) {
      message.error(response.error?.message || response.data?.error?.message)
      return
    }

    message.success('Product created successfully')
    refreshProducts()
    form.resetFields()
    setIsConfirmationVisible(false)
    onClose()
  }

  const closeConfirmationModal = () => {
    setIsConfirmationVisible(false)
  }

  return (
    <>
      <Modal
        title="Add New Product"
        open={open}
        onCancel={onClose}
        footer={
          <Space>
            <Button icon={<PlusOutlined />} onClick={() => setIsCategoryModalOpen(true)}>
              Add Category
            </Button>
            <Button
              className="bg-green-500 hover:!bg-green-600"
              type="primary"
              onClick={handleFormSubmit}
            >
              Create
            </Button>
          </Space>
        }
        width={600}
      >
        <Form form={form} layout="vertical">
          <Form.Item
            name="name"
            label="Product Name"
            rules={[{ required: true, message: 'Please enter product name' }]}
          >
            <Input />
          </Form.Item>

          <Form.Item
            name="category"
            label="Category"
            rules={[{ required: true, message: 'Please select category' }]}
          >
            <Select
              placeholder="Select category"
              allowClear
              showSearch
              options={categories.map((category: any) => ({
                label: category.label,
                value: category.value
              }))}
              filterOption={(input, option) =>
                (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
              }
              onChange={handleCategoryChange}
              loading={loading}
            />
          </Form.Item>

          <Form.Item
            name="productId"
            label="Product ID"
            rules={[{ required: true, message: 'Please enter product ID' }]}
          >
            <Input />
          </Form.Item>

          <Form.Item name="nature" label="Nature">
            <Input />
          </Form.Item>

          <Form.Item name="tag" label="Tag">
            <Input />
          </Form.Item>

          <Form.Item name="minStockLevel" label="Minimum Stock Level">
            <InputNumber style={{ width: '100%' }} />
          </Form.Item>

          <Form.Item
            name="salePrice"
            label="Sale Price"
            rules={[{ required: true, message: 'Please enter sale price' }]}
          >
            <InputNumber style={{ width: '100%' }} />
          </Form.Item>
        </Form>
      </Modal>

      <CreateCategoryModal
        open={isCategoryModalOpen}
        onClose={() => setIsCategoryModalOpen(false)}
        onCategoryCreated={fetchCategories}
      />

      <ProductConfirmationModal
        open={isConfirmationVisible}
        onCancel={closeConfirmationModal}
        onConfirm={handleCreateProduct}
        productDetails={formValues}
      />
    </>
  )
}

export default AddNewProductModal

interface ProductConfirmationModalProps {
  open: boolean
  onCancel: () => void
  onConfirm: () => void
  productDetails: ProductFormValues | null
}

const ProductConfirmationModal = ({
  open,
  onCancel,
  onConfirm,
  productDetails
}: ProductConfirmationModalProps) => {
  if (!productDetails) return null

  return (
    <Modal
      title="Confirm Product Details"
      open={open}
      onCancel={onCancel}
      footer={
        <Space>
          <Button onClick={onCancel}>Cancel</Button>
          <Button
            type="primary"
            onClick={onConfirm}
            className="!border-green-400 !bg-green-400 hover:!border-green-600 hover:!bg-green-600"
          >
            Confirm
          </Button>
        </Space>
      }
      width={600}
    >
      <p>Are you sure you want to add this product?</p>

      <Descriptions
        className="mt-4"
        bordered
        title="Product Details"
        column={1}
        layout="horizontal"
      >
        <Descriptions.Item label="Product Name">{productDetails.name}</Descriptions.Item>
        <Descriptions.Item label="Product ID">{productDetails.productId}</Descriptions.Item>
        {/* <Descriptions.Item label="Category">{productDetails.category}</Descriptions.Item> */}
        <Descriptions.Item label="Nature">{productDetails.nature}</Descriptions.Item>
        <Descriptions.Item label="Tag">{productDetails.tag}</Descriptions.Item>
        <Descriptions.Item label="Minimum Stock Level">
          {productDetails.minStockLevel}
        </Descriptions.Item>
        <Descriptions.Item label="Sale Price">{productDetails.salePrice}</Descriptions.Item>
      </Descriptions>
    </Modal>
  )
}
