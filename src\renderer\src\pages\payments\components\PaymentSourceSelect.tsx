import { Form, Radio, Select, Space, Typography } from 'antd'
import { useBankContext } from '@/renderer/contexts'

const { Text } = Typography

interface PaymentSourceSelectProps {
  form: any
  namePrefix?: string
}

export const PaymentSourceSelect = ({ form, namePrefix = '' }: PaymentSourceSelectProps) => {
  const { banks } = useBankContext()
  const source = Form.useWatch([`${namePrefix}Source`], form)

  return (
    <Space direction="vertical" className="w-full">
      <Form.Item
        name={`${namePrefix}Source`}
        label="Pay or Receive From"
        rules={[{ required: true, message: 'Please select payment source' }]}
      >
        <Radio.Group>
          <Radio.Button value="SMALL_COUNTER">Small Counter</Radio.Button>
          <Radio.Button value="CASH_VAULT">Vault</Radio.Button>
          <Radio.Button value="BANK">Bank</Radio.Button>
        </Radio.Group>
      </Form.Item>

      {source === 'BANK' && (
        <Form.Item
          name={`${namePrefix}bankId`}
          rules={[{ required: true, message: 'Please select bank' }]}
        >
          <Select placeholder="Select bank" options={banks} className="w-full" />
        </Form.Item>
      )}
    </Space>
  )
}
