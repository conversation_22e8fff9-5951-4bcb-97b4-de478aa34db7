export interface LedgerEntry {
    id: string
    date: Date
    amount: number
    creditOrDebit: 'CREDIT' | 'DEBIT'
    description: string
    status: 'ACTIVE' | 'VOID'
    balance?: number
    createdBy: {
        name: string
    }
}

export interface PaginatedLedgerResponse {
    entries: LedgerEntry[]
    pagination: {
        total: number
        page: number
        limit: number
        hasMore: boolean
    }
    totals: {
        credits: number
        debits: number
        net: number
        overall: {
            credits: number
            debits: number
            net: number
            balance?: number
        }
    }
}

export interface DailyLedgerResponse {
    date: Date
    entries: LedgerEntry[]
    totals: {
        credits: number
        debits: number
        net: number
    }
}

export interface GetLedgerParams {
    page?: number
    limit?: number
    startDate?: Date
    endDate?: Date
    partyId?: string
}

export interface GetDailyLedgerParams {
    date: Date
}
