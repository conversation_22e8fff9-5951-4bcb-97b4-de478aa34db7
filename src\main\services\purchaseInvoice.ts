import { prisma } from '../db';
import { Prisma } from '@prisma/client';
import dayjs from 'dayjs';
import { processDateRange } from '../utils/helperFunctions';
import {
    CreatePurchaseInvoiceData,
    GetPurchaseInvoicesParams,
    PurchaseInvoiceResponse
} from '@/common/types';



class PurchaseInvoiceService {
    async create(data: CreatePurchaseInvoiceData) {
        // Validate quantities and prices
        for (const item of data.items) {
            if (item.quantity <= 0) {
                throw new Error(`Quantity must be greater than zero for product ${item.productId}`);
            }
            if (item.purchasePrice <= 0) {
                throw new Error(`Purchase price must be greater than zero for product ${item.productId}`);
            }
        }

        return await prisma.$transaction(async (tx) => {
            // Create the purchase invoice first
            const invoice = await tx.purchaseInvoice.create({
                data: {
                    invoiceNumber: data.invoiceNumber,
                    vendorId: data.vendorId,
                    totalAmount: data.totalAmount,
                    paidAmount: data.paidAmount,
                    previousBalance: data.previousBalance,
                    newBalance: data.newBalance,
                    date: data.date,
                    createdById: data.createdById,
                    items: {
                        create: data.items
                    }
                },
                include: {
                    items: true
                }
            });



            // Create stock entries and update product quantities
            for (const item of data.items) {
                await tx.stock.create({
                    data: {
                        purchasePrice: item.purchasePrice,
                        quantity: item.quantity,
                        productId: item.productId,
                        vendorId: data.vendorId,
                        purchaseInvoiceId: invoice.id
                    }
                });

                await tx.product.update({
                    where: { id: item.productId },
                    data: {
                        quantityInStock: { increment: item.quantity }
                    }
                });
            }

            // Update vendor's balance
            await tx.party.update({
                where: { id: data.vendorId },
                data: {
                    currentBalance: data.newBalance
                }
            });

            // Create purchase ledger entry
            await tx.ledger.create({
                data: {
                    partyId: data.vendorId,
                    amount: data.totalAmount,
                    creditOrDebit: 'CREDIT',
                    description: `Purchase Invoice #${data.invoiceNumber}`,
                    referenceType: 'PurchaseInvoice',
                    purchaseRef: invoice.id,
                    date: data.date,
                    status: 'ACTIVE',
                    createdById: data.createdById
                }
            });

            // Handle payment if paid amount > 0
            if (data.paidAmount > 0 && data.paymentMethod) {
                // Validate payment source has sufficient balance
                switch (data.paymentMethod) {
                    case 'cash':
                        const cashBalance = await tx.smallCounter.findFirst();
                        if (!cashBalance || cashBalance.cashInShop < data.paidAmount) {
                            throw new Error('Insufficient cash in counter');
                        }
                        await tx.smallCounter.updateMany({
                            data: { cashInShop: { decrement: data.paidAmount } }
                        });
                        break;

                    case 'vault':
                        const vault = await tx.cashVault.findFirst();
                        if (!vault || vault.balance < data.paidAmount) {
                            throw new Error('Insufficient funds in vault');
                        }
                        await tx.cashVault.updateMany({
                            data: { balance: { decrement: data.paidAmount } }
                        });
                        break;

                    case 'bank':
                        if (!data.bankId) throw new Error('Bank ID is required for bank payment');
                        const bankBalance = await tx.banks.findUnique({
                            where: { id: data.bankId }
                        });
                        if (!bankBalance || bankBalance.balance < data.paidAmount) {
                            throw new Error('Insufficient funds in bank');
                        }
                        await tx.banks.update({
                            where: { id: data.bankId },
                            data: { balance: { decrement: data.paidAmount } }
                        });
                        break;
                }

                // Create payment record
                const payment = await tx.payments.create({
                    data: {
                        date: data.date,
                        type: 'PAID',
                        amount: data.paidAmount,
                        paymentMethod: data.paymentMethod === 'bank' ? 'BANK_TRANSFER' : 'CASH',
                        referenceNumber: data.invoiceNumber,
                        partyId: data.vendorId,
                        createdById: data.createdById,
                        sourceLocation: data.paymentMethod === 'bank' ? 'BANK' : data.paymentMethod === 'vault' ? 'CASH_VAULT' : 'SMALL_COUNTER',
                        locationId: data.paymentMethod === 'bank' ? data.bankId : null,
                    }
                });

                /// Link payment to purchase invoice
                await tx.purchaseInvoice.update({
                    where: { id: invoice.id },
                    data: { paymentId: payment.id }
                });

                const vault = await tx.cashVault.findFirst();

                // Payment ledger entry
                await tx.ledger.create({
                    data: {
                        partyId: data.vendorId,
                        amount: data.paidAmount,
                        creditOrDebit: 'DEBIT',
                        description: `Payment against Invoice #${data.invoiceNumber}`,
                        referenceType: 'Payment',
                        paymentRef: payment.id,
                        date: data.date,
                        status: 'ACTIVE',
                        bankId: data.paymentMethod === 'bank' ? data.bankId : undefined,
                        cashSource: data.paymentMethod === 'cash' ? 'SMALL_COUNTER' :
                            data.paymentMethod === 'vault' ? 'CASH_VAULT' : 'BANK',
                        createdById: data.createdById
                    }
                });
            }

            return invoice;
        });
    }

    async void(id: string, adminId: string, reason: string) {
        return await prisma.$transaction(async (tx) => {
            // Get purchase invoice with items and payment details
            const purchaseInvoice = await tx.purchaseInvoice.findUnique({
                where: { id },
                include: {
                    items: true,
                    payment: {
                        select: {
                            id: true,
                            amount: true,
                            sourceLocation: true,
                            locationId: true
                        }
                    },
                    vendor: true,
                    Ledger: true
                }
            });

            if (!purchaseInvoice) {
                throw new Error('Purchase invoice not found');
            }

            // Check if invoice is already voided
            if (purchaseInvoice.status === 'VOID') {
                throw new Error('Invoice is already voided');
            }

            // Generate a unique voided invoice number
            const voidedInvoiceNumber = `${purchaseInvoice.invoiceNumber}_VOID_${dayjs().format('YYYY/MM/DD HH:mm:ss')}`;

            // POTENTIAL ISSUE: The following stock check logic is complex and might not handle all edge cases.
            // For example, if a product has multiple stock entries from different purchase invoices,
            // and some of those entries have been partially sold, this check might not accurately
            // determine if the specific stock entries from this invoice can be voided.
            // A more robust approach would be to track which specific stock entries have been used
            // in sales and check those directly.

            // Check if items can be voided (stock check)
            const stockEntries = await tx.stock.findMany({
                where: { purchaseInvoiceId: id },
                select: {
                    id: true,
                    quantity: true,
                    productId: true
                }
            });

            const stockByProduct = stockEntries.reduce((acc, stock) => {
                if (!acc[stock.productId]) {
                    acc[stock.productId] = 0;
                }
                acc[stock.productId] += stock.quantity;
                return acc;
            }, {} as Record<string, number>);

            // Compare quantities
            for (const item of purchaseInvoice.items) {
                const stockQuantity = stockByProduct[item.productId] || 0;
                if (stockQuantity !== item.quantity) {
                    throw new Error(`Cannot void invoice: Some items of product ${item.productId} have been sold`);
                }
            }

            // First handle the core purchase invoice operations
            await Promise.all([
                tx.purchaseInvoice.update({
                    where: { id },
                    data: {
                        status: 'VOID',
                        voidedById: adminId,
                        voidedAt: new Date(),
                        voidingReason: reason,
                        invoiceNumber: voidedInvoiceNumber
                    }
                }),
                tx.stock.deleteMany({
                    where: { purchaseInvoiceId: id }
                }),
                tx.ledger.updateMany({
                    where: { purchaseRef: id },
                    data: {
                        status: 'VOID',
                        voidedById: adminId
                    }
                }),
                tx.party.update({
                    where: { id: purchaseInvoice.vendorId },
                    data: {
                        currentBalance: {
                            increment: purchaseInvoice.totalAmount
                        }
                    }
                })
            ]);


            // Handle payment reversal if payment exists
            if (purchaseInvoice.payment) {
                const { amount, sourceLocation, locationId } = purchaseInvoice.payment;

                // Handle source balance updates
                switch (sourceLocation) {
                    case 'SMALL_COUNTER':
                        await tx.smallCounter.updateMany({
                            data: { cashInShop: { increment: amount } }
                        });
                        break;

                    case 'CASH_VAULT':
                        const vault = await tx.cashVault.findFirst();
                        if (!vault) throw new Error('Cash vault not found');
                        await tx.cashVault.updateMany({
                            data: { balance: { increment: amount } }
                        });
                        break;

                    case 'BANK':
                        if (!locationId) {
                            throw new Error('Bank ID not found for bank payment');
                        }
                        await tx.banks.update({
                            where: { id: locationId },
                            data: { balance: { increment: amount } }
                        });
                        break;
                }

                // Void payment record and ledger entry
                await Promise.all([
                    tx.payments.update({
                        where: { id: purchaseInvoice.payment.id },
                        data: {
                            status: 'VOID',
                            voidedById: adminId,
                            voidedAt: new Date(),
                            voidingReason: `Voided due to deletion of Purchase Invoice #${purchaseInvoice.invoiceNumber}`
                        }
                    }),
                    tx.ledger.update({
                        where: { id: purchaseInvoice.payment.id },
                        data: {
                            status: 'VOID',
                            voidedById: adminId
                        }
                    })
                ]);
            }

            // Update product quantities
            await Promise.all(
                purchaseInvoice.items.map(item =>
                    tx.product.update({
                        where: { id: item.productId },
                        data: {
                            quantityInStock: { decrement: item.quantity }
                        }
                    })
                )
            );

            return purchaseInvoice;
        });
    }

    async getById(id: string) {
        const invoice = await prisma.purchaseInvoice.findUnique({
            where: { id },
            include: {
                vendor: true,
                items: {
                    include: {
                        product: true
                    }
                },
                createdBy: { select: { name: true } },
                voidedBy: { select: { name: true } }
            }
        });

        if (!invoice) {
            throw new Error('Purchase invoice not found');
        }

        return invoice;
    }

    async getAll({
        page = 1,
        limit = 10,
        search,
        vendorId,
        startDate,
        endDate,
        status = 'ACTIVE'
    }: GetPurchaseInvoicesParams): Promise<PurchaseInvoiceResponse> {
        const where: Prisma.PurchaseInvoiceWhereInput = {
            status,
            type: 'PURCHASE',
            ...(vendorId && { vendorId }),
            ...(search && {
                OR: [
                    { invoiceNumber: { contains: search, mode: 'insensitive' } },
                    { vendor: { name: { contains: search, mode: 'insensitive' } } }
                ]
            }),
            ...(startDate || endDate ? {
                date: processDateRange(startDate, endDate)
            } : {})
        };

        // POTENTIAL ISSUE: The getAll method returns dates as ISO strings, but this transformation
        // should ideally be handled by the client. The backend should return native Date objects
        // and let the client format them as needed.

        const [invoices, total] = await Promise.all([
            prisma.purchaseInvoice.findMany({
                where,
                include: {
                    vendor: true,
                    items: {
                        include: {
                            product: {
                                include: {
                                    category: true
                                }
                            }
                        }
                    },
                    createdBy: { select: { name: true } },
                    voidedBy: { select: { name: true } }
                },
                skip: (page - 1) * limit,
                take: limit,
                orderBy: { date: 'desc' }
            }),
            prisma.purchaseInvoice.count({ where })
        ]);

        return {
            invoices: invoices.map(invoice => ({
                ...invoice,
                date: invoice.date.toISOString(),
                voidedAt: invoice.voidedAt?.toISOString()
            })),
            total,
            page,
            totalPages: Math.ceil(total / limit)
        };
    }

    async getByVendor(vendorId: string, { page, limit }: { page: number; limit: number }) {
        const where = { vendorId };

        // POTENTIAL ISSUE: This method doesn't handle concurrent modifications to the same invoice.
        // If two users try to modify the same invoice simultaneously, it could lead to race conditions.
        // Consider implementing optimistic concurrency control using version numbers or timestamps.

        return await prisma.$transaction(async (tx) => {
            const [invoices, total] = await Promise.all([
                tx.purchaseInvoice.findMany({
                    where,
                    include: {
                        items: {
                            include: {
                                product: true
                            }
                        },
                        createdBy: { select: { name: true } },
                        voidedBy: { select: { name: true } }
                    },
                    skip: (page - 1) * limit,
                    take: limit,
                    orderBy: { date: 'desc' }
                }),
                tx.purchaseInvoice.count({ where })
            ]);

            return { invoices, total, page, totalPages: Math.ceil(total / limit) };
        });
    }

}

export const purchaseInvoiceService = new PurchaseInvoiceService();
