// import { IRootState } from "@/redux";
import { useEffect } from 'react'
import { useSelector } from 'react-redux'
import { Outlet, useNavigate } from 'react-router'
import './auth-layout.scss'
import { App_Routes } from '@/common'
import { Layout } from 'antd'
import { useTheme } from '../contexts/ThemeContext'

const AuthLayout = () => {
  const navigate = useNavigate()
  const { isDarkMode } = useTheme()

  // const user = useSelector((state: IRootState) => state.user.data);

  // useEffect(() => {
  //   if (user) {
  //     navigate(App_Routes.DASHBOARD);
  //   }
  // }, [user]);

  return (
    <Layout>
      <div className="auth-layout">
        <div className="auth-container">
          <div
            className={`auth-form m-auto w-[400px] rounded-xl bg-[length:200%_200%] bg-[10%_10%] shadow-2xl transition-[background-position] duration-500 ease-in-out hover:bg-[90%_90%] ${
              isDarkMode
                ? 'bg-gradient-to-br from-blue-950 via-slate-950 to-blue-900 shadow-blue-950'
                : 'bg-gradient-to-br from-sky-200 via-white to-blue-200 shadow-blue-200'
            }`}
          >
            <Outlet />
          </div>
        </div>
      </div>
    </Layout>
  )
}

export default AuthLayout
