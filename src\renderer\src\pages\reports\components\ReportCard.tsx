import { <PERSON>, Button, Tooltip } from 'antd'
import { FileTextOutlined, RightOutlined } from '@ant-design/icons'
import { ReportCardProps } from '../types'
import { useTheme } from '@/renderer/contexts'

const ReportCard = ({ report, onGenerate }: ReportCardProps) => {
  const { isDarkMode } = useTheme()

  return (
    <Card
      hoverable
      className={`h-full transition-all duration-500 ${
        isDarkMode
          ? 'bg-black hover:shadow-[0_8px_30px_rgb(99_102_241_/_0.9)]'
          : 'bg-slate-50 hover:shadow-[0_8px_30px_rgb(99_102_241_/_0.4)]'
      }`}
      onClick={() => onGenerate(report)}
    >
      <div className="flex h-full flex-col">
        {/* Header */}
        <div className="mb-4 flex items-start justify-between">
          <div className="flex items-center gap-3">
            <div className={`rounded-lg p-2 ${isDarkMode ? 'bg-indigo-900/50' : 'bg-indigo-100'}`}>
              <FileTextOutlined className="text-xl text-indigo-500" />
            </div>
            <div>
              <h3
                className={`m-0 text-lg font-semibold ${isDarkMode ? 'text-white' : 'text-gray-800'}`}
              >
                {report.title}
              </h3>
              <p className={`m-0 text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                {report.category}
              </p>
            </div>
          </div>
        </div>

        {/* Description */}
        <p className={`mb-4 flex-grow text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
          {report.description}
        </p>

        {/* Footer */}
        <div className="mt-auto flex items-center justify-between">
          <Tooltip title="Configure and generate report">
            <Button
              type="text"
              icon={<RightOutlined />}
              className={
                isDarkMode
                  ? 'text-indigo-400 hover:text-indigo-300'
                  : 'text-indigo-600 hover:text-indigo-500'
              }
            >
              Generate Report
            </Button>
          </Tooltip>
        </div>
      </div>
    </Card>
  )
}

export default ReportCard
