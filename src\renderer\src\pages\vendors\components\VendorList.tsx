import { useEffect, useState } from 'react'
import { Table, Popconfirm, Tag, App } from 'antd'
import { DeleteOutlined, EditOutlined } from '@ant-design/icons'
import { partyApi } from '@/renderer/services'
import type { ColumnsType } from 'antd/es/table'
import { formatCurrency } from '@/renderer/utils'
import { useApi } from '@/renderer/hooks'
import { usePartyContext } from '@/renderer/contexts'
import { PartyType } from '@/common/types'

interface VendorListProps {
  onVendorClick: (id: string) => void
  searchQuery: string
  refreshTrigger: number
}

interface VendorData {
  id: string
  name: string
  contact?: string
  address?: string
  phoneNumber?: string
  currentBalance: number
}

interface GetPartiesParams {
  page: number
  limit: number
  search: string
  type?: PartyType
}

export const VendorList = ({ onVendorClick, searchQuery, refreshTrigger }: VendorListProps) => {
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0
  })

  const { message } = App.useApp()
  const { refreshVendors } = usePartyContext()

  const {
    data: vendorData,
    isLoading: loading,
    request: fetchVendors,
    error
  } = useApi<{ parties: VendorData[]; total: number }, [GetPartiesParams]>(partyApi.getParties)

  useEffect(() => {
    fetchVendors({
      page: pagination.current,
      limit: pagination.pageSize,
      search: searchQuery,
      type: PartyType.VENDOR
    })
  }, [pagination.current, pagination.pageSize, searchQuery, refreshTrigger])

  useEffect(() => {
    if (vendorData) {
      setPagination((prev) => ({
        ...prev,
        total: vendorData.total
      }))
    }
  }, [vendorData])

  const handleDelete = async (id: string) => {
    const response = await partyApi.deleteParty(id)

    if (response.error.error || response.data.error) {
      message.error(response.error.message || response.data.error.message)
      return
    }

    message.success('Vendor deleted successfully')

    fetchVendors({
      page: pagination.current,
      limit: pagination.pageSize,
      search: searchQuery,
      type: PartyType.VENDOR
    })

    refreshVendors()
  }

  const columns: ColumnsType<VendorData> = [
    {
      title: 'No',
      dataIndex: 'index',
      render: (_, __, index) => index + 1 + (pagination.current - 1) * pagination.pageSize
    },
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name'
    },
    {
      title: 'Contact Person',
      dataIndex: 'contact',
      key: 'contact'
    },
    {
      title: 'Phone',
      dataIndex: 'phoneNumber',
      key: 'phoneNumber'
    },
    {
      title: 'Address',
      dataIndex: 'address',
      key: 'address'
    },
    {
      title: 'Current Balance',
      dataIndex: 'currentBalance',
      key: 'currentBalance',
      render: (balance) => (
        <Tag color={balance > 0 ? 'green' : balance < 0 ? 'red' : 'default'}>
          {formatCurrency(balance)}
        </Tag>
      )
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record) => (
        <div className="action-buttons">
          <EditOutlined onClick={() => onVendorClick(record.id)} />
          <Popconfirm
            title="Delete Vendor"
            description="Are you sure you want to delete this vendor?"
            onConfirm={() => handleDelete(record.id)}
            okText="Yes"
            cancelText="No"
          >
            <DeleteOutlined className="delete-icon" />
          </Popconfirm>
        </div>
      )
    }
  ]

  return (
    <Table
      columns={columns}
      dataSource={vendorData?.parties}
      rowKey="id"
      loading={loading}
      sticky
      virtual
      pagination={{
        ...pagination,
        position: ['topRight'],
        showPrevNextJumpers: true,
        showSizeChanger: true,
        showQuickJumper: true,
        showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`
      }}
      onChange={(newPagination) =>
        setPagination({
          current: newPagination.current || 1,
          pageSize: newPagination.pageSize || 10,
          total: pagination.total
        })
      }
    />
  )
}
