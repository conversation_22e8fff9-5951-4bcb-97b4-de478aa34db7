import React from 'react'
import { Modal, Typography, Descriptions, Table, Tag, Tooltip, Divider, Space, Avatar } from 'antd'
import { BarcodeOutlined, TagsOutlined, InfoCircleOutlined } from '@ant-design/icons'
import { formatCurrency } from '@/renderer/utils'
import dayjs from 'dayjs'
import { FaUserAlt } from 'react-icons/fa'

const { Text, Title } = Typography

interface StockReturnItem {
  id: string
  quantity: number
  purchasePrice: number
  product: {
    name: string
    productId: string
    tag?: string
    nature?: string
    category: {
      name: string
    }
  }
}

interface StockReturn {
  id: string
  invoiceNumber: string
  originalInvoiceNumber: string
  date: string | Date
  status: string
  totalAmount: number
  customer: {
    id: string
    name: string
    phoneNumber: string | null
  } | null
  customerType: string
  type?: string
  items: StockReturnItem[]
  createdBy: {
    name: string
  }
  voidingReason?: string
}

interface ReturnStockDetailProps {
  visible: boolean
  onClose: () => void
  stockReturn: StockReturn | null
}

const StockReturnDetails: React.FC<ReturnStockDetailProps> = ({
  visible,
  onClose,
  stockReturn
}) => {
  if (!stockReturn) return null

  const isLegacyReturn = stockReturn.type === 'LEGACY_STOCK_RETURN'

  const itemColumns = [
    {
      title: 'Product Details',
      dataIndex: ['product'],
      key: 'product',
      render: (product) => (
        <Space direction="vertical" size="small">
          <Space wrap>
            <Tag>{product.name}</Tag>
            <Tooltip title="Product ID">
              <Tag color="blue" icon={<BarcodeOutlined />}>
                {product.productId}
              </Tag>
            </Tooltip>
            <Tooltip title="Product Tag">
              <Tag color="cyan" icon={<TagsOutlined />}>
                {product.tag || 'N/A'}
              </Tag>
            </Tooltip>
            <Tooltip title="Product Nature">
              <Tag color="purple">{product.nature || 'N/A'}</Tag>
            </Tooltip>
            <Tooltip title="Product Category">
              <Tag color="gold">{product.category.name || 'N/A'}</Tag>
            </Tooltip>
          </Space>
        </Space>
      )
    },
    {
      title: 'Quantity',
      dataIndex: 'quantity',
      key: 'quantity',
      width: 100
    },
    {
      title: 'Purchase Price',
      dataIndex: 'purchasePrice',
      key: 'purchasePrice',
      width: 150,
      render: (price: number) => formatCurrency(price)
    },
    {
      title: 'Total',
      key: 'total',
      width: 150,
      render: (_, record: StockReturnItem) => formatCurrency(record.quantity * record.purchasePrice)
    }
  ]

  return (
    <Modal
      open={visible}
      onCancel={onClose}
      footer={null}
      width={800}
      title={
        <Space align="center">
          <InfoCircleOutlined />
          <span>
            {isLegacyReturn ? 'Legacy Stock Return Details' : 'Stock Return Details'}
            {isLegacyReturn && (
              <Tag color="purple" className="ml-2">
                LEGACY
              </Tag>
            )}
          </span>
        </Space>
      }
    >
      <div className="mb-6">
        <Descriptions bordered column={2} size="small">
          <Descriptions.Item label="Return Invoice" span={1}>
            <Text strong>{stockReturn.invoiceNumber}</Text>
          </Descriptions.Item>
          <Descriptions.Item label={isLegacyReturn ? 'Return Type' : 'Original Invoice'} span={1}>
            {isLegacyReturn ? (
              <Tag color="purple">LEGACY RETURN</Tag>
            ) : (
              <Text>{stockReturn.originalInvoiceNumber}</Text>
            )}
          </Descriptions.Item>
          <Descriptions.Item label="Date" span={1}>
            {dayjs(stockReturn.date).format('YYYY-MM-DD HH:mm')}
          </Descriptions.Item>
          <Descriptions.Item label="Status" span={1}>
            <Tag color={stockReturn.status === 'ACTIVE' ? 'green' : 'red'}>
              {stockReturn.status}
            </Tag>
          </Descriptions.Item>
          <Descriptions.Item label="Customer" span={2}>
            <Space>
              <Avatar
                icon={<FaUserAlt />}
                size="small"
                style={{
                  backgroundColor: stockReturn.customerType === 'REGISTERED' ? '#4338ca' : '#6d28d9'
                }}
              />
              <span>
                {stockReturn.customer ? stockReturn.customer.name : 'Walk-in Customer'}
                {stockReturn.customerType === 'REGISTERED' && (
                  <Tag color="blue" className="ml-2">
                    REGISTERED
                  </Tag>
                )}
                {stockReturn.customerType === 'WALK_IN' && (
                  <Tag color="purple" className="ml-2">
                    WALK-IN
                  </Tag>
                )}
              </span>
            </Space>
            {stockReturn.customer && stockReturn.customer.phoneNumber && (
              <div className="mt-1 text-gray-500">Phone: {stockReturn.customer.phoneNumber}</div>
            )}
          </Descriptions.Item>
          <Descriptions.Item label="Total Amount" span={1}>
            <Text strong className="text-lg text-indigo-600">
              {formatCurrency(stockReturn.totalAmount)}
            </Text>
          </Descriptions.Item>
          <Descriptions.Item label="Created By" span={1}>
            {stockReturn.createdBy.name}
          </Descriptions.Item>
        </Descriptions>

        {isLegacyReturn && (
          <div className="mt-4">
            <Title level={5} className="text-blue-500">
              Legacy Return Information:
            </Title>
            <div className="rounded-md border border-blue-200 bg-blue-50 p-3">
              This is a return for a purchase made before the system was implemented. No original
              invoice exists in the system for this return.
            </div>
          </div>
        )}

        {stockReturn.voidingReason && (
          <div className="mt-4">
            <Title level={5} className="text-red-500">
              Void Reason:
            </Title>
            <div className="rounded-md border border-red-200 bg-red-50 p-3">
              {stockReturn.voidingReason}
            </div>
          </div>
        )}
      </div>

      <Divider orientation="left">Returned Items ({stockReturn.items.length})</Divider>

      <Table
        columns={itemColumns}
        dataSource={stockReturn.items}
        pagination={false}
        rowKey="id"
        size="small"
        virtual
        sticky
        bordered
        summary={(pageData) => {
          const total = pageData.reduce((sum, item) => sum + item.quantity * item.purchasePrice, 0)
          return (
            <Table.Summary.Row>
              <Table.Summary.Cell index={0} colSpan={3} align="right">
                <Text strong>Total:</Text>
              </Table.Summary.Cell>
              <Table.Summary.Cell index={1}>
                <Text strong className="text-indigo-600">
                  {formatCurrency(total)}
                </Text>
              </Table.Summary.Cell>
            </Table.Summary.Row>
          )
        }}
      />
    </Modal>
  )
}

export default StockReturnDetails
