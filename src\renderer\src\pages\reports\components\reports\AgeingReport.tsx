import { useEffect } from 'react'
import { <PERSON><PERSON>, Card, Col, Row, Table, Spin } from 'antd'
import { useTheme } from '@/renderer/contexts'
import { useApi } from '@/renderer/hooks'
import { reportsApi } from '@/renderer/services'
import { AgeingReport as AgeingReportType, ReportFormat } from '@/common/types'
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Cell
} from 'recharts'

interface Props {
  format: ReportFormat
  shouldGenerate: boolean
  onGenerateComplete: () => void
  currentReportId: string
  dateRange?: [Date, Date]
  partyType: 'CUSTOMER' | 'VENDOR'
  minAmount?: number
}

const AgeingReport = ({
  format,
  shouldGenerate,
  onGenerateComplete,
  currentReportId,
  dateRange,
  partyType,
  minAmount
}: Props) => {
  const { isDarkMode } = useTheme()
  const { request, data, isLoading, errorMessage } = useApi<
    AgeingReportType,
    [
      {
        format: ReportFormat
        startDate?: Date
        endDate?: Date
        partyType: 'CUSTOMER' | 'VENDOR'
        minAmount?: number
      }
    ]
  >(reportsApi.generateAgeingReport)

  useEffect(() => {
    if (shouldGenerate && currentReportId === 'ageing-report') {
      request({
        format,
        startDate: dateRange?.[0],
        endDate: dateRange?.[1],
        partyType,
        minAmount
      }).finally(() => {
        onGenerateComplete()
      })
    }
  }, [shouldGenerate])

  if (isLoading) {
    return <Spin size="large" className="flex w-full justify-center p-8" />
  }

  if (errorMessage) {
    return <Alert type="error" message={errorMessage} className="mb-4" />
  }

  if (!data) return null

  const COLORS = ['#6366f1', '#8b5cf6', '#d946ef', '#f43f5e', '#f97316']

  return (
    <div className="space-y-6">
      {/* Summary Cards */}
      <Row gutter={[16, 16]}>
        <Col span={8}>
          <Card className={isDarkMode ? 'bg-black' : 'bg-white'}>
            <div className="text-center">
              <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Total Outstanding
              </p>
              <h2 className={`text-2xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-800'}`}>
                ₹{data.summary.totalOutstanding.toLocaleString()}
              </h2>
            </div>
          </Card>
        </Col>
        <Col span={8}>
          <Card className={isDarkMode ? 'bg-black' : 'bg-white'}>
            <div className="text-center">
              <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Average Overdue Days
              </p>
              <h2 className={`text-2xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-800'}`}>
                {data.summary.averageOverdueDays.toFixed(1)} days
              </h2>
            </div>
          </Card>
        </Col>
        <Col span={8}>
          <Card className={isDarkMode ? 'bg-black' : 'bg-white'}>
            <div className="text-center">
              <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Total Parties
              </p>
              <h2 className={`text-2xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-800'}`}>
                {data.summary.totalParties}
              </h2>
            </div>
          </Card>
        </Col>
      </Row>

      {/* Ageing Buckets Chart */}
      <Card title="Ageing Analysis" className={isDarkMode ? 'bg-black' : 'bg-white'}>
        <div style={{ height: 400 }}>
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={data.ageingBuckets}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="bucket" />
              <YAxis />
              <Tooltip
                formatter={(value) => `₹${Number(value).toLocaleString()}`}
                labelFormatter={(label) => `Age: ${label}`}
              />
              <Bar dataKey="amount" name="Amount">
                {data.ageingBuckets.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                ))}
              </Bar>
            </BarChart>
          </ResponsiveContainer>
        </div>
      </Card>

      {/* Details Table */}
      <Card title="Party Details" className={isDarkMode ? 'bg-black' : 'bg-white'}>
        <Table
          dataSource={data.details}
          columns={[
            {
              title: 'Party ID',
              dataIndex: 'partyId',
              key: 'partyId'
            },
            {
              title: 'Name',
              dataIndex: 'name',
              key: 'name'
            },
            {
              title: 'Total',
              dataIndex: 'total',
              key: 'total',
              render: (value) => `₹${value.toLocaleString()}`
            },
            {
              title: 'Current',
              dataIndex: ['buckets', 'current'],
              key: 'current',
              render: (value) => `₹${value.toLocaleString()}`
            },
            {
              title: '1-30 Days',
              dataIndex: ['buckets', '1-30'],
              key: '1-30',
              render: (value) => `₹${value.toLocaleString()}`
            },
            {
              title: '31-60 Days',
              dataIndex: ['buckets', '31-60'],
              key: '31-60',
              render: (value) => `₹${value.toLocaleString()}`
            },
            {
              title: '61-90 Days',
              dataIndex: ['buckets', '61-90'],
              key: '61-90',
              render: (value) => `₹${value.toLocaleString()}`
            },
            {
              title: '>90 Days',
              dataIndex: ['buckets', '>90'],
              key: '>90',
              render: (value) => `₹${value.toLocaleString()}`
            },
            {
              title: 'Last Payment',
              dataIndex: 'lastPaymentDate',
              key: 'lastPaymentDate',
              render: (date) => new Date(date).toLocaleDateString()
            }
          ]}
          pagination={false}
          scroll={{ x: true }}
        />
      </Card>
    </div>
  )
}

export default AgeingReport
