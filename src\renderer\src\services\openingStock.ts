import { Channels } from "@/common/constants";
import { http } from "./http";


interface OpeningStockItem {
    productId: string
    quantity: number
    purchasePrice: number
}

export const addOpeningStock = (items: OpeningStockItem[], adminId: string) => {
    return http.post(Channels.ADD_OPENING_STOCK, { body: { items, adminId } });
}

export const getOpeningStock = () => {
    return http.get(Channels.GET_OPENING_STOCK);
}
