import { Form, Input, Drawer, But<PERSON>, InputNumber } from 'antd'
import { partyApi } from '@/renderer/services'
import { useSelector } from 'react-redux'
import { IRootState } from '@/renderer/redux'
import { App } from 'antd'
import { usePartyContext } from '@/renderer/contexts'

interface CreateCreditorDrawerProps {
  open: boolean
  onClose: () => void
  onCreditorCreated: () => void
}

export const CreateCreditorDrawer = ({
  open,
  onClose,
  onCreditorCreated
}: CreateCreditorDrawerProps) => {
  const { message } = App.useApp()
  const [form] = Form.useForm()

  const user = useSelector((state: IRootState) => state.user.data)

  const { refreshCreditors } = usePartyContext()

  const handleSubmit = async (values: any) => {
    const response = await partyApi.createParty({
      ...values,
      type: 'CREDITOR',
      createdById: user?.id
    })

    if (response.error.error || response.data.error) {
      message.error(response.error.message || response.data.error.message)
      return
    }

    message.success('Creditor created successfully')
    refreshCreditors()
    form.resetFields()
    onCreditorCreated()
  }

  return (
    <Drawer
      title="Add New Creditor"
      placement="right"
      onClose={onClose}
      open={open}
      width={500}
      extra={
        <Button type="primary" onClick={() => form.submit()}>
          Create
        </Button>
      }
    >
      <Form form={form} layout="vertical" onFinish={handleSubmit}>
        <Form.Item
          name="name"
          label="Creditor Name"
          rules={[{ required: true, message: 'Please enter creditor name' }]}
        >
          <Input />
        </Form.Item>

        <Form.Item name="openingBalance" label="Opening Balance">
          <InputNumber className="w-full" addonBefore="PKR" />
        </Form.Item>

        <Form.Item name="contact" label="Contact Person">
          <Input />
        </Form.Item>

        <Form.Item name="phoneNumber" label="Phone Number">
          <Input />
        </Form.Item>

        <Form.Item name="address" label="Address">
          <Input.TextArea rows={4} />
        </Form.Item>
      </Form>
    </Drawer>
  )
}
