import { Card, Descriptions, Space, Tag, Typography } from 'antd'
import { useEffect, useState } from 'react'
import { useTheme } from '@/renderer/contexts'
import { FaBox, FaBarcode, FaTag, FaWarehouse } from 'react-icons/fa'
import { HiCurrencyRupee } from 'react-icons/hi'
import { BsFillBoxSeamFill } from 'react-icons/bs'
import type { SaleItemData } from '@/common/types'

const { Text, Title } = Typography

interface ProductDetailsCardProps {
  selectedProductId: string
  productDetails: any
  items: SaleItemData[]
}

export const ProductDetailsCard = ({
  selectedProductId,
  productDetails,
  items
}: ProductDetailsCardProps) => {
  const [isVisible, setIsVisible] = useState(false)
  const { isDarkMode } = useTheme()

  const isProductInInvoice = items.some((item) => item.productId === selectedProductId)

  useEffect(() => {
    if (selectedProductId) {
      // First hide the current product details
      setIsVisible(false)

      setTimeout(() => {
        setIsVisible(true)
      }, 500)
    } else {
      setIsVisible(false)
    }
  }, [selectedProductId])

  return (
    <div
      className={`overflow-hidden transition-[opacity,transform,max-height] duration-500 ${
        isVisible ? 'max-h-60 translate-y-0 opacity-100' : 'max-h-0 -translate-y-4 opacity-0'
      }`}
    >
      <Card
        className={`${
          isDarkMode
            ? 'bg-gradient-to-br from-indigo-950 via-black to-indigo-900 shadow-lg shadow-indigo-900'
            : 'bg-gradient-to-br from-indigo-200 via-white to-indigo-100 shadow-lg shadow-indigo-100'
        } ${isProductInInvoice ? 'opacity-50 shadow-inner shadow-red-500' : ''} `}
      >
        {productDetails && (
          <Space direction="vertical" size="middle" className="w-full">
            <div className="flex items-center justify-between">
              <Space>
                <BsFillBoxSeamFill className="text-2xl text-green-500" />
                <Title level={4} className="!mb-0">
                  {productDetails.name}
                </Title>
                <Tag color="green">{productDetails.nature}</Tag>
                {isProductInInvoice && <Tag color="red">Already in Invoice</Tag>}
              </Space>
              <div className="text-right">
                <Space direction="vertical" size="small">
                  <div className="flex items-center justify-end gap-2">
                    <HiCurrencyRupee className="text-xl text-green-500" />
                    <Text strong className="text-lg text-green-500">
                      {productDetails.salePrice.toLocaleString('en-US', {
                        minimumFractionDigits: 2
                      })}
                    </Text>
                  </div>
                </Space>
              </div>
            </div>

            <Descriptions column={2} size="small">
              <Descriptions.Item
                label={
                  <Space>
                    <FaBox className="text-gray-500" />
                    <Text>Product ID</Text>
                  </Space>
                }
              >
                {productDetails.productId}
              </Descriptions.Item>
              <Descriptions.Item
                label={
                  <Space>
                    <FaWarehouse className="text-gray-500" />
                    <Text>Stock</Text>
                  </Space>
                }
              >
                <Text
                  strong
                  className={productDetails.quantityInStock > 0 ? 'text-green-500' : 'text-red-500'}
                >
                  {productDetails.quantityInStock.toLocaleString('en-US')}
                </Text>
              </Descriptions.Item>
              <Descriptions.Item
                label={
                  <Space>
                    <FaBarcode className="text-gray-500" />
                    <Text>Barcode</Text>
                  </Space>
                }
              >
                {productDetails.barcode || 'N/A'}
              </Descriptions.Item>
              <Descriptions.Item
                label={
                  <Space>
                    <FaTag className="text-gray-500" />
                    <Text>Tag</Text>
                  </Space>
                }
              >
                {productDetails.tag || 'N/A'}
              </Descriptions.Item>
            </Descriptions>
          </Space>
        )}
      </Card>
    </div>
  )
}
