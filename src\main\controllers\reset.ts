import { resetService } from '../services'

class ResetController {
    // Ledger
    clearLedger = async () => {
        await resetService.clearLedger()
        return { success: true }
    }

    // Sales
    clearStockEntries = async () => {
        await resetService.clearStockEntries()
        return { success: true }
    }

    clearWalkInSaleItems = async () => {
        await resetService.clearWalkInSaleItems()
        return { success: true }
    }

    clearSaleItems = async () => {
        await resetService.clearSaleItems()
        return { success: true }
    }

    clearWalkInSaleInvoices = async () => {
        await resetService.clearWalkInSaleInvoices()
        return { success: true }
    }

    clearSaleInvoices = async () => {
        await resetService.clearSaleInvoices()
        return { success: true }
    }

    // Purchases
    clearPurchaseItems = async () => {
        await resetService.clearPurchaseItems()
        return { success: true }
    }

    clearStock = async () => {
        await resetService.clearStock()
        return { success: true }
    }

    clearPurchaseInvoices = async () => {
        await resetService.clearPurchaseInvoices()
        return { success: true }
    }

    // Products & Categories
    clearProducts = async () => {
        await resetService.clearProducts()
        return { success: true }
    }

    clearCategories = async () => {
        await resetService.clearCategories()
        return { success: true }
    }

    // Payments & Banks
    clearPayments = async () => {
        await resetService.clearPayments()
        return { success: true }
    }

    clearBanks = async () => {
        await resetService.clearBanks()
        return { success: true }
    }

    // Cash Management
    clearSmallCounter = async () => {
        await resetService.clearSmallCounter()
        return { success: true }
    }

    clearCashVault = async () => {
        await resetService.clearCashVault()
        return { success: true }
    }

    // Expenses
    clearExpenses = async () => {
        await resetService.clearExpenses()
        return { success: true }
    }

    // Invoice Numbers
    clearInvoiceNumbers = async () => {
        await resetService.clearInvoiceNumbers()
        return { success: true }
    }

    // Parties
    clearParties = async () => {
        await resetService.clearParties()
        return { success: true }
    }

    // Admin
    clearAdmins = async () => {
        await resetService.clearAdmins()
        return { success: true }
    }

    // Clear All Data
    clearAllData = async () => {
        await resetService.clearAllData()
        return { success: true }
    }
}

export const resetController = new ResetController()
