import { Table, Tag, Card, Space, DatePicker, Select, Form, Button, Modal, Input, App } from 'antd'
import { useEffect, useState } from 'react'
import { useApi } from '@/renderer/hooks'
import { paymentApi } from '@/renderer/services'
import { formatDate, formatCurrency } from '@/renderer/utils'
import { useTheme } from '@/renderer/contexts'
import { GetTransfersParams, GetTransfersResponse } from '@/common/types'
import { useSelector } from 'react-redux'
import { IRootState } from '@/renderer/redux'
import { DeleteOutlined } from '@ant-design/icons'

const { RangePicker } = DatePicker
const { TextArea } = Input

interface TransferListProps {
  refreshTrigger: number
}

export const TransferList = ({ refreshTrigger }: TransferListProps) => {
  const { isDarkMode } = useTheme()
  const [form] = Form.useForm()
  const [voidForm] = Form.useForm()
  const { message } = App.useApp()
  const user = useSelector((state: IRootState) => state.user.data)
  const [dateRange, setDateRange] = useState<[Date, Date] | null>(null)
  const [fromLocation, setFromLocation] = useState<'SMALL_COUNTER' | 'CASH_VAULT' | 'BANK' | null>(
    null
  )
  const [toLocation, setToLocation] = useState<'SMALL_COUNTER' | 'CASH_VAULT' | 'BANK' | null>(null)
  const [status, setStatus] = useState<'ACTIVE' | 'VOID' | 'ALL'>('ACTIVE')
  const [isVoidModalOpen, setIsVoidModalOpen] = useState(false)
  const [selectedTransferId, setSelectedTransferId] = useState<string | null>(null)
  const [voidLoading, setVoidLoading] = useState(false)
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0
  })

  const {
    data: transfers,
    isLoading,
    request: fetchTransfers
  } = useApi<GetTransfersResponse, [GetTransfersParams]>(async (params) => {
    return await paymentApi.getTransfers(params)
  })

  useEffect(() => {
    fetchTransfers({
      startDate: dateRange?.[0],
      endDate: dateRange?.[1],
      fromLocation: fromLocation || undefined,
      toLocation: toLocation || undefined,
      status: status === 'ALL' ? undefined : status,
      page: pagination.current,
      limit: pagination.pageSize
    })
  }, [
    dateRange,
    fromLocation,
    toLocation,
    status,
    refreshTrigger,
    pagination.current,
    pagination.pageSize
  ])

  // Update total when data changes
  useEffect(() => {
    if (transfers) {
      setPagination((prev) => ({
        ...prev,
        total: transfers.total
      }))
    }
  }, [transfers])

  const handleVoidTransfer = async () => {
    if (!selectedTransferId || !user?.id) return

    try {
      setVoidLoading(true)
      const values = await voidForm.validateFields()

      const response = await paymentApi.voidTransfer({
        id: selectedTransferId,
        adminId: user.id,
        reason: values.reason
      })

      if (response.error.error || response.data.error) {
        throw new Error(response.error.message || response.data.error.message)
      }

      message.success('Transfer voided successfully')
      setIsVoidModalOpen(false)
      voidForm.resetFields()
      setSelectedTransferId(null)
      fetchTransfers({
        startDate: dateRange?.[0],
        endDate: dateRange?.[1],
        fromLocation: fromLocation || undefined,
        toLocation: toLocation || undefined,
        status: status === 'ALL' ? undefined : status,
        page: pagination.current,
        limit: pagination.pageSize
      })
    } catch (error: any) {
      message.error(error.message || 'Failed to void transfer')
    } finally {
      setVoidLoading(false)
    }
  }

  const locationOptions = [
    { label: 'Small Counter', value: 'SMALL_COUNTER' },
    { label: 'Cash Vault', value: 'CASH_VAULT' },
    { label: 'Bank', value: 'BANK' }
  ]

  const statusOptions = [
    { label: 'Active', value: 'ACTIVE' },
    { label: 'Void', value: 'VOID' },
    { label: 'All', value: 'ALL' }
  ]

  const columns = [
    {
      title: 'Date',
      dataIndex: 'date',
      key: 'date',
      render: (date: string) => formatDate(date)
    },
    {
      title: 'Amount',
      dataIndex: 'amount',
      key: 'amount',
      align: 'right' as const,
      render: (amount: number) => formatCurrency(amount)
    },
    {
      title: 'Type',
      dataIndex: 'creditOrDebit',
      key: 'type',
      render: (type: string) => <Tag color={type === 'DEBIT' ? 'red' : 'green'}>{type}</Tag>
    },
    {
      title: 'Source',
      dataIndex: 'cashSource',
      key: 'source',
      render: (source: string) => source?.replace('_', ' ')
    },
    {
      title: 'Destination',
      dataIndex: 'cashDestination',
      key: 'destination',
      render: (destination: string) => destination?.replace('_', ' ')
    },
    {
      title: 'Bank',
      dataIndex: ['bank', 'name'],
      key: 'bank'
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => <Tag color={status === 'ACTIVE' ? 'green' : 'red'}>{status}</Tag>
    },
    {
      title: 'Created By',
      dataIndex: ['createdBy', 'name'],
      key: 'createdBy'
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_: any, record: any) => (
        <Space>
          {record.status === 'ACTIVE' && (
            <Button
              type="text"
              danger
              size="small"
              onClick={() => {
                setSelectedTransferId(record.id)
                setIsVoidModalOpen(true)
              }}
              icon={<DeleteOutlined />}
            />
          )}
        </Space>
      )
    }
  ]

  return (
    <Card
      className={`bg-[length:200%_200%] bg-[center] shadow-lg ${
        isDarkMode
          ? 'bg-gradient-to-bl from-indigo-950 via-black to-indigo-900 shadow-indigo-950'
          : 'bg-gradient-to-bl from-indigo-200 via-white to-indigo-100 shadow-indigo-200'
      }`}
    >
      <Form form={form} layout="vertical" className="mb-4">
        <Space wrap className="w-full">
          <Form.Item label="Date Range" className="!mb-0">
            <RangePicker
              onChange={(_, dateStrings) => {
                if (dateStrings[0] && dateStrings[1]) {
                  setDateRange([new Date(dateStrings[0]), new Date(dateStrings[1])])
                } else {
                  setDateRange(null)
                }
              }}
            />
          </Form.Item>
          <Form.Item label="From Location" className="!mb-0">
            <Select
              allowClear
              placeholder="Select source"
              options={locationOptions}
              value={fromLocation}
              onChange={setFromLocation}
              style={{ width: 200 }}
            />
          </Form.Item>
          <Form.Item label="To Location" className="!mb-0">
            <Select
              allowClear
              placeholder="Select destination"
              options={locationOptions}
              value={toLocation}
              onChange={setToLocation}
              style={{ width: 200 }}
            />
          </Form.Item>
          <Form.Item label="Status" className="!mb-0">
            <Select
              placeholder="Select status"
              options={statusOptions}
              value={status}
              onChange={setStatus}
              style={{ width: 150 }}
            />
          </Form.Item>
        </Space>
      </Form>

      <Table
        columns={columns}
        dataSource={transfers?.transfers || []}
        loading={isLoading}
        rowKey="id"
        virtual
        sticky
        size="small"
        pagination={{
          ...pagination,
          onChange: (page, pageSize) =>
            setPagination((prev) => ({
              ...prev,
              current: page,
              pageSize: pageSize || prev.pageSize
            })),
          position: ['topRight'],
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`,
          showPrevNextJumpers: true
        }}
      />

      <Modal
        title="Void Transfer"
        open={isVoidModalOpen}
        onOk={handleVoidTransfer}
        onCancel={() => {
          setIsVoidModalOpen(false)
          setSelectedTransferId(null)
          voidForm.resetFields()
        }}
        confirmLoading={voidLoading}
        okText="Void"
        okButtonProps={{ danger: true }}
      >
        <div>
          <p className="mb-4 text-red-500">Warning: This action cannot be undone!</p>
          <Form form={voidForm} layout="vertical">
            <Form.Item
              name="reason"
              label="Reason"
              rules={[{ required: true, message: 'Please enter a reason for voiding' }]}
            >
              <TextArea rows={3} placeholder="Enter reason for voiding" />
            </Form.Item>
          </Form>
        </div>
      </Modal>
    </Card>
  )
}
