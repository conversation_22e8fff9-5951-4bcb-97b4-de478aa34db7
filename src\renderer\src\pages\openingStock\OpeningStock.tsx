import { useState, useEffect } from 'react'
import {
  Form,
  Input,
  InputNumber,
  Button,
  Table,
  Space,
  App,
  Select,
  Tabs,
  Modal,
  Tag,
  Typography,
  Tooltip,
  Card
} from 'antd'

import { useTheme } from '@/renderer/contexts'
import { OpeningStockList, AddOpeningStock } from './components'

export const OpeningStock = () => {
  const [refreshTrigger, setRefreshTrigger] = useState(0)

  const { isDarkMode } = useTheme()

  const items = [
    {
      key: 'list',
      label: 'Opening Stock List',
      children: <OpeningStockList refreshTrigger={refreshTrigger} />
    },
    {
      key: 'add',
      label: 'Add Opening Stock',
      children: <AddOpeningStock setRefreshTrigger={setRefreshTrigger} />
    }
  ]

  return (
    <Card
      className={`m-6 bg-[length:200%_200%] bg-[center] shadow-lg ${
        isDarkMode
          ? 'bg-gradient-to-bl from-indigo-950 via-black to-indigo-900 shadow-indigo-950'
          : 'bg-gradient-to-bl from-indigo-200 via-white to-indigo-100 shadow-indigo-200'
      }`}
    >
      <Tabs items={items} className="" />
    </Card>
  )
}
