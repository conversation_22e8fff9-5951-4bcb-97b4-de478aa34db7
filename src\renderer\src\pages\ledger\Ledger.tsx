import { Tabs, Card } from 'antd'
import { DailyLedgerList } from './components/DailyLedgerList'
import { AccountLedgerList } from './components/AccountLedgerList'
import { useTheme } from '@/renderer/contexts'

const Ledger = () => {
  const { isDarkMode } = useTheme()

  return (
    <div style={{ padding: 24 }}>
      <Card
        className={`bg-[length:200%_200%] bg-[center] shadow-lg ${
          isDarkMode
            ? 'bg-gradient-to-bl from-indigo-950 via-black to-indigo-900 shadow-indigo-950'
            : 'bg-gradient-to-bl from-indigo-200 via-white to-indigo-100 shadow-indigo-200'
        }`}
      >
        <Tabs
          defaultActiveKey="party"
          items={[
            {
              key: 'party',
              label: 'Party Ledger',
              children: <AccountLedgerList />
            },
            {
              key: 'daily',
              label: 'Daily Ledger',
              children: <DailyLedgerList />
            }
          ]}
        />
      </Card>
    </div>
  )
}

export default Ledger
