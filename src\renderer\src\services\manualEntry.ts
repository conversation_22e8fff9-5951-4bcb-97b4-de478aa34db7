import { http } from './http';
import { Channels } from '@/common/constants';
import {
    CreateManualEntryData,
    GetManualEntriesParams,
    VoidManualEntryParams,
    ManualEntryStatus,
    ManualEntrySortOrder
} from '@/common/types/manualEntry';

export const createManualEntry = async (data: CreateManualEntryData) => {
    return await http.post(Channels.CREATE_MANUAL_ENTRY, {
        body: data
    });
};

export const getManualEntries = async (params: GetManualEntriesParams) => {
    return await http.get(Channels.GET_MANUAL_ENTRIES, {
        body: params
    });
};

export const getManualEntryById = async (id: string) => {
    return await http.get(Channels.GET_MANUAL_ENTRY_BY_ID, {
        params: { id }
    });
};

export const voidManualEntry = async (data: VoidManualEntryParams) => {
    return await http.post(Channels.VOID_MANUAL_ENTRY, {
        body: data
    });
};

// Export enums for frontend use
export { ManualEntryStatus, ManualEntrySortOrder };

