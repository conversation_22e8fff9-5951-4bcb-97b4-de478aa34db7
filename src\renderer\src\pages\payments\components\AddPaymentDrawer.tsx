import {
  Drawer,
  Form,
  Radio,
  Select,
  DatePicker,
  Input,
  InputNumber,
  Space,
  Button,
  App,
  Card,
  Typography
} from 'antd'
import { useState, useEffect } from 'react'
import { PaymentSourceSelect } from './PaymentSourceSelect'
import { PaymentDetailsCard } from './PaymentDetailCard'
// import { PartySelect } from '@/renderer/components'
import { paymentApi } from '@/renderer/services'
import { usePartyContext } from '@/renderer/contexts'
import { useSelector } from 'react-redux'
import { IRootState } from '@/renderer/redux'
import { handleDatePickerValue } from '@/renderer/utils'

const { Text } = Typography

interface AddPaymentDrawerProps {
  open: boolean
  onClose: () => void
  setRefreshTrigger: (trigger: any) => void
}

export const AddPaymentDrawer = ({ open, onClose, setRefreshTrigger }: AddPaymentDrawerProps) => {
  const [form] = Form.useForm()
  const [partyType, setPartyType] = useState<'ALL' | 'VENDOR' | 'CUSTOMER' | 'CREDITOR'>('ALL')
  const { message } = App.useApp()
  const { customers, creditors, vendors } = usePartyContext()
  const user = useSelector((state: IRootState) => state.user.data)

  // Watch form values for dynamic updates
  const paymentType = Form.useWatch('type', form)
  const source = Form.useWatch('Source', form)
  const amount = Form.useWatch('amount', form)
  const selectedBank = Form.useWatch('bankId', form)

  useEffect(() => {
    form.setFieldValue('bankId', null)
  }, [source])

  const handleSubmit = async (values: any) => {
    try {
      const response = await paymentApi.createPayment({
        type: values.type,
        partyId: values.partyId,
        amount: values.amount,
        date: handleDatePickerValue(values.date.toDate()),
        source: values.Source,
        locationId: values.Source === 'BANK' ? values.bankId : undefined,
        description: values.description,
        paymentMethod: values.paymentMethod,
        createdById: user?.id || ''
      })

      if (response.error.error || response.data.error) {
        throw new Error(response.error.message || response.data.error.message)
      }

      message.success('Payment created successfully')
      setRefreshTrigger((prev: number) => prev + 1)
      form.resetFields()
      onClose()
    } catch (error: any) {
      message.error(error.message || 'Failed to create payment')
    }
  }

  const handleClose = () => {
    form.resetFields()
    onClose()
  }

  const getFilteredParties = () => {
    switch (partyType) {
      case 'VENDOR':
        return vendors
      case 'CUSTOMER':
        return customers
      case 'CREDITOR':
        return creditors
      default:
        return [...vendors, ...customers, ...creditors]
    }
  }

  const partyTypeOptions = [
    { label: 'All Parties', value: 'ALL' },
    { label: 'Vendors', value: 'VENDOR' },
    { label: 'Customers', value: 'CUSTOMER' },
    { label: 'Creditors', value: 'CREDITOR' }
  ]

  return (
    <Drawer title="Add Payment" placement="right" width={720} onClose={handleClose} open={open}>
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        initialValues={{
          type: 'PAID',
          date: null,
          amount: 0
        }}
      >
        <Form.Item
          name="type"
          label="Payment Type"
          rules={[{ required: true, message: 'Please select payment type' }]}
        >
          <Radio.Group>
            <Radio.Button value="PAID">Paid</Radio.Button>
            <Radio.Button value="RECEIVED">Received</Radio.Button>
          </Radio.Group>
        </Form.Item>

        <Form.Item label="Party Type">
          <Select
            placeholder="Select party type"
            options={partyTypeOptions}
            value={partyType}
            onChange={(value) => {
              setPartyType(value)
              form.setFieldValue('partyId', null)
            }}
          />
        </Form.Item>

        <Form.Item
          name="partyId"
          label="Party"
          rules={[{ required: true, message: 'Please select a party' }]}
        >
          <Select
            allowClear
            showSearch
            placeholder="Select party"
            options={getFilteredParties()}
            filterOption={(input, option) =>
              (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
            }
          />
        </Form.Item>

        <Form.Item
          name="date"
          label="Date"
          rules={[{ required: true, message: 'Please select date' }]}
        >
          <DatePicker className="w-full" />
        </Form.Item>

        <Form.Item
          name="amount"
          label="Amount"
          rules={[{ required: true, message: 'Please enter amount' }]}
        >
          <InputNumber
            className="w-full"
            formatter={(value) => `PKR ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
            parser={(value) => value!.replace(/PKR\s?|(,*)/g, '')}
          />
        </Form.Item>

        <Form.Item
          name="paymentMethod"
          label="Payment Method"
          rules={[{ required: true, message: 'Please select payment method' }]}
        >
          <Radio.Group>
            <Radio.Button value="CASH">Cash</Radio.Button>
            <Radio.Button value="BANK_TRANSFER">Bank Transfer</Radio.Button>
            <Radio.Button value="CHEQUE">Cheque</Radio.Button>
          </Radio.Group>
        </Form.Item>

        <PaymentSourceSelect form={form} />

        <PaymentDetailsCard source={source} selectedBank={selectedBank} paidAmount={amount || 0} />

        <Form.Item
          name="description"
          label="Description"
          rules={[{ required: true, message: 'Please enter description' }]}
        >
          <Input.TextArea rows={4} />
        </Form.Item>

        <div className="absolute bottom-0 left-0 right-0 border-t p-4">
          <Space className="w-full justify-end">
            <Button onClick={handleClose}>Cancel</Button>
            <Button type="primary" htmlType="submit" className="bg-blue-500">
              Submit
            </Button>
          </Space>
        </div>
      </Form>
    </Drawer>
  )
}
