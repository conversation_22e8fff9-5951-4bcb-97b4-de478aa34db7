import { productApi } from '@/renderer/services'
import { App } from 'antd'

export const fetchProductDetails = async (
    productId: string,
    message: any
) => {
    if (!productId) return null

    const response = await productApi.getProduct(productId)

    if (response.error.error || response.data.error) {
        message.error(response.error.message || response.data.error.message)
        return null
    }

    return response.data.data
} 