import { prisma } from '../db';
import { Prisma, PartyType } from '@prisma/client';
import dayjs from 'dayjs';
import { processDateRange } from '../utils/helperFunctions';
import {
    DailyOverview,
    TimelineData,
    TopEntity,
    ProductWithStock,
    StockValueOverview,
    CashFlowOverview,
    PartyOverview,
    SalesDistribution,
    PaymentMethodDistribution,
    TimeRange
} from '@/common/types/dashBoard';

class DashboardService {
    // UI: Stats cards with comparison
    async getDailySalesOverview(): Promise<DailyOverview> {
        const today = new Date();
        const yesterday = dayjs(today).subtract(1, 'day').toDate();

        const [todaySales, yesterdaySales] = await Promise.all([
            prisma.saleInvoice.aggregate({
                where: {
                    date: {
                        gte: dayjs(today).startOf('day').toDate(),
                        lte: dayjs(today).endOf('day').toDate()
                    },
                    status: 'ACTIVE'
                },
                _count: { id: true },
                _sum: { totalAmount: true }
            }),
            prisma.saleInvoice.aggregate({
                where: {
                    date: {
                        gte: dayjs(yesterday).startOf('day').toDate(),
                        lte: dayjs(yesterday).endOf('day').toDate()
                    },
                    status: 'ACTIVE'
                },
                _count: { id: true },
                _sum: { totalAmount: true }
            })
        ]);

        const todayAvg = todaySales._count.id > 0
            ? (todaySales._sum.totalAmount || 0) / todaySales._count.id
            : 0;
        const yesterdayAvg = yesterdaySales._count.id > 0
            ? (yesterdaySales._sum.totalAmount || 0) / yesterdaySales._count.id
            : 0;

        return {
            totalAmount: todaySales._sum.totalAmount || 0,
            totalInvoices: todaySales._count.id,
            averageInvoiceValue: todayAvg,
            comparisonWithPrevious: {
                amount: yesterdaySales._sum.totalAmount || 0,
                invoices: yesterdaySales._count.id,
                average: yesterdayAvg
            }
        };
    }

    // UI: Line chart with selectable time range
    async getSalesTimeline({ days }: TimeRange): Promise<TimelineData[]> {
        const startDate = days === 1
            ? dayjs().startOf('day').toDate()  // Today only
            : dayjs().subtract(days - 1, 'day').startOf('day').toDate(); // Start of period (inclusive)
        const endDate = dayjs().endOf('day').toDate();

        const sales = await prisma.saleInvoice.groupBy({
            by: ['date'],
            where: {
                date: { gte: startDate, lte: endDate },
                status: 'ACTIVE'
            },
            _count: { id: true },
            _sum: { totalAmount: true }
        });

        return sales.map(sale => ({
            date: dayjs(sale.date).format('YYYY-MM-DD'),
            totalAmount: sale._sum.totalAmount || 0,
            totalInvoices: sale._count.id
        }));
    }

    // UI: Bar chart with top 5 items
    async getTopProducts({ days }: TimeRange): Promise<TopEntity[]> {
        const startDate = days === 1
            ? dayjs().startOf('day').toDate()  // Today only
            : dayjs().subtract(days - 1, 'day').startOf('day').toDate(); // Start of period (inclusive)

        const topProducts = await prisma.saleItem.groupBy({
            by: ['productId'],
            where: {
                saleInvoice: {
                    date: { gte: startDate },
                    status: 'ACTIVE'
                }
            },
            _sum: { total: true, totalQuantity: true },
            take: 5,
            orderBy: {
                _sum: {
                    total: 'desc'
                }
            }
        });

        const productDetails = await prisma.product.findMany({
            where: {
                id: { in: topProducts.map(p => p.productId) }
            },
            select: { id: true, name: true }
        });

        return topProducts.map(product => {
            const details = productDetails.find(p => p.id === product.productId);
            return {
                id: product.productId,
                name: details?.name || 'Unknown Product',
                amount: product._sum.total || 0,
                count: product._sum.totalQuantity || 0
            };
        });
    }

    // UI: Table with warning indicators
    async getLowStockProducts(): Promise<ProductWithStock[]> {
        const products = await prisma.product.findMany({
            where: {
                minStockLevel: { not: null },
                quantityInStock: {
                    lte: prisma.product.fields.minStockLevel
                }
            },
            select: {
                id: true,
                name: true,
                productId: true,
                quantityInStock: true,
                minStockLevel: true,
                category: {
                    select: { name: true }
                }
            },
            take: 10,
            orderBy: [
                {
                    quantityInStock: 'asc'
                }
            ]
        });

        // Filter out products with null minStockLevel and map to required type
        return products
            .filter((product): product is (typeof product & { minStockLevel: number }) =>
                product.minStockLevel !== null
            )
            .map(product => ({
                id: product.id,
                name: product.name,
                productId: product.productId,
                quantityInStock: product.quantityInStock,
                minStockLevel: product.minStockLevel,
                category: product.category
            }));
    }

    // UI: Stats cards with icons
    async getStockValueOverview(): Promise<StockValueOverview> {
        const [stockValues, productsCount, lowStockCount] = await Promise.all([
            // Get both purchase and sale values for in-stock items
            prisma.$queryRaw<Array<{ purchaseValue: number, saleValue: number }>>`
                SELECT 
                    SUM(s.quantity * s."purchasePrice") as "purchaseValue",
                    SUM(s.quantity * p."salePrice") as "saleValue"
                FROM "Stock" s
                JOIN "Product" p ON s."productId" = p.id
                WHERE s.status = 'IN_STOCK'
            `,
            prisma.product.count(),
            prisma.product.count({
                where: {
                    minStockLevel: { not: null },
                    quantityInStock: {
                        lte: prisma.product.fields.minStockLevel
                    }
                }
            })
        ]);

        const stockValue = stockValues[0] || { purchaseValue: 0, saleValue: 0 };

        return {
            purchase: {
                totalValue: stockValue.purchaseValue || 0,
                averageValue: productsCount > 0 ? (stockValue.purchaseValue || 0) / productsCount : 0
            },
            sale: {
                totalValue: stockValue.saleValue || 0,
                averageValue: productsCount > 0 ? (stockValue.saleValue || 0) / productsCount : 0
            },
            totalProducts: productsCount,
            lowStockCount
        };
    }

    // UI: Stats cards with up/down indicators
    async getCashFlowOverview(): Promise<CashFlowOverview> {
        const yesterday = dayjs().subtract(1, 'day').toDate();
        const today = new Date();

        const [banks, vault, counter, yesterdayLedger, todayLedger] = await Promise.all([
            prisma.banks.aggregate({
                _sum: { balance: true }
            }),
            prisma.cashVault.findFirst(),
            prisma.smallCounter.findFirst(),
            // Get yesterday's ledger entries
            prisma.ledger.groupBy({
                by: ['cashDestination'],
                where: {
                    date: {
                        gte: dayjs(yesterday).startOf('day').toDate(),
                        lte: dayjs(yesterday).endOf('day').toDate()
                    },
                    status: 'ACTIVE'
                },
                _sum: { amount: true }
            }),
            // Get today's ledger entries
            prisma.ledger.groupBy({
                by: ['cashDestination'],
                where: {
                    date: {
                        gte: dayjs(today).startOf('day').toDate(),
                        lte: dayjs(today).endOf('day').toDate()
                    },
                    status: 'ACTIVE'
                },
                _sum: { amount: true }
            })
        ]);

        // Calculate changes for each destination
        const calculateChange = (destination: string) => {
            const todayEntry = todayLedger.find(e => e.cashDestination === destination);
            const yesterdayEntry = yesterdayLedger.find(e => e.cashDestination === destination);
            return (todayEntry?._sum.amount || 0) - (yesterdayEntry?._sum.amount || 0);
        };

        return {
            bank: {
                totalBalance: banks._sum.balance || 0,
                lastDayChange: calculateChange('BANK')
            },
            vault: {
                balance: vault?.balance || 0,
                lastDayChange: calculateChange('CASH_VAULT')
            },
            counter: {
                balance: counter?.cashInShop || 0,
                lastDayChange: calculateChange('SMALL_COUNTER')
            }
        };
    }

    // UI: Stats cards with percentages
    async getPartyOverview(): Promise<PartyOverview> {
        const startOfMonth = dayjs().startOf('month').toDate();

        const [customers, vendors, creditors] = await Promise.all([
            this.getPartyTypeStats('CUSTOMER', startOfMonth),
            this.getPartyTypeStats('VENDOR', startOfMonth),
            this.getPartyTypeStats('CREDITOR', startOfMonth)
        ]);

        return { customers, vendors, creditors };
    }

    private async getPartyTypeStats(type: PartyType, startOfMonth: Date) {
        const [total, newThisMonth, activeTransactions] = await Promise.all([
            prisma.party.count({
                where: { type }
            }),
            prisma.party.count({
                where: {
                    type,
                    createdAt: { gte: startOfMonth }
                }
            }),
            prisma.ledger.groupBy({
                by: ['partyId'],
                where: {
                    party: { type },
                    date: { gte: startOfMonth }
                }
            })
        ]);

        return {
            total,
            active: activeTransactions.length,
            newThisMonth
        };
    }

    // UI: Pie chart
    async getSalesDistribution({ days }: TimeRange): Promise<SalesDistribution> {
        const startDate = days === 1
            ? dayjs().startOf('day').toDate()  // Today only
            : dayjs().subtract(days - 1, 'day').startOf('day').toDate(); // Start of period (inclusive)

        const [walkIn, registered] = await Promise.all([
            prisma.walkInSaleInvoice.aggregate({
                where: {
                    date: { gte: startDate },
                    status: 'ACTIVE'
                },
                _count: { id: true },
                _sum: { totalAmount: true }
            }),
            prisma.saleInvoice.aggregate({
                where: {
                    date: { gte: startDate },
                    status: 'ACTIVE'
                },
                _count: { id: true },
                _sum: { totalAmount: true }
            })
        ]);

        return {
            walkIn: {
                amount: walkIn._sum.totalAmount || 0,
                count: walkIn._count.id
            },
            registered: {
                amount: registered._sum.totalAmount || 0,
                count: registered._count.id
            }
        };
    }

    // UI: Pie chart
    async getPaymentMethodDistribution({ days }: TimeRange): Promise<PaymentMethodDistribution[]> {
        const startDate = days === 1
            ? dayjs().startOf('day').toDate()  // Today only
            : dayjs().subtract(days - 1, 'day').startOf('day').toDate(); // Start of period (inclusive)

        const payments = await prisma.payments.groupBy({
            by: ['paymentMethod'],
            where: {
                date: { gte: startDate },
                status: 'ACTIVE'
            },
            _count: { id: true },
            _sum: { amount: true }
        });

        return payments.map(payment => ({
            method: payment.paymentMethod,
            amount: payment._sum.amount || 0,
            count: payment._count.id
        }));
    }
}

export const dashboardService = new DashboardService();

