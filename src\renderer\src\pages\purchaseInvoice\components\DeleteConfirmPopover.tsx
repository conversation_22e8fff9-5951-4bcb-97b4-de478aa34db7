import { Popover, Button, Space } from 'antd'
import { FaTrash, FaTrashAlt } from 'react-icons/fa'
import { useState } from 'react'

interface DeleteConfirmPopoverProps {
  onConfirm: () => void
}

export const DeleteConfirmPopover = ({ onConfirm }: DeleteConfirmPopoverProps) => {
  const [visible, setVisible] = useState(false)
  const [isHovered, setIsHovered] = useState(false)

  const content = (
    <Space>
      <Button size="small" onClick={() => setVisible(false)}>
        No
      </Button>
      <Button
        size="small"
        type="primary"
        danger
        onClick={() => {
          onConfirm()
          setVisible(false)
        }}
      >
        Yes
      </Button>
    </Space>
  )

  return (
    <Popover
      content={content}
      title="Are you sure you want to void this invoice?"
      trigger="click"
      open={visible}
      onOpenChange={setVisible}
    >
      <div
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
        style={{
          backgroundColor: 'cyan',
          cursor: 'pointer',
          justifyContent: 'center',
          display: 'flex',
          position: 'relative',
          alignItems: 'center',
          marginLeft: 'auto',
          marginRight: 'auto'
        }}
      >
        <FaTrash
          color="#ff4d4f"
          className="transition-all duration-300 absolute"
          style={{
            opacity: isHovered ? 0 : 1,
            transform: isHovered ? 'scale(0)' : 'scale(1)'
          }}
        />
        <FaTrashAlt
          color="#ff4d4f"
          className="transition-all duration-300 absolute"
          style={{
            opacity: isHovered ? 1 : 0,
            transform: isHovered ? 'scale(1.4)' : 'scale(0)'
          }}
        />
      </div>
    </Popover>
  )
}
