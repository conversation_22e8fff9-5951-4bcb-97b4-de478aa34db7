{
  "extends": "@electron-toolkit/tsconfig/tsconfig.web.json",
  "include": [
    "src/renderer/src/env.d.ts",
    "src/renderer/src/**/*",
    "src/renderer/src/**/*.tsx",
    "src/preload/*.d.ts",
    "src/common"
  ],
  "compilerOptions": {
    "composite": true,
    "jsx": "react-jsx",
    "baseUrl": ".",
    "paths": {
      "@/renderer/*": [
        "src/renderer/src/*"
      ],
      "@/common": [
        "src/common"
      ],
      "@/common/*": [
        "src/common/*"
      ],
    }
  }
}