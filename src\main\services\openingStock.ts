import { prisma } from '../db'
import { Status, StockStatus } from '@prisma/client'
import dayjs from 'dayjs'
import { OpeningStockItem, GetOpeningStockParams } from '@/common/types'


class OpeningStockService {
    private async getOrCreateOpeningStockVendor(adminId: string) {
        const openingStockVendor = await prisma.party.findFirst({
            where: {
                name: 'OPENING_STOCK',
                type: 'VENDOR'
            }
        })

        if (openingStockVendor) {
            return openingStockVendor
        }

        return await prisma.party.create({
            data: {
                name: 'OPENING_STOCK',
                type: 'VENDOR',
                currentBalance: 0 // This vendor's balance will always remain 0
            }
        })
    }

    private generateOpeningStockInvoiceNumber(): string {
        return `O_STK_${dayjs().format('YYYY/MM/DD_HH:mm:ss')}`
    }

    async addOpeningStock(items: OpeningStockItem[], adminId: string) {
        try {
            // Validate items
            if (!items || items.length === 0) {
                throw new Error('No items provided for opening stock')
            }

            // Validate quantities and prices
            for (const item of items) {
                if (item.quantity <= 0) {
                    throw new Error(`Quantity must be positive for product ${item.productId}`)
                }
                if (item.purchasePrice < 0) {
                    throw new Error(`Purchase price cannot be negative for product ${item.productId}`)
                }
            }

            return await prisma.$transaction(async (tx) => {
                // Get or create the special opening stock vendor
                const openingStockVendor = await this.getOrCreateOpeningStockVendor(adminId)

                // Create a special purchase invoice for opening stock with unique invoice number
                const purchaseInvoice = await tx.purchaseInvoice.create({
                    data: {
                        invoiceNumber: this.generateOpeningStockInvoiceNumber(),
                        totalAmount: 0, // Since this is opening stock, we don't want it to affect any balances
                        paidAmount: 0,
                        previousBalance: 0,
                        newBalance: 0,
                        type: 'OPENING_STOCK',
                        date: new Date(),
                        vendorId: openingStockVendor.id,
                        createdById: adminId,
                        status: Status.ACTIVE
                    }
                })

                // Prepare batch data for purchase items
                const purchaseItemsData = items.map(item => ({
                    quantity: item.quantity,
                    purchasePrice: item.purchasePrice,
                    total: item.quantity * item.purchasePrice,
                    purchaseInvoiceId: purchaseInvoice.id,
                    productId: item.productId
                }));

                // Batch create purchase items
                await tx.purchaseItem.createMany({
                    data: purchaseItemsData
                });

                // Prepare batch data for stock entries
                const stockEntriesData = items.map(item => ({
                    purchasePrice: item.purchasePrice,
                    quantity: item.quantity,
                    status: StockStatus.IN_STOCK,
                    productId: item.productId,
                    vendorId: openingStockVendor.id,
                    purchaseInvoiceId: purchaseInvoice.id
                }));

                // Batch create stock entries
                await tx.stock.createMany({
                    data: stockEntriesData
                });

                // Update products' quantities in bulk by using separate update calls in parallel
                // (Prisma doesn't support batch increment operations, but we can run them in parallel)
                await Promise.all(
                    items.map(item =>
                        tx.product.update({
                            where: { id: item.productId },
                            data: {
                                quantityInStock: {
                                    increment: item.quantity
                                }
                            }
                        })
                    )
                );

                return purchaseInvoice
            }, {
                timeout: 30000 // 30 seconds timeout
            })
        } catch (error: any) {
            // Enhance error messages for timeout issues
            if (error.message && (
                error.message.includes('Transaction already closed') ||
                error.message.includes('timeout') ||
                error.message.includes('Transaction not found')
            )) {
                throw new Error(
                    `Operation timed out. The input size (${items.length} items) was too large to be processed at once. ` +
                    `Please try reducing the number of items per batch or split into multiple operations. ` +
                    `Original error: ${error.message}`
                );
            }
            throw error;
        }
    }

    // Get opening stock with optional filters
    async getOpeningStock(params?: GetOpeningStockParams) {
        const openingStockVendor = await prisma.party.findFirst({
            where: {
                name: 'OPENING_STOCK',
                type: 'VENDOR'
            }
        })

        if (!openingStockVendor) {
            return {
                openingStock: [],
                total: 0,
                page: 1,
                totalPages: 0
            }
        }

        const page = params?.page || 1
        const limit = params?.limit || 10
        const skip = (page - 1) * limit

        const where = {
            vendorId: openingStockVendor.id,
            type: 'OPENING_STOCK' as const,
            ...(params?.startDate && { date: { gte: params.startDate } }),
            ...(params?.endDate && { date: { lte: params.endDate } }),
            ...(params?.productId && {
                items: {
                    some: {
                        productId: params.productId
                    }
                }
            })
        }

        const [openingStock, total] = await Promise.all([
            prisma.purchaseInvoice.findMany({
                where,
                include: {
                    items: {
                        include: {
                            product: true
                        }
                    },
                    createdBy: true
                },
                orderBy: {
                    date: 'desc'
                },
                skip,
                take: limit
            }),
            prisma.purchaseInvoice.count({ where })
        ])

        return {
            openingStock,
            total,
            page,
            totalPages: Math.ceil(total / limit)
        }
    }
}

export const openingStockService = new OpeningStockService();
