import { Prisma } from '@prisma/client';
import { IRequest } from '../../common';
import { productService } from '../services';
import { ProductQuantityReportParams, UpdateProductData } from '@/common/types';

interface CreateProductData {
    name: string;
    categoryId: string;
    productId: string;
    nature?: string;
    tag?: string;
    minStockLevel?: number;
    salePrice: number;
}

interface CreateProductData extends Prisma.ProductCreateInput { }

class ProductController {
    async createProduct(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const productData: CreateProductData = req.body as CreateProductData;

        if (!productData.name || !productData.categoryId || !productData.productId || !productData.salePrice) {
            throw new Error("Missing required fields for product creation.");
        }

        return await productService.createProduct(productData);
    }

    async updateProduct(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { id } = req.params ?? {};
        const data = req.body as UpdateProductData;

        if (!id) throw new Error("Product ID is required");

        // Check if at least one field is provided for update
        if (!data.salePrice && !data.minStockLevel && data.nature === undefined && data.tag === undefined) {
            throw new Error("At least one field (salePrice, minStockLevel, nature, or tag) must be provided");
        }

        return await productService.updateProduct(id, data);
    }

    async getProduct(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { id } = req.params ?? {};
        if (!id) throw new Error("Product ID is required");

        return await productService.getProductById(id);
    }

    async deleteProduct(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { id } = req.params ?? {};
        if (!id) throw new Error("Product ID is required");

        return await productService.deleteProduct(id);
    }

    async getProducts(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { page = 1, limit = 10, search } = req.query ?? {};
        return await productService.getProducts({ page, limit, search });
    }

    async getProductsForSelect(_event: Electron.IpcMainInvokeEvent) {
        return await productService.getProductsForSelect();
    }

    async getProductQuantityReport(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        return await productService.getProductQuantityReport(req.query as ProductQuantityReportParams)

    }
}

export const productController = new ProductController();