import { CashLocation, SaleInvoice, Status, WalkInSaleInvoice } from "@prisma/client";

// Subset of CashLocation that excludes 'ACCOUNT'
export type PaymentLocation = Extract<CashLocation, 'BANK' | 'CASH_VAULT' | 'SMALL_COUNTER'>;

export interface SaleItemData {
    productId: string;
    totalQuantity: number;
    salePrice: number;
}

// export interface CreateSaleInvoiceData {
//     invoiceNumber: string;
//     customerId: string;
//     discountAmount: number;
//     paidAmount: number;
//     date: Date;
//     createdById: string;
//     items: SaleItemData[];
//     paymentMethod?: 'CASH' | 'BANK_TRANSFER';
//     bankId?: string;
//     source?: PaymentLocation;
// }

// export interface CreateWalkInSaleData {
//     invoiceNumber: string;
//     customerName?: string;
//     discountAmount: number;
//     paidAmount: number;
//     date: Date;
//     createdById: string;
//     items: SaleItemData[];
//     paymentMethod: 'CASH' | 'BANK_TRANSFER';
//     bankId?: string;
//     source: PaymentLocation;
// }


// Form Data Types
export interface SaleInvoiceFormData {
    customerId?: string;
    customerName?: string;
    date: Date;
    items: SaleItemData[];
    discountAmount: number;
    paidAmount: number;
    paymentMethod?: 'CASH' | 'BANK_TRANSFER';
    bankId?: string;
    source?: PaymentLocation;
    invoiceNumber: string;
    createdById: string;
}

export interface VoidSaleData {
    id: string;
    adminId: string;
    reason: string;
}

export interface GetSaleInvoicesParams {
    page?: number;
    limit?: number;
    search?: string;
    startDate?: Date;
    endDate?: Date;
    partyId?: string;
    productId?: string;
    status?: Status | 'ALL';
    invoiceType?: 'REGISTERED' | 'WALK_IN';
}

export interface GetSaleInvoicesResponse {
    data: Array<SaleInvoice | WalkInSaleInvoice>;
    total: number;
    page: number;
    totalPages: number;
}



export type SaleInvoiceType = 'REGISTERED' | 'WALK_IN';

export interface SaleInvoiceComponentProps {
    onBack: () => void;
    onSuccess: () => void;
    type: SaleInvoiceType;
    createMode: 'REGISTERED' | 'WALK_IN' | 'none';
}

export interface InvoiceFormProps {
    formData: Pick<SaleInvoiceFormData, 'customerId' | 'customerName' | 'date'>;
    onChange: (data: Partial<Pick<SaleInvoiceFormData, 'customerId' | 'customerName' | 'date'>>) => void;
    type: SaleInvoiceType;
}

export interface ItemsTableProps {
    items: SaleItemData[];
    onItemsChange: (items: SaleItemData[]) => void;
    selectedProductId: string;
    setSelectedProductId: (id: string) => void;
}

export interface SummaryProps {
    items: SaleItemData[];
    formData: Pick<SaleInvoiceFormData, 'discountAmount' | 'paidAmount' | 'paymentMethod' | 'bankId' | 'source'>;
    onChange: (data: Partial<Pick<SaleInvoiceFormData, 'discountAmount' | 'paidAmount' | 'paymentMethod' | 'bankId' | 'source'>>) => void;
    onSubmit: () => void;
    loading: boolean;
    type: SaleInvoiceType;
}

export interface CustomerDetailsCardProps {
    formData: Pick<SaleInvoiceFormData, 'customerId'>;
}

export interface ProductDetailsCardProps {
    selectedProductId: string;
}
