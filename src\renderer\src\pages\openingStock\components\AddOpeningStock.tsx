import { useRef, useState, useMemo, useCallback } from 'react'
import {
  Form,
  InputNumber,
  Button,
  Table,
  Space,
  App,
  Select,
  Modal,
  Card,
  Typography,
  Input
} from 'antd'
import { useProductContext } from '@/renderer/contexts'
import { openingStockApi } from '@/renderer/services'
import { useSelector } from 'react-redux'
import { IRootState } from '@/renderer/redux'
import { useTheme } from '@/renderer/contexts'
import type { SelectProps } from 'antd/es/select'
import { SearchOutlined, DeleteOutlined } from '@ant-design/icons'

const { Text } = Typography

interface OpeningStockItem {
  productId: string
  quantity: number
  purchasePrice: number
  key?: string // Adding key for optimization
}

interface AddOpeningStockProps {
  setRefreshTrigger: (trigger: any) => void
}

export const AddOpeningStock = ({ setRefreshTrigger }: AddOpeningStockProps) => {
  const [form] = Form.useForm()
  const [items, setItems] = useState<OpeningStockItem[]>([])
  const [loading, setLoading] = useState(false)
  const [confirmModalVisible, setConfirmModalVisible] = useState(false)
  const [searchText, setSearchText] = useState('')
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize, setPageSize] = useState(50) // Default page size
  const { products, refreshProducts } = useProductContext()
  const { message } = App.useApp()
  const user = useSelector((state: IRootState) => state.user.data)
  const { isDarkMode } = useTheme()

  // Create a ref for the Select component
  const selectRef = useRef<any>(null)

  // Memoize available products to prevent recalculation on every render
  const availableProducts = useMemo(() => {
    const usedProductIds = new Set(items.map((item) => item.productId))
    return products.filter((product) => !usedProductIds.has(product.value))
  }, [products, items])

  // Calculate total in a memoized way
  const totalAmount = useMemo(() => {
    return items.reduce((sum, item) => sum + item.quantity * item.purchasePrice, 0)
  }, [items])

  // Handle item addition optimized with useCallback
  const handleAddItem = useCallback(async () => {
    try {
      const values = await form.validateFields()

      // Check if product already exists in items
      if (items.some((item) => item.productId === values.productId)) {
        message.error('This product is already in the list')
        return
      }

      // Generate a unique key for this item
      const newItem = {
        ...values,
        key: `${values.productId}-${Date.now()}`
      }

      setItems((prevItems) => [...prevItems, newItem])
      form.resetFields()

      // Focus the select component after a short delay to ensure it's ready
      setTimeout(() => {
        selectRef.current?.focus()
      }, 0)
    } catch (error) {
      // Form validation error, no need to handle
    }
  }, [form, items, message])

  // Handle item removal optimized with useCallback
  const handleRemoveItem = useCallback((key: string) => {
    setItems((prevItems) => prevItems.filter((item) => item.key !== key))
  }, [])

  // Handle batch submission
  const handleSubmit = useCallback(async () => {
    if (items.length === 0) {
      message.error('Please add at least one item')
      return
    }

    if (items.length > 1000) {
      message.warning(
        'Large number of items detected. This operation might take some time or time out. Consider submitting in smaller batches.'
      )
    }

    setLoading(true)
    try {
      // Remove the key property before sending to API
      const itemsToSubmit = items.map(({ productId, quantity, purchasePrice }) => ({
        productId,
        quantity,
        purchasePrice
      }))

      const response = await openingStockApi.addOpeningStock(itemsToSubmit, user?.id || '')

      if (response.error.error || response.data.error) {
        throw new Error(response.error.message || response.data.error.message)
      }

      message.success('Opening stock added successfully')
      setItems([])
      refreshProducts()
      setConfirmModalVisible(false)
      setRefreshTrigger((prev: number) => prev + 1)
    } catch (error: any) {
      message.error(error.message || 'Failed to add opening stock')
    } finally {
      setLoading(false)
    }
  }, [items, user?.id, message, refreshProducts, setRefreshTrigger])

  // Filter items based on search text
  const filteredItems = useMemo(() => {
    if (!searchText) return items

    return items.filter((item) => {
      const product = products.find((p) => p.value === item.productId)
      return product?.label?.toLowerCase().includes(searchText.toLowerCase())
    })
  }, [items, products, searchText])

  // Calculate pagination data
  const paginatedItems = useMemo(() => {
    const startIndex = (currentPage - 1) * pageSize
    return filteredItems.slice(startIndex, startIndex + pageSize)
  }, [filteredItems, currentPage, pageSize])

  // Define columns with memoization
  const columns = useMemo(
    () => [
      {
        title: 'Sr. No.',
        width: 80,
        render: (_: any, __: any, index: number) => (currentPage - 1) * pageSize + index + 1
      },
      {
        title: 'Product',
        dataIndex: 'productId',
        render: (productId: string) => {
          const product = products.find((p) => p.value === productId)
          return <Text className="text-base">{product?.label || productId}</Text>
        }
      },
      {
        title: 'Quantity',
        dataIndex: 'quantity',
        width: 120,
        render: (qty: number) => <Text className="text-base">{qty.toLocaleString('en-US')}</Text>
      },
      {
        title: 'Purchase Price',
        dataIndex: 'purchasePrice',
        width: 150,
        render: (price: number) => (
          <Text className="text-base">
            {price.toLocaleString('en-US', { minimumFractionDigits: 2 })}
          </Text>
        )
      },
      {
        title: 'Total',
        width: 150,
        render: (record: OpeningStockItem) => (
          <Text strong className="text-base">
            {(record.quantity * record.purchasePrice).toLocaleString('en-US', {
              minimumFractionDigits: 2
            })}
          </Text>
        )
      },
      {
        title: 'Action',
        width: 100,
        render: (record: OpeningStockItem) => (
          <Button
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleRemoveItem(record.key as string)}
            className="hover:!border-red-600 hover:!text-red-600"
          />
        )
      }
    ],
    [products, handleRemoveItem, currentPage, pageSize]
  )

  return (
    <div className="space-y-6 p-6">
      <Card
        className={`shadow-sm ${
          isDarkMode
            ? 'bg-gradient-to-br from-indigo-950 via-black to-indigo-900 shadow-lg shadow-indigo-900'
            : 'bg-gradient-to-br from-indigo-200 via-white to-indigo-100 shadow-lg shadow-indigo-100'
        }`}
        style={{
          backgroundSize: '200% 200%',
          backgroundPosition: 'center'
        }}
      >
        <Form form={form} layout="vertical" className="mb-6">
          <Space wrap size="middle" align="end" className="items-center">
            <Form.Item
              className="w-96"
              name="productId"
              label={
                <Text strong className="text-base">
                  Product
                </Text>
              }
              rules={[{ required: true, message: 'Please select a product' }]}
            >
              <Select
                ref={selectRef}
                showSearch
                placeholder="Select a product"
                options={availableProducts}
                className="w-full"
                filterOption={(input, option) =>
                  (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                }
              />
            </Form.Item>

            <Form.Item
              name="quantity"
              label={
                <Text strong className="text-base">
                  Quantity
                </Text>
              }
              rules={[{ required: true, message: 'Please enter quantity' }]}
            >
              <InputNumber min={0} className="w-40" />
            </Form.Item>

            <Form.Item
              name="purchasePrice"
              label={
                <Text strong className="text-base">
                  Purchase Price
                </Text>
              }
              rules={[{ required: true, message: 'Please enter purchase price' }]}
            >
              <InputNumber min={0} className="w-40" />
            </Form.Item>

            <Button
              onClick={handleAddItem}
              className="mt-2 !border-blue-500 text-blue-500 hover:!border-blue-600 hover:!text-blue-600"
            >
              Add Item
            </Button>
          </Space>
        </Form>

        {items.length > 0 && (
          <div className="mb-4 flex items-center justify-between">
            <div>
              <Text className="mr-2 text-base">Total Items: {items.length}</Text>
              {filteredItems.length !== items.length && (
                <Text className="text-base text-gray-500">(Filtered: {filteredItems.length})</Text>
              )}
            </div>
            <Input
              placeholder="Search products..."
              prefix={<SearchOutlined />}
              onChange={(e) => setSearchText(e.target.value)}
              value={searchText}
              className="w-60"
              allowClear
            />
          </div>
        )}

        <Table
          size="small"
          columns={columns}
          dataSource={paginatedItems}
          rowKey="key"
          pagination={{
            current: currentPage,
            pageSize: pageSize,
            total: filteredItems.length,
            onChange: (page, pageSize) => {
              setCurrentPage(page)
              if (pageSize) setPageSize(pageSize)
            },
            showSizeChanger: true,
            pageSizeOptions: ['20', '50', '100', '200'],
            position: ['bottomCenter']
          }}
          summary={() => (
            <Table.Summary>
              <Table.Summary.Row>
                <Table.Summary.Cell index={0} colSpan={4}>
                  <Text strong className="text-lg">
                    Grand Total
                  </Text>
                </Table.Summary.Cell>
                <Table.Summary.Cell index={1}>
                  <Text strong className="text-lg">
                    {totalAmount.toLocaleString('en-US', { minimumFractionDigits: 2 })}
                  </Text>
                </Table.Summary.Cell>
                <Table.Summary.Cell index={2} />
              </Table.Summary.Row>
            </Table.Summary>
          )}
          className="overflow-hidden"
        />
      </Card>

      <div className="flex justify-end">
        <Button
          type="primary"
          onClick={() => setConfirmModalVisible(true)}
          disabled={items.length === 0}
          className="h-11 bg-green-500 px-8 text-base hover:!bg-green-600"
          size="large"
        >
          Submit Opening Stock
        </Button>
      </div>

      <Modal
        title={
          <Text strong className="text-lg">
            Confirm Opening Stock
          </Text>
        }
        open={confirmModalVisible}
        onOk={handleSubmit}
        onCancel={() => setConfirmModalVisible(false)}
        confirmLoading={loading}
        okButtonProps={{
          className: 'bg-green-500 hover:!bg-green-600'
        }}
      >
        <div className="space-y-4 py-4">
          <Text className="text-base">Are you sure you want to submit this opening stock?</Text>
          {items.length > 500 && (
            <div className="border-l-4 border-orange-400 bg-orange-50 p-4 dark:bg-orange-400/20">
              <Text className="text-base text-orange-700 dark:text-orange-400">
                You are submitting {items.length} items. This might take some time to process.
              </Text>
            </div>
          )}
          <div className="border-l-4 border-yellow-400 bg-yellow-50 p-4 dark:bg-yellow-400/20">
            <Text className="text-base text-yellow-700 dark:text-yellow-400">
              This action cannot be undone.
            </Text>
          </div>
          <div className="space-y-2">
            <Text className="text-base">Total Items: {items.length}</Text>
            <br />
            <Text strong className="text-lg">
              Total Amount: {totalAmount.toLocaleString('en-US', { minimumFractionDigits: 2 })}
            </Text>
          </div>
        </div>
      </Modal>
    </div>
  )
}
