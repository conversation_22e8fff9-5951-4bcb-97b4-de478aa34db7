import { CashLocation, Prisma, Status } from "@prisma/client";

export interface CreateExpenseData {
    category: string;
    amount: number;
    description?: string;
    date: Date;
    createdById: string;
    paymentSource: CashLocation;
    bankId?: string;
}

export interface GetExpenseParams {
    page: number;
    limit: number;
    search?: string;
    where?: Prisma.ExpenseWhereInput;
    startDate?: Date;
    endDate?: Date;
    status?: Status | string;
    category?: string;
}

export interface VoidExpenseParams {
    id: string;
    voidedById: string;
    reason: string;
}

export interface ExpenseItem {
    id: string;
    category: string;
    amount: number;
    description?: string;
    date: Date;
    status: Status;
    createdBy: {
        id: string;
        name: string;
    };
    voidedBy?: {
        id: string;
        name: string;
    };
    voidedAt?: Date;
    voidingReason?: string;
    Ledger?: {
        cashSource: CashLocation;
        bankId?: string;
        bank?: {
            name: string;
        };
    };
}

export interface ExpenseResponse {
    expenses: ExpenseItem[];
    total: number;
    page: number;
    totalPages: number;
    currentPageTotal: number;
    dateRangeTotal: number | null;
}