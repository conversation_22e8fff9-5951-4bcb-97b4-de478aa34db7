import { InitializeSmallCounterData, IRequest } from '../../common';
import { smallCounterService } from '../services';

class SmallCounterController {
    async initializeSmallCounter(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { amount, adminId } = req.body as InitializeSmallCounterData;
        if (!amount) throw new Error('Amount is required');
        if (!adminId) throw new Error('Admin ID is required');
        return await smallCounterService.initializeSmallCounter({ amount: Number(amount), adminId });
    }

    async getSmallCounterBalance(_event: Electron.IpcMainInvokeEvent) {
        return await smallCounterService.getCurrentBalance();
    }

    async transferToVault(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { amount, adminId } = req.body ?? {};
        if (!amount) throw new Error('Amount is required');
        if (!adminId) throw new Error('Admin ID is required');
        return await smallCounterService.transferToVault(Number(amount), adminId);
    }

    async reconcileSmallCounter(_event: Electron.IpcMainInvokeEvent) {
        return await smallCounterService.reconcileSmallCounter();
    }

    async getAllTransactions(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { page, pageSize, startDate, endDate, includeDeleted } = req.query ?? {};
        return await smallCounterService.getAllTransactions({
            page: page ? Number(page) : undefined,
            pageSize: pageSize ? Number(pageSize) : undefined,
            startDate: startDate ? new Date(startDate) : undefined,
            endDate: endDate ? new Date(endDate) : undefined,
            includeDeleted: includeDeleted === 'true'
        });
    }

    async generateSmallCounterStatement(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { startDate, endDate, page, pageSize } = req.query || {};

        if (!startDate || !endDate) {
            throw new Error('Start date and end date are required');
        }

        return await smallCounterService.generateSmallCounterStatement({
            startDate: new Date(startDate as string),
            endDate: new Date(endDate as string),
            page: page ? Number(page) : undefined,
            pageSize: pageSize ? Number(pageSize) : undefined
        });
    }
}

export const smallCounterController = new SmallCounterController();