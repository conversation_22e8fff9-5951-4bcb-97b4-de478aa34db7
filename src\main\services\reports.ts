import { prisma } from '../db';
import { Prisma, CustomerType, PaymentMethod, CashLocation } from '@prisma/client';
import {
    ReportFormat,
    TimePeriod,
    FinancialOverviewParams,
    FinancialOverviewReport,
    CashFlowParams,
    CashFlowReport,
    InventoryReportParams,
    InventoryValuationReport,
    SalesReportParams,
    SalesPerformanceReport,
    CustomerAnalyticsParams,
    CustomerAnalyticsReport,
    AgeingReportParams,
    AgeingReport,
    DailyOperationsParams,
    DailyOperationsReport,
    AuditReportParams,
    AuditReport
} from '@/common/types/reports';

class ReportsService {
    // Helper function for date range processing
    private getDateRangeForPeriod(startDate: Date, endDate: Date, period?: TimePeriod) {
        if (!period) return { start: startDate, end: endDate };

        const now = new Date();
        switch (period) {
            case TimePeriod.DAILY:
                return {
                    start: new Date(now.setHours(0, 0, 0, 0)),
                    end: new Date(now.setHours(23, 59, 59, 999))
                };
            case TimePeriod.WEEKLY:
                const weekStart = new Date(now.setDate(now.getDate() - now.getDay()));
                return {
                    start: new Date(weekStart.setHours(0, 0, 0, 0)),
                    end: now
                };
            case TimePeriod.MONTHLY:
                return {
                    start: new Date(now.getFullYear(), now.getMonth(), 1),
                    end: now
                };
            case TimePeriod.YEARLY:
                return {
                    start: new Date(now.getFullYear(), 0, 1),
                    end: now
                };
        }
    }

    // Helper function for calculating percentages
    private calculatePercentage(value: number, total: number): number {
        return total === 0 ? 0 : (value / total) * 100;
    }

    // Financial Reports
    private async getBankBalances() {
        return await prisma.banks.findMany({
            select: {
                name: true,
                balance: true,
                Ledger: {
                    where: {
                        status: 'ACTIVE'
                    }
                }
            }
        });
    }

    private async getCashPositions() {
        const [smallCounter, vault] = await Promise.all([
            prisma.smallCounter.findFirst(),
            prisma.cashVault.findFirst()
        ]);

        return {
            smallCounter: smallCounter?.cashInShop || 0,
            vault: vault?.balance || 0
        };
    }

    private async getPartyBalances() {
        return await prisma.party.findMany({
            where: {
                currentBalance: {
                    not: 0
                }
            },
            select: {
                name: true,
                type: true,
                currentBalance: true,
                ledgers: {
                    orderBy: {
                        date: 'desc'
                    },
                    take: 1,
                    select: {
                        date: true
                    }
                }
            }
        });
    }

    async generateFinancialOverview(params: FinancialOverviewParams): Promise<FinancialOverviewReport> {
        // Get data for current snapshot
        const [bankBalances, cashPositions, partyBalances] = await Promise.all([
            this.getBankBalances(),
            this.getCashPositions(),
            this.getPartyBalances()
        ]);

        const totalBankBalance = bankBalances.reduce((sum, bank) => sum + bank.balance, 0);
        const totalCashInHand = cashPositions.smallCounter + cashPositions.vault;

        const partySummary = partyBalances.reduce((acc, party) => {
            if (party.type === 'CUSTOMER') {
                acc.totalReceivables += party.currentBalance;
            } else if (party.type === 'VENDOR') {
                acc.totalPayables += party.currentBalance;
            }
            return acc;
        }, { totalReceivables: 0, totalPayables: 0 });

        const report: FinancialOverviewReport = {
            summary: {
                totalCashInHand,
                totalBankBalance,
                totalReceivables: partySummary.totalReceivables,
                totalPayables: partySummary.totalPayables,
                netPosition: totalCashInHand + totalBankBalance + partySummary.totalReceivables - partySummary.totalPayables
            },
            bankBalances: bankBalances.map(bank => ({
                bankName: bank.name,
                balance: bank.balance,
                transactions: bank.Ledger.length
            })),
            cashDistribution: [
                {
                    location: 'Small Counter',
                    amount: cashPositions.smallCounter,
                    percentage: this.calculatePercentage(cashPositions.smallCounter, totalCashInHand)
                },
                {
                    location: 'Vault',
                    amount: cashPositions.vault,
                    percentage: this.calculatePercentage(cashPositions.vault, totalCashInHand)
                }
            ],
            partyBalances: partyBalances.map(party => ({
                partyName: party.name,
                type: party.type,
                balance: party.currentBalance,
                lastTransactionDate: party.ledgers[0]?.date || new Date()
            }))
        };

        // Format specific transformations
        if (params.format === ReportFormat.PDF) {
            // Add any PDF specific formatting
            // For example, round numbers, format dates, etc.
        } else if (params.format === ReportFormat.EXCEL) {
            // Add any Excel specific formatting
            // For example, flatten nested objects, etc.
        }

        return report;
    }

    // Inventory Reports
    private async getProductStock() {
        return await prisma.stock.findMany({
            where: {
                status: 'IN_STOCK'
            },
            include: {
                product: {
                    include: {
                        category: true
                    }
                }
            }
        });
    }

    async generateInventoryValuation(params: InventoryReportParams): Promise<InventoryValuationReport> {
        const stock = await this.getProductStock();

        const categoryMap = new Map();
        let totalValue = 0;
        let lowStockItems = 0;

        stock.forEach(item => {
            const categoryId = item.product.categoryId;
            if (!categoryMap.has(categoryId)) {
                categoryMap.set(categoryId, {
                    category: item.product.category.name,
                    itemCount: 0,
                    totalValue: 0
                });
            }

            const value = item.quantity * item.purchasePrice;
            totalValue += value;
            categoryMap.get(categoryId).itemCount++;
            categoryMap.get(categoryId).totalValue += value;

            if (item.product.minStockLevel && item.quantity <= item.product.minStockLevel) {
                lowStockItems++;
            }
        });

        const report: InventoryValuationReport = {
            summary: {
                totalProducts: stock.length,
                totalValue,
                averageValue: totalValue / stock.length,
                lowStockItems
            },
            categoryDistribution: Array.from(categoryMap.values()).map(cat => ({
                ...cat,
                percentage: this.calculatePercentage(cat.totalValue, totalValue)
            })),
            stockDetails: stock.map(item => ({
                productId: item.product.productId,
                name: item.product.name,
                category: item.product.category.name,
                quantity: item.quantity,
                value: item.quantity * item.purchasePrice,
                lastPurchaseDate: item.createdAt,
                lastSaleDate: new Date() // This needs to be implemented properly
            })),
            ageingAnalysis: [] // This needs to be implemented
        };

        // Format specific transformations
        if (params.format === ReportFormat.PDF || params.format === ReportFormat.EXCEL) {
            // Add format specific transformations
        }

        return report;
    }

    // Cash Flow Report
    async generateCashFlow(params: CashFlowParams): Promise<CashFlowReport> {
        // Process date range based on time period or explicit dates
        const { start: startDate, end: endDate } = this.getDateRangeForPeriod(
            params.startDate || new Date(),
            params.endDate || new Date(),
            params.timePeriod
        );

        const dateRange = {
            gte: startDate,
            lte: endDate
        };

        // Get all transactions for the period
        const transactions = await prisma.ledger.findMany({
            where: {
                date: dateRange,
                status: 'ACTIVE'
            },
            include: {
                bank: {
                    select: { name: true }
                },
                party: {
                    select: { name: true, type: true }
                },
                expense: {
                    select: { category: true }
                }
            },
            orderBy: { date: 'asc' }
        });

        // Calculate daily cash flows
        const dailyFlows = new Map<string, {
            date: Date;
            inflow: number;
            outflow: number;
            categories: Map<string, { inflow: number; outflow: number }>;
        }>();

        // Process transactions
        transactions.forEach(trans => {
            const dateKey = trans.date.toISOString().split('T')[0];
            if (!dailyFlows.has(dateKey)) {
                dailyFlows.set(dateKey, {
                    date: trans.date,
                    inflow: 0,
                    outflow: 0,
                    categories: new Map()
                });
            }

            const flow = dailyFlows.get(dateKey)!;

            // Get display category for reporting
            let displayCategory: string;
            if (trans.referenceType === 'Payment' && trans.party) {
                displayCategory = trans.party.type === 'CUSTOMER' ? 'Customer Payments' : 'Vendor Payments';
            } else if (trans.referenceType === 'Expense' && trans.expense) {
                displayCategory = `Expense - ${trans.expense.category}`;
            } else {
                displayCategory = trans.referenceType.replace(/([A-Z])/g, ' $1').trim();
            }

            if (!flow.categories.has(displayCategory)) {
                flow.categories.set(displayCategory, { inflow: 0, outflow: 0 });
            }
            const catFlow = flow.categories.get(displayCategory)!;

            if (trans.creditOrDebit === 'CREDIT') {
                flow.inflow += trans.amount;
                catFlow.inflow += trans.amount;
            } else {
                flow.outflow += trans.amount;
                catFlow.outflow += trans.amount;
            }
        });

        // Calculate totals and prepare report
        let totalInflow = 0;
        let totalOutflow = 0;
        const categoryTotals = new Map<string, { inflow: number; outflow: number }>();

        const dailyFlowsArray = Array.from(dailyFlows.values()).sort((a, b) =>
            a.date.getTime() - b.date.getTime()
        );

        dailyFlowsArray.forEach(flow => {
            totalInflow += flow.inflow;
            totalOutflow += flow.outflow;

            flow.categories.forEach((value, category) => {
                if (!categoryTotals.has(category)) {
                    categoryTotals.set(category, { inflow: 0, outflow: 0 });
                }
                const catTotal = categoryTotals.get(category)!;
                catTotal.inflow += value.inflow;
                catTotal.outflow += value.outflow;
            });
        });

        const report = {
            timeline: dailyFlowsArray.map(flow => ({
                date: flow.date,
                inflow: flow.inflow,
                outflow: flow.outflow,
                netFlow: flow.inflow - flow.outflow
            })),
            inflowSources: Array.from(categoryTotals.entries())
                .filter(([_, values]) => values.inflow > 0)
                .map(([category, values]) => ({
                    source: category,
                    amount: values.inflow,
                    percentage: this.calculatePercentage(values.inflow, totalInflow)
                })),
            outflowCategories: Array.from(categoryTotals.entries())
                .filter(([_, values]) => values.outflow > 0)
                .map(([category, values]) => ({
                    category,
                    amount: values.outflow,
                    percentage: this.calculatePercentage(values.outflow, totalOutflow)
                })),
            summary: {
                totalInflow,
                totalOutflow,
                netCashFlow: totalInflow - totalOutflow,
                periodStart: startDate,
                periodEnd: endDate
            }
        } as CashFlowReport;

        return report;
    }

    // Sales Performance Report
    async generateSalesPerformance(params: SalesReportParams): Promise<SalesPerformanceReport> {
        // Process date range based on time period or explicit dates
        const { start: startDate, end: endDate } = this.getDateRangeForPeriod(
            params.startDate || new Date(),
            params.endDate || new Date(),
            params.timePeriod
        );

        const dateRange = {
            gte: startDate,
            lte: endDate
        };

        // Get all sales transactions for the period
        const [regularSales, walkInSales] = await Promise.all([
            prisma.saleInvoice.findMany({
                where: {
                    date: dateRange,
                    status: 'ACTIVE'
                },
                include: {
                    customer: {
                        select: {
                            name: true,
                            type: true
                        }
                    },
                    items: {
                        include: {
                            product: {
                                select: {
                                    id: true,
                                    name: true,
                                    category: {
                                        select: {
                                            name: true
                                        }
                                    }
                                }
                            }
                        }
                    },
                    payment: {
                        select: {
                            paymentMethod: true
                        }
                    }
                }
            }),
            prisma.walkInSaleInvoice.findMany({
                where: {
                    date: dateRange,
                    status: 'ACTIVE'
                },
                include: {
                    items: {
                        include: {
                            product: {
                                select: {
                                    id: true,
                                    name: true,
                                    category: {
                                        select: {
                                            name: true
                                        }
                                    }
                                }
                            }
                        }
                    },
                    payment: {
                        select: {
                            paymentMethod: true
                        }
                    }
                }
            })
        ]);

        // Process sales data
        const salesByDate = new Map<string, {
            amount: number;
            orders: number;
        }>();

        const salesByCustomerType = new Map<CustomerType, {
            amount: number;
            count: number;
        }>();

        const salesByPaymentMethod = new Map<PaymentMethod, {
            amount: number;
            count: number;
        }>();

        const productSales = new Map<string, {
            productId: string;
            name: string;
            quantity: number;
            amount: number;
            profit: number;
        }>();

        // Process regular sales
        regularSales.forEach(sale => {
            // Process date
            const dateKey = sale.date.toISOString().split('T')[0];
            if (!salesByDate.has(dateKey)) {
                salesByDate.set(dateKey, { amount: 0, orders: 0 });
            }
            const dateStats = salesByDate.get(dateKey)!;
            dateStats.amount += sale.totalAmount;
            dateStats.orders += 1;

            // Process customer type
            const customerType = sale.customer.type === 'CUSTOMER' ? 'REGISTERED' : 'WALK_IN';
            if (!salesByCustomerType.has(customerType)) {
                salesByCustomerType.set(customerType, { amount: 0, count: 0 });
            }
            const typeStats = salesByCustomerType.get(customerType)!;
            typeStats.amount += sale.totalAmount;
            typeStats.count += 1;

            // Process payment method
            if (sale.payment) {
                const paymentMethod = sale.payment.paymentMethod;
                if (!salesByPaymentMethod.has(paymentMethod)) {
                    salesByPaymentMethod.set(paymentMethod, { amount: 0, count: 0 });
                }
                const methodStats = salesByPaymentMethod.get(paymentMethod)!;
                methodStats.amount += sale.totalAmount;
                methodStats.count += 1;
            }

            // Process products
            sale.items.forEach(item => {
                const productId = item.product.id;
                if (!productSales.has(productId)) {
                    productSales.set(productId, {
                        productId,
                        name: item.product.name,
                        quantity: 0,
                        amount: 0,
                        profit: 0
                    });
                }
                const productStats = productSales.get(productId)!;
                productStats.quantity += item.totalQuantity;
                productStats.amount += item.total;
                productStats.profit += item.totalProfit;
            });
        });

        // Process walk-in sales (similar to regular sales)
        walkInSales.forEach(sale => {
            const dateKey = sale.date.toISOString().split('T')[0];
            if (!salesByDate.has(dateKey)) {
                salesByDate.set(dateKey, { amount: 0, orders: 0 });
            }
            const dateStats = salesByDate.get(dateKey)!;
            dateStats.amount += sale.totalAmount;
            dateStats.orders += 1;

            // Walk-in sales are always WALK_IN type
            if (!salesByCustomerType.has('WALK_IN')) {
                salesByCustomerType.set('WALK_IN', { amount: 0, count: 0 });
            }
            const typeStats = salesByCustomerType.get('WALK_IN')!;
            typeStats.amount += sale.totalAmount;
            typeStats.count += 1;

            if (sale.payment) {
                const paymentMethod = sale.payment.paymentMethod;
                if (!salesByPaymentMethod.has(paymentMethod)) {
                    salesByPaymentMethod.set(paymentMethod, { amount: 0, count: 0 });
                }
                const methodStats = salesByPaymentMethod.get(paymentMethod)!;
                methodStats.amount += sale.totalAmount;
                methodStats.count += 1;
            }

            sale.items.forEach(item => {
                const productId = item.product.id;
                if (!productSales.has(productId)) {
                    productSales.set(productId, {
                        productId,
                        name: item.product.name,
                        quantity: 0,
                        amount: 0,
                        profit: 0
                    });
                }
                const productStats = productSales.get(productId)!;
                productStats.quantity += item.totalQuantity;
                productStats.amount += item.total;
                productStats.profit += item.totalProfit;
            });
        });

        // Calculate totals and prepare report
        const totalSales = Array.from(salesByDate.values()).reduce((sum, day) => sum + day.amount, 0);
        const totalOrders = Array.from(salesByDate.values()).reduce((sum, day) => sum + day.orders, 0);

        const report: SalesPerformanceReport = {
            summary: {
                totalSales,
                totalOrders,
                averageOrderValue: totalOrders > 0 ? totalSales / totalOrders : 0,
                returnRate: 0 // We'll need to implement returns tracking to calculate this
            },
            salesTrend: Array.from(salesByDate.entries())
                .sort(([a], [b]) => a.localeCompare(b))
                .map(([period, stats]) => ({
                    period,
                    amount: stats.amount,
                    orders: stats.orders
                })),
            salesByCustomerType: Array.from(salesByCustomerType.entries()).map(([type, stats]) => ({
                type,
                amount: stats.amount,
                percentage: this.calculatePercentage(stats.amount, totalSales)
            })),
            topProducts: Array.from(productSales.values())
                .sort((a, b) => b.amount - a.amount)
                .slice(0, 10),
            paymentMethodDistribution: Array.from(salesByPaymentMethod.entries()).map(([method, stats]) => ({
                method,
                amount: stats.amount,
                percentage: this.calculatePercentage(stats.amount, totalSales)
            }))
        };

        return report;
    }

    // Customer Analytics Report
    async generateCustomerAnalytics(params: CustomerAnalyticsParams): Promise<CustomerAnalyticsReport> {
        // Process date range based on time period or explicit dates
        const { start: startDate, end: endDate } = this.getDateRangeForPeriod(
            params.startDate || new Date(),
            params.endDate || new Date(),
            params.timePeriod
        );

        const { minPurchases = 1, customerType } = params;
        const dateRange = {
            gte: startDate,
            lte: endDate
        };

        // Get all customers with their sales data
        const customers = await prisma.party.findMany({
            where: {
                type: 'CUSTOMER',
                ...(customerType && { customerType }),
                SaleInvoice: {
                    some: {
                        date: dateRange,
                        status: 'ACTIVE'
                    }
                }
            },
            include: {
                SaleInvoice: {
                    where: {
                        date: dateRange,
                        status: 'ACTIVE'
                    },
                    select: {
                        date: true,
                        totalAmount: true
                    }
                },
                _count: {
                    select: {
                        SaleInvoice: {
                            where: {
                                date: dateRange,
                                status: 'ACTIVE'
                            }
                        }
                    }
                }
            }
        });

        // Process customer data
        const customerStats = customers
            .filter(customer => (customer._count?.SaleInvoice || 0) >= minPurchases)
            .map(customer => {
                const totalSpent = customer.SaleInvoice.reduce((sum, sale) => sum + (sale.totalAmount || 0), 0);
                const lastPurchase = customer.SaleInvoice.reduce(
                    (latest, sale) => (sale.date > latest ? sale.date : latest),
                    new Date(0)
                );

                return {
                    id: customer.id,
                    name: customer.name,
                    purchases: customer._count?.SaleInvoice || 0,
                    totalSpent,
                    lastPurchase,
                    currentBalance: customer.currentBalance
                };
            })
            .sort((a, b) => b.totalSpent - a.totalSpent);

        // Calculate purchase frequency distribution
        const purchaseFrequency = new Map<string, number>();
        customerStats.forEach(customer => {
            const frequency = this.getPurchaseFrequencyBucket(customer.purchases);
            purchaseFrequency.set(
                frequency,
                (purchaseFrequency.get(frequency) || 0) + 1
            );
        });

        // Calculate customer segmentation based on spending
        const segmentation = this.calculateCustomerSegmentation(customerStats);

        // Calculate summary metrics
        const totalCustomers = customerStats.length;
        const activeCustomers = customerStats.filter(
            customer => customer.lastPurchase >= new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
        ).length;
        const totalSpent = customerStats.reduce((sum, customer) => sum + customer.totalSpent, 0);

        const report: CustomerAnalyticsReport = {
            summary: {
                totalCustomers,
                activeCustomers,
                averageCustomerValue: totalCustomers > 0 ? totalSpent / totalCustomers : 0
            },
            topCustomers: customerStats.slice(0, 10),
            purchaseFrequency: Array.from(purchaseFrequency.entries()).map(([frequency, count]) => ({
                frequency,
                customerCount: count
            })),
            customerSegmentation: Array.from(segmentation.entries()).map(([segment, { count, total }]) => ({
                segment,
                count,
                percentage: this.calculatePercentage(count, totalCustomers)
            }))
        };

        return report;
    }

    private getPurchaseFrequencyBucket(purchases: number): string {
        if (purchases <= 1) return 'One-time';
        if (purchases <= 3) return 'Occasional';
        if (purchases <= 6) return 'Regular';
        if (purchases <= 12) return 'Frequent';
        return 'VIP';
    }

    private calculateCustomerSegmentation(customers: Array<{
        totalSpent: number;
    }>): Map<string, { count: number; total: number }> {
        const segmentation = new Map<string, { count: number; total: number }>();
        const sortedSpending = customers.map(c => c.totalSpent).sort((a, b) => b - a);
        const totalSpending = sortedSpending.reduce((sum, spent) => sum + spent, 0);

        let cumulativeSpending = 0;
        let processedCustomers = 0;

        // A/B/C segmentation based on spending
        for (const spent of sortedSpending) {
            cumulativeSpending += spent;
            processedCustomers++;

            let segment: string;
            const spendingPercentage = (cumulativeSpending / totalSpending) * 100;

            if (spendingPercentage <= 70) {
                segment = 'A - Top Spenders';
            } else if (spendingPercentage <= 90) {
                segment = 'B - Regular Customers';
            } else {
                segment = 'C - Occasional Buyers';
            }

            if (!segmentation.has(segment)) {
                segmentation.set(segment, { count: 0, total: 0 });
            }
            const stats = segmentation.get(segment)!;
            stats.count++;
            stats.total += spent;
        }

        return segmentation;
    }

    async generateAgeingReport(params: AgeingReportParams): Promise<AgeingReport> {
        // Process date range based on time period or explicit dates
        const { start: startDate, end: endDate } = this.getDateRangeForPeriod(
            params.startDate || new Date(),
            params.endDate || new Date(),
            params.timePeriod
        );

        const { partyType, minAmount = 0 } = params;

        // Get all parties with outstanding balances and their payment history within date range
        const parties = await prisma.party.findMany({
            where: {
                type: partyType,
                currentBalance: {
                    not: 0,
                    ...(minAmount > 0 && { gte: minAmount })
                }
            },
            include: {
                Payments: {
                    where: {
                        status: 'ACTIVE',
                        date: {
                            gte: startDate,
                            lte: endDate
                        }
                    },
                    orderBy: {
                        date: 'desc'
                    }
                }
            }
        });

        const now = new Date();
        let totalOutstanding = 0;
        let totalOverdueDays = 0;
        const details: AgeingReport['details'] = [];

        for (const party of parties) {
            const lastPayment = party.Payments[0];
            const lastPaymentDate = lastPayment?.date || startDate; // Use start date if no payments in range
            const overdueDays = Math.floor((now.getTime() - lastPaymentDate.getTime()) / (1000 * 60 * 60 * 24));
            totalOverdueDays += overdueDays;
            totalOutstanding += Math.abs(party.currentBalance);

            // Calculate buckets
            const balance = Math.abs(party.currentBalance);
            const buckets = {
                current: 0,
                '1-30': 0,
                '31-60': 0,
                '61-90': 0,
                '>90': 0
            };

            if (overdueDays <= 0) buckets.current = balance;
            else if (overdueDays <= 30) buckets['1-30'] = balance;
            else if (overdueDays <= 60) buckets['31-60'] = balance;
            else if (overdueDays <= 90) buckets['61-90'] = balance;
            else buckets['>90'] = balance;

            details.push({
                partyId: party.id,
                name: party.name,
                total: balance,
                buckets,
                lastPaymentDate
            });
        }

        // Calculate ageing buckets for chart
        const ageingBuckets = Object.entries(
            details.reduce((acc, detail) => {
                Object.entries(detail.buckets).forEach(([bucket, amount]) => {
                    acc[bucket] = (acc[bucket] || 0) + amount;
                });
                return acc;
            }, {} as Record<string, number>)
        ).map(([bucket, amount]) => ({
            bucket,
            amount,
            percentage: this.calculatePercentage(amount, totalOutstanding)
        }));

        return {
            summary: {
                totalOutstanding,
                averageOverdueDays: parties.length ? totalOverdueDays / parties.length : 0,
                totalParties: parties.length
            },
            details,
            ageingBuckets
        };
    }

    async generateDailyOperations(params: DailyOperationsParams): Promise<DailyOperationsReport> {
        // Process date range based on time period or explicit dates
        const { start: startDate, end: endDate } = this.getDateRangeForPeriod(
            params.startDate || new Date(),
            params.endDate || new Date(),
            params.timePeriod
        );

        const startOfDay = new Date(startDate.setHours(0, 0, 0, 0));
        const endOfDay = new Date(endDate.setHours(23, 59, 59, 999));

        // Get all transactions for the day
        const [sales, purchases, expenses, ledgerEntries] = await Promise.all([
            prisma.saleInvoice.findMany({
                where: {
                    date: { gte: startOfDay, lte: endOfDay },
                    status: 'ACTIVE'
                },
                include: {
                    createdBy: true
                }
            }),
            prisma.purchaseInvoice.findMany({
                where: {
                    date: { gte: startOfDay, lte: endOfDay },
                    status: 'ACTIVE'
                },
                include: {
                    createdBy: true
                }
            }),
            prisma.expense.findMany({
                where: {
                    date: { gte: startOfDay, lte: endOfDay },
                    status: 'ACTIVE'
                },
                include: {
                    createdBy: true,
                    Ledger: true
                }
            }),
            prisma.ledger.findMany({
                where: {
                    date: { gte: startOfDay, lte: endOfDay },
                    status: 'ACTIVE'
                },
                include: {
                    createdBy: true
                }
            })
        ]);

        // Calculate totals
        const totalSales = sales.reduce((sum, sale) => sum + sale.totalAmount, 0);
        const totalPurchases = purchases.reduce((sum, purchase) => sum + purchase.totalAmount, 0);
        const totalExpenses = expenses.reduce((sum, expense) => sum + expense.amount, 0);

        // Combine all transactions chronologically
        const transactions = [
            ...sales.map(sale => ({
                time: sale.date,
                type: 'Sale',
                description: `Sale Invoice #${sale.invoiceNumber}`,
                amount: sale.totalAmount,
                status: sale.status
            })),
            ...purchases.map(purchase => ({
                time: purchase.date,
                type: 'Purchase',
                description: `Purchase Invoice #${purchase.invoiceNumber}`,
                amount: purchase.totalAmount,
                status: purchase.status
            })),
            ...expenses.map(expense => ({
                time: expense.date,
                type: 'Expense',
                description: `${expense.category}: ${expense.description || ''}`,
                amount: expense.amount,
                status: expense.status
            }))
        ].sort((a, b) => a.time.getTime() - b.time.getTime());

        // Calculate cash movements by location
        const cashMovements = await Promise.all([
            this.getCashLocationMovements(CashLocation.SMALL_COUNTER, startOfDay, endOfDay),
            this.getCashLocationMovements(CashLocation.CASH_VAULT, startOfDay, endOfDay),
            this.getCashLocationMovements(CashLocation.BANK, startOfDay, endOfDay)
        ]);

        return {
            date: startDate,
            summary: {
                totalSales,
                totalPurchases,
                totalExpenses,
                netCashFlow: totalSales - totalPurchases - totalExpenses
            },
            transactions,
            cashMovements
        };
    }

    private async getCashLocationMovements(location: CashLocation, startDate: Date, endDate: Date) {
        const entries = await prisma.ledger.findMany({
            where: {
                date: { gte: startDate, lte: endDate },
                status: 'ACTIVE',
                cashSource: location
            }
        });

        const inflow = entries
            .filter(entry => entry.creditOrDebit === 'CREDIT')
            .reduce((sum, entry) => sum + entry.amount, 0);

        const outflow = entries
            .filter(entry => entry.creditOrDebit === 'DEBIT')
            .reduce((sum, entry) => sum + entry.amount, 0);

        return {
            location,
            inflow,
            outflow,
            netFlow: inflow - outflow
        };
    }

    async generateAuditReport(params: AuditReportParams): Promise<AuditReport> {
        // Process date range based on time period or explicit dates
        const { start: startDate, end: endDate } = this.getDateRangeForPeriod(
            params.startDate || new Date(),
            params.endDate || new Date(),
            params.timePeriod
        );

        const { userId, transactionType } = params;
        const dateRange = {
            date: {
                gte: startDate,
                lte: endDate
            }
        };

        // Get all relevant transactions
        const ledgerEntries = await prisma.ledger.findMany({
            where: {
                ...dateRange,
                ...(userId && { createdById: userId }),
                ...(transactionType && { referenceType: transactionType })
            },
            include: {
                createdBy: true,
                voidedBy: true
            },
            orderBy: {
                date: 'desc'
            }
        });

        // Transform ledger entries into audit transactions
        const transactions = ledgerEntries.map(entry => ({
            date: entry.date,
            user: entry.createdBy.name,
            action: entry.referenceType,
            details: entry.description,
            originalValue: entry.amount.toString(),
            newValue: entry.voidedBy ? '0' : undefined,
            status: entry.status
        }));

        // Calculate action distribution
        const actionCounts = transactions.reduce((acc, trans) => {
            acc[trans.action] = (acc[trans.action] || 0) + 1;
            return acc;
        }, {} as Record<string, number>);

        const totalTransactions = transactions.length;
        const actionDistribution = Object.entries(actionCounts).map(([action, count]) => ({
            action,
            count,
            percentage: this.calculatePercentage(count, totalTransactions)
        }));

        // Calculate summary metrics
        const voidTransactions = transactions.filter(t => t.status === 'VOID').length;
        const modifications = transactions.filter(t => t.newValue !== undefined).length;
        const unusualActivity = transactions.filter(t => {
            const amount = parseFloat(t.originalValue);
            return amount > 100000; // Consider transactions over 100,000 as unusual
        }).length;

        return {
            transactions,
            actionDistribution,
            summary: {
                totalTransactions,
                voidTransactions,
                modifications,
                unusualActivity
            }
        };
    }
}

export const reportsService = new ReportsService();