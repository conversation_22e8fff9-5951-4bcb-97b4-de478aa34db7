import { IRequest } from '../../common';
import { expenseService } from '../services';
import { CreateExpenseData, GetExpenseParams } from '../../common/types/expense';

class ExpenseController {
    async createExpense(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { category, amount, description, date, createdById, paymentSource, bankId } = req.body as CreateExpenseData;

        if (!category || !amount || !createdById || !paymentSource) {
            throw new Error("Missing required fields for expense creation");
        }

        return await expenseService.createExpense({
            category,
            amount,
            description,
            date: date ? new Date(date) : new Date(),
            createdById,
            paymentSource,
            bankId
        });
    }

    async deleteExpense(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { id, voidedById, reason } = req.params ?? {};
        if (!id || !voidedById || !reason) throw new Error("Expense ID, voided by ID and reason are required");

        return await expenseService.voidExpense(id, voidedById, reason);
    }

    async getExpense(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { id } = req.params ?? {};
        if (!id) throw new Error("Expense ID is required");

        return await expenseService.getExpenseById(id);
    }

    async getExpenses(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { page = 1, limit = 10, search, startDate, endDate, status, category } = req.query as GetExpenseParams;
        return await expenseService.getExpenses({ page, limit, search, startDate, endDate, status, category });
    }

    // async getExpensesByDay(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
    //     const { date, page = 1, limit = 10 } = req.query ?? {};
    //     if (!date) throw new Error("Date is required");

    //     return await expenseService.getExpensesByDay(new Date(date), { page, limit });
    // }

    // async getExpensesByDateRange(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
    //     const { startDate, endDate, page = 1, limit = 10 } = req.query ?? {};
    //     if (!startDate || !endDate) throw new Error("Start and end dates are required");

    //     return await expenseService.getExpensesByDateRange(
    //         new Date(startDate),
    //         new Date(endDate),
    //         { page, limit }
    //     );
    // }
}

export const expenseController = new ExpenseController();