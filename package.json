{"name": "sj-lace-app", "version": "1.2.2", "description": "A Point of sale built by <PERSON><PERSON><PERSON><PERSON>.", "main": "./out/main/index.js", "author": "<PERSON><PERSON><PERSON><PERSON>", "scripts": {"format": "prettier --write .", "lint": "eslint . --ext .js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix", "typecheck:node": "tsc --noEmit -p tsconfig.node.json --composite false", "typecheck:web": "tsc --noEmit -p tsconfig.web.json --composite false", "typecheck": "npm run typecheck:node && npm run typecheck:web", "start": "electron-vite preview", "dev": "electron-vite dev --watch", "build": "prisma generate && npm run typecheck && electron-vite build", "postinstall": "electron-builder install-app-deps && prisma generate", "build:unpack": "npm run build && electron-builder --dir", "build:win": "npm run build && electron-builder --win", "build:mac": "electron-vite build && electron-builder --mac", "build:linux": "electron-vite build && electron-builder --linux", "prisma:generate": "prisma generate", "prisma:studio": "prisma studio", "prisma:migrate": "prisma migrate deploy", "db:push": "prisma db push"}, "dependencies": {"@electron-toolkit/preload": "^3.0.1", "@electron-toolkit/utils": "^3.0.0", "@prisma/client": "^5.22.0", "@types/recharts": "^1.8.29", "bcryptjs": "^2.4.3", "date-fns": "^4.1.0", "jspdf": "^2.5.2", "jspdf-autotable": "^3.8.4", "recharts": "^2.15.1"}, "devDependencies": {"@electron-toolkit/eslint-config-prettier": "^2.0.0", "@electron-toolkit/eslint-config-ts": "^2.0.0", "@electron-toolkit/tsconfig": "^1.0.1", "@reduxjs/toolkit": "^2.2.6", "@types/node": "^20.14.8", "@types/plist": "^3.0.5", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@types/verror": "^1.10.10", "@vitejs/plugin-react": "^4.3.1", "antd": "^5.19.3", "autoprefixer": "^10.4.19", "electron": "^31.0.2", "electron-builder": "^24.13.3", "electron-vite": "^2.3.0", "eslint": "^8.57.0", "eslint-plugin-react": "^7.34.3", "postcss": "^8.4.39", "prettier": "^3.4.2", "prettier-plugin-tailwindcss": "^0.6.9", "prisma": "^5.22.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-icons": "^5.2.1", "react-redux": "^9.1.2", "react-router": "^6.25.1", "react-router-dom": "^6.25.1", "redux-persist": "^6.0.0", "sass": "^1.77.8", "sequelize-cli": "^6.6.2", "tailwindcss": "^3.4.6", "typescript": "^5.5.2", "vite": "^5.3.1"}}