import { publicDecrypt } from 'crypto';
import { ILicense, ILicenseVerification } from '@/common/types';
import { getMachineId } from '../utils/machineId';
import { app } from 'electron';
import path from 'path';
import fs from 'fs';

/**
 * Name of the file where the license key will be stored locally
 */
const LICENSE_FILE = 'sj-lace-license.key';

/**
 * Service responsible for handling all license-related operations
 * This includes license verification, validation, and storage management
 */
class LicenseService {
    private publicKey: string;
    private licenseFilePath: string;

    /**
     * Initializes the license service
     * @throws Error if LICENSE_PUBLIC_KEY environment variable is not set
     */
    constructor() {
        const envPublicKey = process.env.LICENSE_PUBLIC_KEY;
        if (!envPublicKey) {
            throw new Error('LICENSE_PUBLIC_KEY environment variable is not set. License verification cannot proceed.');
        }
        this.publicKey = envPublicKey;
        // Store license file in the app's user data directory for persistence
        this.licenseFilePath = path.join(app.getPath('userData'), LICENSE_FILE);
    }

    /**
     * Validates the structure and data types of a license object
     * @param license - The license object to validate
     * @returns boolean indicating if the license format is valid
     */
    private validateLicenseFormat(license: any): license is ILicense {
        return (
            typeof license === 'object' &&
            typeof license.key === 'string' &&
            typeof license.machineId === 'string' &&
            typeof license.expiryDate === 'string' &&
            !isNaN(new Date(license.expiryDate).getTime())
        );
    }

    /**
     * Decrypts and validates an encrypted license string
     * @param encryptedLicense - Base64 encoded encrypted license string
     * @returns Decrypted license object or null if decryption/validation fails
     */
    private decryptLicense(encryptedLicense: string): ILicense | null {
        try {
            // Basic input validation
            if (!encryptedLicense || typeof encryptedLicense !== 'string') {
                console.error('Invalid license format: License must be a string');
                return null;
            }

            // Convert base64 string to buffer for decryption
            const buffer = Buffer.from(encryptedLicense, 'base64');
            const decrypted = publicDecrypt(
                {
                    key: this.publicKey,
                    padding: 1 // RSA_PKCS1_PADDING
                },
                new Uint8Array(buffer)
            );

            // Parse and validate the decrypted license
            const parsedLicense = JSON.parse(decrypted.toString());
            if (!this.validateLicenseFormat(parsedLicense)) {
                console.error('Invalid license format: Missing required fields or invalid data types');
                return null;
            }

            return parsedLicense;
        } catch (error) {
            console.error('Failed to decrypt license:', error);
            return null;
        }
    }

    /**
     * Checks if a license is approaching its expiration date
     * @param expiryDate - The license expiration date
     * @param warningDays - Number of days before expiry to start warning (default: 30)
     * @returns boolean indicating if the license is expiring soon
     */
    private isLicenseExpiringSoon(expiryDate: Date, warningDays: number = 30): boolean {
        const now = new Date();
        const daysUntilExpiry = Math.ceil((expiryDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
        return daysUntilExpiry <= warningDays && daysUntilExpiry > 0;
    }

    /**
     * Gets the current system time
     * @returns Current date/time
     * @note In production, this should be replaced with a trusted time server check
     */
    private getSystemTime(): Date {
        // TODO: Implement NTP server validation in production
        return new Date();
    }

    /**
     * Verifies a license key and returns the verification result
     * @param encryptedLicense - Optional encrypted license string. If not provided, reads from stored file
     * @returns License verification result including validity status and any warnings/errors
     */
    public verifyLicense(encryptedLicense?: string): ILicenseVerification {

        try {
            // If no license provided, attempt to read from storage
            if (!encryptedLicense) {
                if (!fs.existsSync(this.licenseFilePath)) {
                    return { isValid: false, error: 'No license found' };
                }
                encryptedLicense = fs.readFileSync(this.licenseFilePath, 'utf8');
            }

            // Decrypt and validate the license
            const license = this.decryptLicense(encryptedLicense);
            if (!license) {
                return { isValid: false, error: 'Invalid license format' };
            }

            // Verify machine ID matches
            const currentMachineId = getMachineId();
            if (license.machineId !== currentMachineId) {
                return { isValid: false, error: 'License not valid for this machine' };
            }

            // Check expiration
            const now = this.getSystemTime();
            const expiryDate = new Date(license.expiryDate);

            if (expiryDate < now) {
                return { isValid: false, error: 'License has expired', expiryDate: expiryDate.toLocaleDateString() };
            }

            // Check for approaching expiration
            if (this.isLicenseExpiringSoon(expiryDate)) {
                const daysRemaining = Math.ceil((expiryDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
                return {
                    isValid: true,
                    warning: `License will expire in ${daysRemaining} days`,
                    expiryDate: expiryDate.toLocaleDateString(),
                    license: {
                        ...license,
                        isValid: true,
                        expiresIn: daysRemaining
                    }
                };
            }

            // Store valid license locally
            fs.writeFileSync(this.licenseFilePath, encryptedLicense);

            return {
                isValid: true,
                license: {
                    ...license,
                    isValid: true
                }
            };
        } catch (error) {
            console.error('License verification failed:', error);
            return { isValid: false, error: 'License verification failed' };
        }
    }

    /**
     * Retrieves and verifies the stored license
     * @returns License verification result
     */
    public getLicense(): ILicenseVerification {
        if (!fs.existsSync(this.licenseFilePath)) {
            return { isValid: false, error: 'No license found' };
        }

        const encryptedLicense = fs.readFileSync(this.licenseFilePath, 'utf8');

        return this.verifyLicense(encryptedLicense);
    }
}

export const licenseService = new LicenseService();