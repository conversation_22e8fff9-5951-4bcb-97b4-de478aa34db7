import { InvoiceNumberType } from "@/common/types";
import { invoiceNumberService } from "../services";
import { IRequest } from "@/common/types";


class InvoiceNumberController {
    async generateInvoiceNumber(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { type, adminId } = req.body as { type: InvoiceNumberType, adminId: string };
        return await invoiceNumberService.generateInvoiceNumber(type, adminId);
    }


    async confirmInvoiceNumber(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { invoiceNumber } = req.body as { invoiceNumber: string };
        return await invoiceNumberService.confirmInvoiceNumber(invoiceNumber);
    }

    async cancelInvoiceNumber(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { invoiceNumber } = req.body as { invoiceNumber: string };
        return await invoiceNumberService.cancelInvoiceNumber(invoiceNumber);
    }

    async cleanupExpiredNumbers(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        return await invoiceNumberService.cleanupExpiredNumbers();
    }

}

export const invoiceNumberController = new InvoiceNumberController();
