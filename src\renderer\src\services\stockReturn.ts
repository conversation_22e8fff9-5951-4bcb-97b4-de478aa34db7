import { Channels } from "@/common/constants";
import { http } from './http'
import {
    ProcessStockReturnParams,
    ProcessLegacyStockReturnParams,
    VoidReturnStockParams,
    GetStockReturnParams,
    GetSaleInvoiceByNumberParams,
    StockReturnType,
    StockReturnStatus,
    StockReturnSortOrder
} from '@/common/types/stockReturn'

export const processStockReturn = async (data: ProcessStockReturnParams) => {
    return await http.post(Channels.PROCESS_RETURN_STOCK, { body: data })
}

export const processLegacyStockReturn = async (data: ProcessLegacyStockReturnParams) => {
    return await http.post(Channels.PROCESS_LEGACY_RETURN_STOCK, { body: data })
}

export const voidReturnStock = async (data: VoidReturnStockParams) => {
    return await http.post(Channels.VOID_RETURN_STOCK, { body: data })
}

export const getStockReturn = async (data: GetStockReturnParams) => {
    return await http.get(Channels.GET_RETURNED_STOCK, { body: data })
}

export const getSaleInvoiceByNumber = async (data: GetSaleInvoiceByNumberParams) => {
    return await http.get(Channels.GET_SALE_INVOICE_BY_NUMBER, { body: data })
}




