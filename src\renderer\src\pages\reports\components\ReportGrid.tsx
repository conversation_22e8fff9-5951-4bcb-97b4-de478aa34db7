import { useState } from 'react'
import { Row, Col } from 'antd'
import ReportCard from './ReportCard'
import ReportModal from './ReportModal'
import { Report, ReportGridProps } from '../types'
import { reportsList } from '../data'

const ReportGrid = ({ searchQuery }: ReportGridProps) => {
  const [selectedReport, setSelectedReport] = useState<Report | null>(null)

  const filteredReports = reportsList.filter(
    (report) =>
      report.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      report.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      report.category.toLowerCase().includes(searchQuery.toLowerCase())
  )

  const handleGenerateReport = (report: Report) => {
    setSelectedReport(report)
  }

  return (
    <>
      <Row gutter={[24, 24]}>
        {filteredReports.map((report) => (
          <Col xs={24} sm={12} lg={8} xl={6} key={report.id}>
            <ReportCard report={report} onGenerate={handleGenerateReport} />
          </Col>
        ))}
      </Row>

      <ReportModal
        report={selectedReport}
        open={!!selectedReport}
        onClose={() => setSelectedReport(null)}
      />
    </>
  )
}

export default ReportGrid
