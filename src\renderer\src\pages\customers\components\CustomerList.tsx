import { useEffect, useState } from 'react'
import { Table, Popconfirm, Tag, App } from 'antd'
import { DeleteOutlined, EditOutlined } from '@ant-design/icons'
import { partyApi } from '@/renderer/services'
import type { ColumnsType } from 'antd/es/table'
import { formatCurrency } from '@/renderer/utils'
import { useApi } from '@/renderer/hooks'
import { GetPartiesParams, PartyType } from '@/common/types'

interface CustomerListProps {
  onCustomerClick: (id: string) => void
  searchQuery: string
  refreshTrigger: number
}

interface CustomerData {
  id: string
  name: string
  contact?: string
  address?: string
  phoneNumber?: string
  currentBalance: number
}

export const CustomerList = ({
  onCustomerClick,
  searchQuery,
  refreshTrigger
}: CustomerListProps) => {
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0
  })

  const { message } = App.useApp()

  const {
    data: customerData,
    isLoading: loading,
    request: fetchCustomers,
    error
  } = useApi<{ parties: CustomerData[]; total: number }, [GetPartiesParams]>(partyApi.getParties)

  useEffect(() => {
    fetchCustomers({
      page: pagination.current,
      limit: pagination.pageSize,
      search: searchQuery,
      type: PartyType.CUSTOMER
    })
  }, [pagination.current, pagination.pageSize, searchQuery, refreshTrigger])

  useEffect(() => {
    if (customerData) {
      setPagination((prev) => ({
        ...prev,
        total: customerData.total
      }))
    }
  }, [customerData])

  const handleDelete = async (id: string) => {
    const response = await partyApi.deleteParty(id)

    if (response.error.error || response.data.error) {
      message.error(response.error.message || response.data.error.message)
      return
    }

    message.success('Customer deleted successfully')
    fetchCustomers({
      page: pagination.current,
      limit: pagination.pageSize,
      search: searchQuery,
      type: PartyType.CUSTOMER
    })
  }

  const columns: ColumnsType<CustomerData> = [
    {
      title: 'No',
      dataIndex: 'index',
      render: (_, __, index) => index + 1 + (pagination.current - 1) * pagination.pageSize
    },
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name'
    },
    {
      title: 'Contact',
      dataIndex: 'contact',
      key: 'contact'
    },
    {
      title: 'Phone',
      dataIndex: 'phoneNumber',
      key: 'phoneNumber'
    },
    {
      title: 'Balance',
      dataIndex: 'currentBalance',
      key: 'currentBalance',
      render: (balance) => (
        <Tag color={balance > 0 ? 'green' : balance < 0 ? 'red' : 'default'}>
          {/* {formatCurrency(Math.abs(balance))} */}
          {formatCurrency(balance)}
        </Tag>
      )
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record) => (
        <div className="action-buttons">
          <EditOutlined onClick={() => onCustomerClick(record.id)} />
          <Popconfirm
            title="Delete Customer"
            description="Are you sure you want to delete this customer?"
            onConfirm={() => handleDelete(record.id)}
            okText="Yes"
            cancelText="No"
          >
            <DeleteOutlined className="delete-icon" />
          </Popconfirm>
        </div>
      )
    }
  ]

  return (
    <Table
      columns={columns}
      dataSource={customerData?.parties || []}
      rowKey="id"
      loading={loading}
      sticky
      virtual
      pagination={{
        ...pagination,
        position: ['topRight'],
        showPrevNextJumpers: true,
        showSizeChanger: true,
        showQuickJumper: true,
        showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`
      }}
      onChange={(newPagination) => setPagination({ ...pagination, ...newPagination })}
    />
  )
}
