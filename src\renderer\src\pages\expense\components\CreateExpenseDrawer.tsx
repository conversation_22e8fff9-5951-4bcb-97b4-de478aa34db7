import { useState } from 'react'
import { Form, Input, Drawer, <PERSON><PERSON>, DatePicker, InputNumber, Select, App } from 'antd'
import { expenseApi } from '@/renderer/services'
import dayjs from 'dayjs'
import { useSelector } from 'react-redux'
import { IRootState } from '@/renderer/redux'
import { useBankContext } from '@/renderer/contexts'
import { CreateExpenseData, CashLocation } from '@/common/types'

// This interface is only used as props between components, so it stays here
interface CreateExpenseDrawerProps {
  open: boolean
  onClose: () => void
  onExpenseCreated: () => void
}

export const CreateExpenseDrawer = ({
  open,
  onClose,
  onExpenseCreated
}: CreateExpenseDrawerProps) => {
  const [form] = Form.useForm()
  const [paymentSource, setPaymentSource] = useState<CashLocation | null>(null)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const { message } = App.useApp()

  const user = useSelector((state: IRootState) => state.user.data)
  const { banks } = useBankContext()

  const handleSourceChange = (value: CashLocation) => {
    setPaymentSource(value)
    if (value !== CashLocation.BANK) {
      form.setFieldValue('bankId', undefined)
    }
  }

  const handleSubmit = async (values: any) => {
    if (!user?.id) {
      message.error('User information not found')
      return
    }

    setIsSubmitting(true)
    const expenseData: CreateExpenseData = {
      category: values.category,
      amount: values.amount,
      description: values.description,
      date: values.date.toDate(),
      createdById: user.id,
      paymentSource: values.paymentSource,
      bankId: values.paymentSource === CashLocation.BANK ? values.bankId : undefined
    }

    const response = await expenseApi.createExpense(expenseData)

    setIsSubmitting(false)

    if (response.error.error || response.data.error) {
      message.error(response.error.message || response.data.error.message)
      return
    }

    message.success('Expense added successfully')
    form.resetFields()
    onExpenseCreated()
  }

  const handleClose = () => {
    form.resetFields()
    onClose()
  }

  return (
    <Drawer
      title="Add New Expense"
      placement="right"
      onClose={handleClose}
      open={open}
      width={500}
      extra={
        <Button type="primary" onClick={() => form.submit()} loading={isSubmitting}>
          Create
        </Button>
      }
    >
      <Form form={form} layout="vertical" onFinish={handleSubmit}>
        <Form.Item
          name="date"
          label="Date"
          rules={[{ required: true, message: 'Please select date' }]}
          initialValue={dayjs()}
        >
          <DatePicker style={{ width: '100%' }} />
        </Form.Item>

        <Form.Item
          name="category"
          label="Category"
          rules={[{ required: true, message: 'Please select category' }]}
        >
          <Select>
            <Select.Option value="Food">Food</Select.Option>
            <Select.Option value="Transport">Transport</Select.Option>
            <Select.Option value="Entertainment">Entertainment</Select.Option>
            <Select.Option value="Shopping">Shopping</Select.Option>
            <Select.Option value="Bills">Bills</Select.Option>
            <Select.Option value="Salary">Salary</Select.Option>
            <Select.Option value="Rent">Rent</Select.Option>
            <Select.Option value="Utilities">Utilities</Select.Option>
            <Select.Option value="Others">Others</Select.Option>
          </Select>
        </Form.Item>

        <Form.Item
          name="amount"
          label="Amount"
          rules={[
            { required: true, message: 'Please enter amount' },
            { type: 'number', min: 0.01, message: 'Amount must be greater than 0' }
          ]}
        >
          <InputNumber
            style={{ width: '100%' }}
            formatter={(value) => `$ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
            parser={(value) => value!.replace(/\$\s?|(,*)/g, '')}
          />
        </Form.Item>

        <Form.Item
          name="paymentSource"
          label="Payment Source"
          rules={[{ required: true, message: 'Please select payment source' }]}
        >
          <Select onChange={handleSourceChange}>
            <Select.Option value={CashLocation.SMALL_COUNTER}>Small Counter</Select.Option>
            <Select.Option value={CashLocation.CASH_VAULT}>Cash Vault</Select.Option>
            <Select.Option value={CashLocation.BANK}>Bank</Select.Option>
          </Select>
        </Form.Item>

        {paymentSource === CashLocation.BANK && (
          <Form.Item
            name="bankId"
            label="Bank Account"
            rules={[{ required: true, message: 'Please select bank account' }]}
          >
            <Select
              allowClear
              showSearch
              options={banks}
              filterOption={(input, option) =>
                (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
              }
            />
          </Form.Item>
        )}

        <Form.Item
          name="description"
          label="Description"
          rules={[{ required: true, message: 'Please enter description' }]}
        >
          <Input.TextArea rows={4} />
        </Form.Item>
      </Form>
    </Drawer>
  )
}
