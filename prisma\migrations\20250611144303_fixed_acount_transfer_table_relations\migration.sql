/*
  Warnings:

  - A unique constraint covering the columns `[fromLedgerId]` on the table `AccountTransfer` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[toLedgerId]` on the table `AccountTransfer` will be added. If there are existing duplicate values, this will fail.

*/
-- CreateIndex
CREATE UNIQUE INDEX "AccountTransfer_fromLedgerId_key" ON "AccountTransfer"("fromLedgerId");

-- CreateIndex
CREATE UNIQUE INDEX "AccountTransfer_toLedgerId_key" ON "AccountTransfer"("toLedgerId");

-- AddForeignKey
ALTER TABLE "AccountTransfer" ADD CONSTRAINT "AccountTransfer_fromLedgerId_fkey" FOREIGN KEY ("fromLedgerId") REFERENCES "Ledger"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeign<PERSON>ey
ALTER TABLE "AccountTransfer" ADD CONSTRAINT "AccountTransfer_toLedgerId_fkey" FOREIGN KEY ("toLedgerId") REFERENCES "Ledger"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
