import { Status } from '@prisma/client';


export interface CreateBankData {
    name: string;
    accountNo: string;
    openingBalance?: number;
    adminId: string;
}

export interface GetAllBanksFilter {
    isActive?: boolean;
    page?: number;
    limit?: number;
}

export interface BankResponse {
    banks: Bank[];
    total: number;
    page: number;
    totalPages: number;
}


// Bank Types
export interface Bank {
    id: string;
    name: string;
    accountNo: string;
    balance: number;
    isActive: boolean;
}

export interface CreateBankData {
    name: string;
    accountNo: string;
    openingBalance?: number;
    adminId: string;
}

export interface GetAllBanksFilter {
    isActive?: boolean;
    page?: number;
    limit?: number;
}

// Bank Ledger Types
export interface BankLedgerFilter {
    bankId?: string;
    startDate?: Date;
    endDate?: Date;
    creditOrDebit?: 'CREDIT' | 'DEBIT';
    referenceType?: string;
    status?: Status;
    page?: number;
    limit?: number;
}

export interface BankDetailsTabKey {
    key: 'ledger' | 'summary' | 'search' | 'daily-balances' | 'reconciliation';
}