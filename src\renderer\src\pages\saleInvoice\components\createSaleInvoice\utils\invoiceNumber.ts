import { invoiceNumberApi } from '@/renderer/services'
import { App } from 'antd'

export const generateInvoiceNumber = async (
    createMode: 'REGISTERED' | 'WALK_IN' | 'none',
    userId: string,
    message: any
) => {
    if (!userId) {
        message.error('User not found')
        return null
    }

    const response = await invoiceNumberApi.generateInvoiceNumber(
        createMode === 'REGISTERED' ? 'REGISTERED' : 'WALK_IN',
        userId
    )

    if (response.error.error || response.data.error) {
        message.error(response.error.message || response.data.error.message)
        return null
    }

    return response.data.data.invoiceNumber
}

export const cancelInvoiceNumber = async (invoiceNumber: string) => {
    console.log('running cancelInvoiceNumber', invoiceNumber)
    if (!invoiceNumber) return
    console.log('canceling InvoiceNumber', invoiceNumber)
    return await invoiceNumberApi.cancelInvoiceNumber(invoiceNumber)
}

export const confirmInvoiceNumber = async (invoiceNumber: string) => {
    if (!invoiceNumber) return
    return await invoiceNumberApi.confirmInvoiceNumber(invoiceNumber)
} 