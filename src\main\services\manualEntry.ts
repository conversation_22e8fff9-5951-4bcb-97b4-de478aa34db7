import { prisma } from '../db';
import {
    CreateManualEntryData,
    GetManualEntriesParams,
    VoidManualEntryParams,
    ManualEntriesResponse,
    CreateManualEntryResult,
    VoidManualEntryResult,
    ManualEntryItem,
    ManualEntryStatus,
    ManualEntrySortOrder
} from '@/common/types/manualEntry';
import { CashLocation, CreditDebit, LedgerType, ManualEntryTarget, Status } from '@prisma/client';

class ManualEntryService {
    async createManualEntry(data: CreateManualEntryData): Promise<CreateManualEntryResult> {
        // Validate that exactly one target is specified
        const targetCount = [data.partyId, data.bankId, data.targetType].filter(x => x !== undefined && x !== null).length;
        if (targetCount !== 1) {
            throw new Error('Exactly one target (party, bank, or target type) must be specified');
        }

        // Validate amount
        if (data.amount <= 0) {
            throw new Error('Amount must be greater than zero');
        }

        return await prisma.$transaction(async (tx) => {
            let cashSource: CashLocation | undefined;
            let cashDestination: CashLocation | undefined;
            let partyId: string | undefined;
            let bankId: string | undefined;
            let description = data.description || 'Manual entry';

            // Determine cash flow and update balances based on target
            if (data.partyId) {
                // Manual entry for party
                const party = await tx.party.findUnique({
                    where: { id: data.partyId },
                    select: { id: true, name: true, currentBalance: true }
                });

                if (!party) {
                    throw new Error('Party not found');
                }

                partyId = data.partyId;
                description = `Manual ${data.entryType.toLowerCase()} entry for ${party.name}`;

                // For parties, we don't specify cash source/destination as it's account-based
                // Update party balance
                const newBalance = data.entryType === CreditDebit.DEBIT
                    ? party.currentBalance + data.amount
                    : party.currentBalance - data.amount;

                await tx.party.update({
                    where: { id: data.partyId },
                    data: { currentBalance: newBalance }
                });

            } else if (data.bankId) {
                // Manual entry for bank
                const bank = await tx.banks.findUnique({
                    where: { id: data.bankId },
                    select: { id: true, name: true, balance: true, isActive: true }
                });

                if (!bank) {
                    throw new Error('Bank not found');
                }

                if (!bank.isActive) {
                    throw new Error('Bank is not active');
                }

                bankId = data.bankId;
                cashSource = data.entryType === CreditDebit.CREDIT ? CashLocation.BANK : undefined;
                cashDestination = data.entryType === CreditDebit.DEBIT ? CashLocation.BANK : undefined;
                description = `Manual ${data.entryType.toLowerCase()} entry for ${bank.name}`;

                // Update bank balance
                const newBalance = data.entryType === CreditDebit.DEBIT
                    ? bank.balance + data.amount
                    : bank.balance - data.amount;

                await tx.banks.update({
                    where: { id: data.bankId },
                    data: { balance: newBalance }
                });

            } else if (data.targetType) {
                // Manual entry for cash vault or small counter
                if (data.targetType === ManualEntryTarget.CASH_VAULT) {
                    const vault = await tx.cashVault.findFirst({
                        select: { id: true, balance: true }
                    });

                    if (!vault) {
                        throw new Error('Cash vault not found');
                    }

                    cashSource = data.entryType === CreditDebit.CREDIT ? CashLocation.CASH_VAULT : undefined;
                    cashDestination = data.entryType === CreditDebit.DEBIT ? CashLocation.CASH_VAULT : undefined;
                    description = `Manual ${data.entryType.toLowerCase()} entry for Cash Vault`;

                    // Update vault balance
                    const newBalance = data.entryType === CreditDebit.DEBIT
                        ? vault.balance + data.amount
                        : vault.balance - data.amount;

                    await tx.cashVault.update({
                        where: { id: vault.id },
                        data: { balance: newBalance }
                    });

                } else if (data.targetType === ManualEntryTarget.SMALL_COUNTER) {
                    const counter = await tx.smallCounter.findFirst({
                        select: { id: true, cashInShop: true }
                    });

                    if (!counter) {
                        throw new Error('Small counter not found');
                    }

                    cashSource = data.entryType === CreditDebit.CREDIT ? CashLocation.SMALL_COUNTER : undefined;
                    cashDestination = data.entryType === CreditDebit.DEBIT ? CashLocation.SMALL_COUNTER : undefined;
                    description = `Manual ${data.entryType.toLowerCase()} entry for Small Counter`;

                    // Update counter balance
                    const newBalance = data.entryType === CreditDebit.DEBIT
                        ? counter.cashInShop + data.amount
                        : counter.cashInShop - data.amount;

                    await tx.smallCounter.update({
                        where: { id: counter.id },
                        data: { cashInShop: newBalance }
                    });
                }
            }

            // Create ledger entry
            const ledgerEntry = await tx.ledger.create({
                data: {
                    amount: data.amount,
                    creditOrDebit: data.entryType,
                    description,
                    date: data.transactionDate,
                    referenceType: LedgerType.ManualEntry,
                    cashSource,
                    cashDestination,
                    partyId,
                    bankId,
                    createdById: data.createdById,
                    status: Status.ACTIVE
                }
            });

            // Create manual entry
            const manualEntry = await tx.manualEntry.create({
                data: {
                    amount: data.amount,
                    description: data.description,
                    transactionDate: data.transactionDate,
                    entryType: data.entryType,
                    partyId: data.partyId,
                    bankId: data.bankId,
                    targetType: data.targetType,
                    ledgerId: ledgerEntry.id,
                    createdById: data.createdById
                },
                include: {
                    party: {
                        select: { id: true, name: true, type: true }
                    },
                    bank: {
                        select: { id: true, name: true, accountNo: true }
                    },
                    ledger: {
                        select: { id: true, status: true, cashSource: true, cashDestination: true }
                    },
                    createdBy: {
                        select: { id: true, name: true }
                    }
                }
            });

            return {
                manualEntry: manualEntry as ManualEntryItem,
                message: 'Manual entry created successfully'
            };
        });
    }

    async getManualEntries(params: GetManualEntriesParams): Promise<ManualEntriesResponse> {
        const {
            page = 1,
            pageSize = 20,
            startDate,
            endDate,
            entryType,
            targetType,
            partyId,
            bankId,
            status = ManualEntryStatus.ACTIVE,
            sortOrder = ManualEntrySortOrder.OLDEST_FIRST,
            search
        } = params;

        const skip = (page - 1) * pageSize;

        // Build where clause
        let whereClause: any = {};

        // Status filtering
        if (status === ManualEntryStatus.ACTIVE) {
            whereClause.isDeleted = false;
        } else if (status === ManualEntryStatus.DELETED) {
            whereClause.isDeleted = true;
        }
        // For ALL status, don't add isDeleted filter

        // Date filtering
        if (startDate && endDate) {
            if (startDate.toDateString() === endDate.toDateString()) {
                // Same day - filter for entire day
                const start = new Date(startDate);
                start.setHours(0, 0, 0, 0);
                const end = new Date(startDate);
                end.setHours(23, 59, 59, 999);
                whereClause.transactionDate = {
                    gte: start,
                    lte: end
                };
            } else {
                // Date range
                whereClause.transactionDate = {
                    gte: startDate,
                    lte: endDate
                };
            }
        }

        // Other filters
        if (entryType) whereClause.entryType = entryType;
        if (targetType) whereClause.targetType = targetType;
        if (partyId) whereClause.partyId = partyId;
        if (bankId) whereClause.bankId = bankId;

        // Search functionality
        if (search) {
            whereClause.OR = [
                { description: { contains: search, mode: 'insensitive' } },
                { party: { name: { contains: search, mode: 'insensitive' } } },
                { bank: { name: { contains: search, mode: 'insensitive' } } }
            ];
        }

        // Determine sort order
        const orderBy = sortOrder === ManualEntrySortOrder.NEWEST_FIRST
            ? { transactionDate: 'desc' as const }
            : { transactionDate: 'asc' as const };

        const [entries, total] = await Promise.all([
            prisma.manualEntry.findMany({
                where: whereClause,
                include: {
                    party: {
                        select: { id: true, name: true, type: true }
                    },
                    bank: {
                        select: { id: true, name: true, accountNo: true }
                    },
                    ledger: {
                        select: { id: true, status: true, cashSource: true, cashDestination: true }
                    },
                    createdBy: {
                        select: { id: true, name: true }
                    },
                    deletedBy: {
                        select: { id: true, name: true }
                    }
                },
                orderBy,
                skip,
                take: pageSize
            }),
            prisma.manualEntry.count({
                where: whereClause
            })
        ]);

        return {
            entries: entries as ManualEntryItem[],
            pagination: {
                total,
                pages: Math.ceil(total / pageSize),
                currentPage: page,
                pageSize
            }
        };
    }

    async getManualEntryById(id: string): Promise<ManualEntryItem | null> {
        const entry = await prisma.manualEntry.findUnique({
            where: { id },
            include: {
                party: {
                    select: { id: true, name: true, type: true }
                },
                bank: {
                    select: { id: true, name: true, accountNo: true }
                },
                ledger: {
                    select: { id: true, status: true, cashSource: true, cashDestination: true }
                },
                createdBy: {
                    select: { id: true, name: true }
                },
                deletedBy: {
                    select: { id: true, name: true }
                }
            }
        });

        return entry as ManualEntryItem | null;
    }

    async voidManualEntry(params: VoidManualEntryParams): Promise<VoidManualEntryResult> {
        const { id, deletedById, deletionReason } = params;

        return await prisma.$transaction(async (tx) => {
            // Get the manual entry with its ledger
            const manualEntry = await tx.manualEntry.findUnique({
                where: { id },
                include: {
                    ledger: true,
                    party: true,
                    bank: true
                }
            });

            if (!manualEntry) {
                throw new Error('Manual entry not found');
            }

            if (manualEntry.isDeleted) {
                throw new Error('Manual entry is already voided');
            }

            if (manualEntry.ledger.status === Status.VOID) {
                throw new Error('Associated ledger entry is already voided');
            }

            // Reverse the balance changes
            if (manualEntry.partyId && manualEntry.party) {
                // Reverse party balance
                const reverseAmount = manualEntry.entryType === CreditDebit.DEBIT
                    ? -manualEntry.amount
                    : manualEntry.amount;

                await tx.party.update({
                    where: { id: manualEntry.partyId },
                    data: {
                        currentBalance: manualEntry.party.currentBalance + reverseAmount
                    }
                });

            } else if (manualEntry.bankId && manualEntry.bank) {
                // Reverse bank balance
                const reverseAmount = manualEntry.entryType === CreditDebit.DEBIT
                    ? -manualEntry.amount
                    : manualEntry.amount;

                await tx.banks.update({
                    where: { id: manualEntry.bankId },
                    data: {
                        balance: manualEntry.bank.balance + reverseAmount
                    }
                });

            } else if (manualEntry.targetType) {
                // Reverse cash vault or small counter balance
                if (manualEntry.targetType === ManualEntryTarget.CASH_VAULT) {
                    const vault = await tx.cashVault.findFirst();
                    if (vault) {
                        const reverseAmount = manualEntry.entryType === CreditDebit.DEBIT
                            ? -manualEntry.amount
                            : manualEntry.amount;

                        await tx.cashVault.update({
                            where: { id: vault.id },
                            data: {
                                balance: vault.balance + reverseAmount
                            }
                        });
                    }

                } else if (manualEntry.targetType === ManualEntryTarget.SMALL_COUNTER) {
                    const counter = await tx.smallCounter.findFirst();
                    if (counter) {
                        const reverseAmount = manualEntry.entryType === CreditDebit.DEBIT
                            ? -manualEntry.amount
                            : manualEntry.amount;

                        await tx.smallCounter.update({
                            where: { id: counter.id },
                            data: {
                                cashInShop: counter.cashInShop + reverseAmount
                            }
                        });
                    }
                }
            }

            // Mark ledger entry as voided
            await tx.ledger.update({
                where: { id: manualEntry.ledgerId },
                data: {
                    status: Status.VOID,
                    voidedById: deletedById
                }
            });

            // Mark manual entry as deleted
            await tx.manualEntry.update({
                where: { id },
                data: {
                    isDeleted: true,
                    deletedAt: new Date(),
                    deletedById,
                    deletionReason
                }
            });

            return {
                success: true,
                message: 'Manual entry voided successfully'
            };
        });
    }


}

export const manualEntryService = new ManualEntryService();