import { CreditDebit, ManualEntryTarget, Status } from "@prisma/client";

export interface CreateManualEntryData {
    amount: number;
    description?: string;
    transactionDate: Date;
    entryType: CreditDebit;

    // Target entity (only one should be set)
    partyId?: string;
    bankId?: string;
    targetType?: ManualEntryTarget;

    createdById: string;
}

export enum ManualEntryStatus {
    ACTIVE = 'ACTIVE',
    DELETED = 'DELETED',
    ALL = 'ALL'
}

export enum ManualEntrySortOrder {
    OLDEST_FIRST = 'OLDEST_FIRST',
    NEWEST_FIRST = 'NEWEST_FIRST'
}

export interface GetManualEntriesParams {
    page?: number;
    pageSize?: number;
    startDate?: Date;
    endDate?: Date;
    entryType?: CreditDebit;
    targetType?: ManualEntryTarget;
    partyId?: string;
    bankId?: string;
    status?: ManualEntryStatus;
    sortOrder?: ManualEntrySortOrder;
    search?: string;
}

export interface VoidManualEntryParams {
    id: string;
    deletedById: string;
    deletionReason: string;
}

export interface ManualEntryItem {
    id: string;
    amount: number;
    description?: string;
    transactionDate: Date;
    entryType: CreditDebit;

    // Target information
    partyId?: string;
    party?: {
        id: string;
        name: string;
        type: string;
    };

    bankId?: string;
    bank?: {
        id: string;
        name: string;
        accountNo: string;
    };

    targetType?: ManualEntryTarget;

    // Ledger information
    ledgerId: string;
    ledger: {
        id: string;
        status: Status;
        cashSource?: string;
        cashDestination?: string;
    };

    // Audit trail
    createdAt: Date;
    updatedAt: Date;
    createdBy: {
        id: string;
        name: string;
    };

    // Deletion tracking
    isDeleted: boolean;
    deletedAt?: Date;
    deletedBy?: {
        id: string;
        name: string;
    };
    deletionReason?: string;
}

export interface ManualEntriesResponse {
    entries: ManualEntryItem[];
    pagination: {
        total: number;
        pages: number;
        currentPage: number;
        pageSize: number;
    };
}

export interface CreateManualEntryResult {
    manualEntry: ManualEntryItem;
    message: string;
}

export interface VoidManualEntryResult {
    success: boolean;
    message: string;
}

// Filter options for frontend
export interface ManualEntryFilters {
    dateRange?: {
        startDate: Date;
        endDate: Date;
    };
    entryType?: CreditDebit;
    targetType?: ManualEntryTarget;
    partyId?: string;
    bankId?: string;
    status?: ManualEntryStatus;
    sortOrder?: ManualEntrySortOrder;
    search?: string;
}