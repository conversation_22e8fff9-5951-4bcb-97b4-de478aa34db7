.auth-layout {
  height: 100vh;
  width: 100vw;
  overflow: auto;
  display: flex;
  justify-content: center;
  align-items: center;
  .auth-container {
    width: max(330px, 40%);
    min-height: 400px;
    border-radius: 3px;
    .app-info {
      background-color: #00eebc;
      padding: 20px;
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
      p {
        font-size: 12px;
        font-weight: bold;
      }
    }
    .auth-form {
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 14px;
      .container {
        width: 80%;
        margin-inline: auto;
        margin-block: 50px;
        h3 {
          text-align: center;
        }
        .form {
          margin-top: 40px;
          .ant-form-item-label label {
            font-weight: bold;
          }
          .forgot-link {
            display: flex;
            justify-content: flex-end;
            margin-top: -20px;
            a {
              font-weight: bold;
            }
          }
          .form-actions {
            margin-top: 40px;
          }
        }
      }
    }
    @media screen and (max-width: 1000px) {
      grid-template-columns: 1fr;
      .app-info {
        display: none;
      }
    }
    @media screen and (max-width: 400px) {
      .auth-form {
        .container {
          width: 90%;
          margin-block: 30px;
        }
      }
    }
  }
}
