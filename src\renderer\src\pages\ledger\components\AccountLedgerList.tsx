import { Select, Radio, Table, Typography, Space, Button, App, Toolt<PERSON>, DatePicker } from 'antd'
import { ArrowUpOutlined, ArrowDownOutlined } from '@ant-design/icons'
import { formatDate, formatCurrency, formatCurrencyWithoutSymbol } from '@/renderer/utils'
import { LedgerEntry } from '@/common/types/ledger'
import { usePartyContext, Party } from '@/renderer/contexts/PartyContext'
import { useApi } from '@/renderer/hooks'
import { ledgerApi } from '@/renderer/services'
import { useState, useMemo, useEffect } from 'react'
import LedgerSummary from './LedgerSummary'
import { generateLedgerPDF } from '../utils/generateLedgerPDF'
import PrintSaveModal from './PrintSaveModal'
import dayjs from 'dayjs'

const { Text } = Typography
const { RangePicker } = DatePicker

export const AccountLedgerList = () => {
  const { vendors, customers, creditors } = usePartyContext()
  const [selectedPartyType, setSelectedPartyType] = useState<
    'ALL' | 'CUSTOMERS' | 'VENDORS' | 'CREDITORS'
  >('ALL')
  const [selectedParty, setSelectedParty] = useState<string | null>(null)
  const [isSummaryTransitioning, setIsSummaryTransitioning] = useState(false)
  const [dateRange, setDateRange] = useState<[dayjs.Dayjs | null, dayjs.Dayjs | null] | null>(null)
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 50,
    total: 0
  })
  const [printSaveModalVisible, setPrintSaveModalVisible] = useState(false)
  const [printSaveLoading, setPrintSaveLoading] = useState(false)

  const { message } = App.useApp()

  const {
    data: ledgerData,
    isLoading,
    request: fetchLedger,
    clearData: clearLedgerData
  } = useApi<any, [string, any?]>(ledgerApi.getLedgerEntriesByParty)

  const partyOptions = useMemo(() => {
    switch (selectedPartyType) {
      case 'CUSTOMERS':
        return customers
      case 'VENDORS':
        return vendors
      case 'CREDITORS':
        return creditors
      default:
        return [...customers, ...vendors, ...creditors]
    }
  }, [selectedPartyType, customers, vendors, creditors])

  useEffect(() => {
    if (ledgerData?.pagination) {
      setPagination((prev) => ({
        ...prev,
        total: ledgerData.pagination.total
      }))
    }
  }, [ledgerData])

  const handlePartySelect = (partyId: string | null) => {
    if (partyId) {
      setIsSummaryTransitioning(true)
      setSelectedParty(partyId)
      setPagination({ current: 1, pageSize: 50, total: 0 })

      // Build query params with date range if selected
      const queryParams = {
        page: 1,
        limit: 50,
        ...(dateRange && dateRange[0] && { startDate: dateRange[0].toISOString() }),
        ...(dateRange && dateRange[1] && { endDate: dateRange[1].toISOString() })
      }

      fetchLedger(partyId, queryParams).then(() => {
        setTimeout(() => {
          setIsSummaryTransitioning(false)
        }, 50)
      })
    } else {
      setSelectedParty(null)
      clearLedgerData()
      setPagination({ current: 1, pageSize: 50, total: 0 })
    }
  }

  const handleDateRangeChange = (dates: [dayjs.Dayjs | null, dayjs.Dayjs | null] | null) => {
    setDateRange(dates)

    // If a party is already selected, refresh the data with the new date range
    if (selectedParty) {
      setPagination({ current: 1, pageSize: 50, total: 0 })

      const queryParams = {
        page: 1,
        limit: 50,
        ...(dates && dates[0] && { startDate: dates[0].toISOString() }),
        ...(dates && dates[1] && { endDate: dates[1].toISOString() })
      }

      fetchLedger(selectedParty, queryParams)
    }
  }

  const handlePaginationChange = (page: number, pageSize: number) => {
    if (selectedParty) {
      // If pageSize changed, reset to page 1
      if (pageSize !== pagination.pageSize) {
        setPagination((prev) => ({ ...prev, current: 1, pageSize }))

        const queryParams = {
          page: 1,
          limit: pageSize,
          ...(dateRange && dateRange[0] && { startDate: dateRange[0].toISOString() }),
          ...(dateRange && dateRange[1] && { endDate: dateRange[1].toISOString() })
        }

        fetchLedger(selectedParty, queryParams)
        return
      }

      setPagination((prev) => ({ ...prev, current: page }))

      const queryParams = {
        page,
        limit: pageSize,
        ...(dateRange && dateRange[0] && { startDate: dateRange[0].toISOString() }),
        ...(dateRange && dateRange[1] && { endDate: dateRange[1].toISOString() })
      }

      fetchLedger(selectedParty, queryParams)
    }
  }

  const handleExportClick = () => {
    if (!selectedParty) {
      message.error('Please select a party')
      return
    }
    setPrintSaveModalVisible(true)
  }

  const handlePrint = async () => {
    if (!selectedParty) {
      message.error('Please select a party')
      return
    }

    try {
      setPrintSaveLoading(true)
      const response = await ledgerApi.getLedgerEntriesForPDF(selectedParty)

      if (response.error.error || response.data.error) {
        message.error(response.error.message || response.data.error.message)
        setPrintSaveLoading(false)
        return
      }

      const { entries, party, totals } = response.data.data
      const doc = await generateLedgerPDF({ entries, party, totals })

      if (doc) {
        // Get PDF as base64 data
        const pdfData = doc.output('dataurlstring', { filename: 'ledger.pdf' })

        // Send to main process for printing
        const success = await window.electron.ipcRenderer.invoke('print-pdf', pdfData)

        if (success) {
          message.success('Ledger statement sent to printer')
          setPrintSaveModalVisible(false)
        }
      }
      setPrintSaveLoading(false)
    } catch (error) {
      console.error('Error printing ledger:', error)
      message.error('Failed to print ledger statement')
      setPrintSaveLoading(false)
    }
  }

  const handleSave = async () => {
    if (!selectedParty) {
      message.error('Please select a party')
      return
    }

    try {
      setPrintSaveLoading(true)
      const response = await ledgerApi.getLedgerEntriesForPDF(selectedParty)

      if (response.error.error || response.data.error) {
        message.error(response.error.message || response.data.error.message)
        setPrintSaveLoading(false)
        return
      }

      const { entries, party, totals } = response.data.data
      const saved = await generateLedgerPDF({ entries, party, totals }, true)

      if (saved) {
        message.success('Ledger PDF generated successfully')
        setPrintSaveModalVisible(false)
      }
      setPrintSaveLoading(false)
    } catch (error) {
      console.error('Error generating PDF:', error)
      message.error('Failed to generate PDF')
      setPrintSaveLoading(false)
    }
  }

  const handlePrintSaveCancel = () => {
    setPrintSaveModalVisible(false)
    setPrintSaveLoading(false)
  }

  const columns = [
    {
      title: 'S.No',
      dataIndex: 'index',
      key: 'index',
      render: (_, __, index) => (pagination.current - 1) * pagination.pageSize + index + 1,
      width: 80
    },
    {
      title: 'Date',
      dataIndex: 'date',
      key: 'date',
      render: (date: Date) => (
        <Text className="text-gray-600">{formatDate(date.toISOString())}</Text>
      ),
      width: 120
    },
    {
      title: 'Description',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true
    },
    {
      title: 'Credit',
      key: 'credit',
      ellipsis: true,
      render: (entry: LedgerEntry) =>
        entry.creditOrDebit === 'CREDIT' ? (
          <span
            style={{
              color: '#52c41a',
              display: 'flex',
              alignItems: 'center',
              gap: '4px',
              justifyContent: 'center'
            }}
          >
            <ArrowUpOutlined />
            {formatCurrencyWithoutSymbol(entry.amount)}
          </span>
        ) : (
          '-'
        ),
      width: 160,
      align: 'center' as const
    },
    {
      title: 'Debit',
      key: 'debit',
      ellipsis: true,
      render: (entry: LedgerEntry) =>
        entry.creditOrDebit === 'DEBIT' ? (
          <span
            style={{
              color: '#f5222d',
              display: 'flex',
              alignItems: 'center',
              gap: '4px',
              justifyContent: 'center'
            }}
          >
            <ArrowDownOutlined />
            {formatCurrencyWithoutSymbol(entry.amount)}
          </span>
        ) : (
          '-'
        ),
      width: 160,
      align: 'center' as const
    },
    {
      title: 'Balance',
      dataIndex: 'balance',
      key: 'balance',
      ellipsis: true,
      render: (balance: number) => {
        const isPositive = balance >= 0
        return (
          <span
            style={{
              color: isPositive ? '#52c41a' : '#f5222d',
              display: 'flex',
              alignItems: 'center',
              gap: '4px',
              fontWeight: 'bold',
              justifyContent: 'center'
            }}
          >
            {isPositive ? <ArrowUpOutlined /> : <ArrowDownOutlined />}
            {formatCurrencyWithoutSymbol(balance)}
          </span>
        )
      },
      width: 200
    }
  ]

  return (
    <div>
      <div className="mb-5">
        <Space direction="horizontal" wrap className="w-full align-middle">
          <Select
            className="w-36"
            value={selectedPartyType}
            onChange={(value) => {
              setSelectedPartyType(value)
              setSelectedParty(null)
              clearLedgerData()
            }}
            options={[
              { value: 'ALL', label: 'All' },
              { value: 'CUSTOMERS', label: 'Customers' },
              { value: 'VENDORS', label: 'Vendors' },
              { value: 'CREDITORS', label: 'Creditors' }
            ]}
          />

          <Select
            className="!w-96"
            placeholder="Select Party"
            value={selectedParty}
            onChange={handlePartySelect}
            style={{ width: 250 }}
            options={partyOptions.map((party) => ({
              value: party.value,
              label: party.label
            }))}
            allowClear
            showSearch
            filterOption={(input, option) =>
              (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
            }
          />

          <RangePicker value={dateRange} onChange={handleDateRangeChange} />

          <Button type="primary" onClick={handleExportClick}>
            Export Statement
          </Button>
        </Space>
      </div>

      <LedgerSummary
        credits={ledgerData?.totals?.credits || 0}
        debits={ledgerData?.totals?.debits || 0}
        net={ledgerData?.totals?.net || 0}
        overall={ledgerData?.totals?.overall}
        visible={!!selectedParty && !isLoading && !isSummaryTransitioning}
      />

      <Table
        dataSource={ledgerData?.entries || []}
        columns={columns}
        loading={isLoading}
        pagination={{
          position: ['topRight'],
          showSizeChanger: true,
          showQuickJumper: true,
          showLessItems: true,
          current: pagination.current,
          pageSize: pagination.pageSize,
          total: pagination.total,
          onChange: handlePaginationChange,
          showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`
        }}
        rowKey="id"
        virtual
        sticky
        size="small"
        bordered
      />

      <PrintSaveModal
        open={printSaveModalVisible}
        loading={printSaveLoading}
        onCancel={handlePrintSaveCancel}
        onPrint={handlePrint}
        onSave={handleSave}
      />
    </div>
  )
}
