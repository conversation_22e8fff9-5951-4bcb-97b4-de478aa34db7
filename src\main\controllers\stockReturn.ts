import { IRequest } from '@/common/types';
import { stockReturnService } from '../services';
import {
    ProcessStockReturnParams,
    VoidReturnStockParams,
    GetStockReturnParams,
    GetSaleInvoiceByNumberParams,
    ProcessLegacyStockReturnParams
} from '@/common/types/stockReturn';

class StockReturnController {
    async processStockReturn(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { items, originalInvoiceNumber, adminId, paymentDetails } = req.body ?? {};
        const params: ProcessStockReturnParams = { items, originalInvoiceNumber, adminId, paymentDetails };
        const stockReturn = await stockReturnService.processStockReturn(params);
        return stockReturn;
    }

    async processLegacyStockReturn(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const {
            items,
            customerId,
            customerName,
            customerType,
            adminId,
            approverNote,
            paymentDetails,
            returnDate
        } = req.body ?? {};

        const params: ProcessLegacyStockReturnParams = {
            items,
            customerId,
            customerName,
            customerType,
            adminId,
            approverNote,
            paymentDetails,
            returnDate: returnDate ? new Date(returnDate) : undefined
        };

        const stockReturn = await stockReturnService.processLegacyStockReturn(params);
        return stockReturn;
    }

    async voidReturnStock(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { returnInvoiceId, adminId, reason } = req.body ?? {};
        const params: VoidReturnStockParams = { returnInvoiceId, adminId, reason };
        const stockReturn = await stockReturnService.voidReturnStock(params);
        return stockReturn;
    }

    async getStockReturn(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const {
            startDate,
            endDate,
            productId,
            customerId,
            returnType,
            status,
            sortOrder,
            search,
            page,
            limit
        } = req.body ?? {};

        const params: GetStockReturnParams = {
            startDate: startDate ? new Date(startDate) : undefined,
            endDate: endDate ? new Date(endDate) : undefined,
            productId,
            customerId,
            returnType,
            status,
            sortOrder,
            search,
            page,
            limit
        };

        const stockReturn = await stockReturnService.getReturnedStock(params);
        return stockReturn;
    }

    async getSaleInvoiceByNumber(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { invoiceNumber, customerType } = req.body ?? {};
        const params: GetSaleInvoiceByNumberParams = { invoiceNumber, customerType };
        const saleInvoice = await stockReturnService.getSaleInvoiceByNumber(params);
        return saleInvoice;
    }
}

export const stockReturnController = new StockReturnController();
