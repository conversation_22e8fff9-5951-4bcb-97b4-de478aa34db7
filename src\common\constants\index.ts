export enum Channels {
    PING = "ping",
    USER = "user",

    // Product channels
    CREATE_PRODUCT = "create_product",
    GET_PRODUCT = "get_product",
    UPDATE_PRODUCT = "update_product",
    DELETE_PRODUCT = "delete_product",
    GET_PRODUCTS = "get_products",
    GET_PRODUCTS_SELECT = "get_products_select",
    GET_PRODUCT_QUANTITY_REPORT = "get_product_quantity_report",

    // Dashboard channels
    GET_DAILY_SALES_OVERVIEW = "dashboard:get-daily-sales",
    GET_SALES_TIMELINE = "dashboard:get-sales-timeline",
    GET_TOP_PRODUCTS = "dashboard:get-top-products",
    GET_LOW_STOCK_PRODUCTS = "dashboard:get-low-stock",
    GET_STOCK_VALUE_OVERVIEW = "dashboard:get-stock-value",
    GET_CASH_FLOW_OVERVIEW = "dashboard:get-cash-flow",
    GET_PARTY_OVERVIEW = "dashboard:get-party-overview",
    GET_SALES_DISTRIBUTION = "dashboard:get-sales-distribution",
    GET_PAYMENT_METHOD_DISTRIBUTION = "dashboard:get-payment-distribution",

    // Category channels
    CREATE_CATEGORY = 'category:create',
    GET_ALL_CATEGORIES = 'category:get-all',
    GET_CATEGORIES_FOR_SELECT = 'category:get-for-select',
    GENERATE_PRODUCT_ID = 'category:generate-product-id',
    DELETE_CATEGORY = 'category:delete',

    // Party channels
    CREATE_PARTY = "create_party",
    GET_PARTY = "get_party",
    UPDATE_PARTY = "update_party",
    DELETE_PARTY = "delete_party",
    GET_PARTIES = "get_parties",
    GET_VENDORS_SELECT = "get_vendors_select",
    GET_CUSTOMERS_SELECT = "get_customers_select",
    GET_CREDITORS_SELECT = "get_creditors_select",
    GET_ALL_PARTIES_SELECT = "get_all_parties_select",
    GET_DEBTORS = "get_debtors",
    GET_CREDITORS = "get_creditors",
    GET_SETTLED = "get_settled",
    GET_PARTIES_BY_TYPE_FOR_PDF = "get_parties_by_type_for_pdf",

    // Expense channels
    CREATE_EXPENSE = "create_expense",
    DELETE_EXPENSE = "delete_expense",
    GET_EXPENSE = "get_expense",
    GET_EXPENSES = "get_expenses",
    GET_EXPENSES_BY_DAY = "get_expenses_by_day",
    GET_EXPENSES_BY_DATE_RANGE = "get_expenses_by_date_range",

    // Purchase Invoice channels
    CREATE_PURCHASE_INVOICE = "create_purchase_invoice",
    VOID_PURCHASE_INVOICE = "void_purchase_invoice",
    GET_PURCHASE_INVOICE = "get_purchase_invoice",
    GET_ALL_PURCHASE_INVOICES = "get_all_purchase_invoices",
    GET_PURCHASE_INVOICES_BY_VENDOR = "get_purchase_invoices_by_vendor",

    // Stock channels
    GET_AVAILABLE_STOCK = "stock/available",
    GET_SOLD_STOCK = "stock/sold",
    GET_STOCK_BY_PRODUCT = "stock/by-product",
    GET_STOCK_BY_VENDOR = "stock/by-vendor",
    GET_STOCK_BY_PURCHASE_INVOICE = "stock/by-purchase-invoice",

    // Ledger channels
    GET_BY_PARTY = "ledger:get-by-party",
    GET_DAILY = "ledger:get-daily",
    GET_LEDGER_ENTRIES_FOR_PDF = "ledger:get-entries-for-pdf",

    // Bank channels
    CREATE_BANK = "bank:create",
    GET_BANK_BY_ID = "bank:get-by-id",
    DELETE_BANK = "bank:delete",
    GET_BANKS_FOR_SELECT = "bank:get-for-select",
    GET_ALL_BANKS = "bank:get-all",
    DEACTIVATE_BANK = "bank:deactivate",
    REACTIVATE_BANK = "bank:reactivate",

    // Bank Ledger channels
    GET_BANK_LEDGER_ENTRIES = "bank-ledger:get-entries",
    GET_BANK_TRANSACTION_SUMMARY = "bank-ledger:get-transaction-summary",
    GET_DAILY_BANK_LEDGER = "bank-ledger:get-daily",
    RECONCILE_BANK = "bank-ledger:reconcile",
    SEARCH_BANK_TRANSACTIONS = "bank-ledger:search",
    GET_DAILY_CLOSING_BALANCES = "bank-ledger:get-daily-closing-balances",

    // Small Counter channels
    GET_SMALL_COUNTER_BALANCE = 'get-small-counter-balance',
    GET_SMALL_COUNTER_TRANSACTIONS = 'get-small-counter-transactions',
    TRANSFER_TO_VAULT = 'transfer-to-vault',
    INITIALIZE_SMALL_COUNTER = 'initialize-small-counter',
    RECONCILE_SMALL_COUNTER = 'reconcile-small-counter',
    GENERATE_SMALL_COUNTER_STATEMENT = 'small-counter/generate-statement',

    // Cash Vault channels
    IS_VAULT_INITIALIZED = 'is-vault-initialized',
    GET_VAULT_BALANCE = 'get-vault-balance',
    INITIALIZE_VAULT = 'initialize-vault',
    DEPOSIT_TO_VAULT = 'deposit-to-vault',
    WITHDRAW_FROM_VAULT = 'withdraw-from-vault',
    GET_VAULT_LEDGER_ENTRIES = 'get-vault-ledger-entries',
    GET_VAULT_LEDGER_BY_DATE = 'get-vault-ledger-by-date',
    GET_VAULT_LEDGER_BY_RANGE = 'get-vault-ledger-by-range',
    GET_VAULT_TRANSACTION_SUMMARY = 'get-vault-transaction-summary',
    GET_VAULT_DAILY_BALANCES = 'get-vault-daily-balances',
    RECONCILE_VAULT = 'reconcile-vault',
    GENERATE_CASH_VAULT_STATEMENT = 'cash-vault/generate-statement',

    // Payment channels
    CREATE_PAYMENT = 'create-payment',
    GET_PAYMENT_BY_ID = 'get-payment-by-id',
    GET_PAYMENTS = 'get-payments',
    GET_PAYMENTS_BY_DATE_RANGE = 'get-payments-by-date-range',
    VOID_PAYMENT = 'void-payment',
    // Transfer channels
    TRANSFER_BETWEEN_LOCATIONS = 'transfer/between-locations',
    TRANSFER_BETWEEN_PARTIES = 'transfer/between-parties',
    GET_TRANSFERS = 'transfer/get-transfers',
    VOID_TRANSFER = 'transfer/void-transfer',

    // Sale Invoice Channels
    CREATE_WALK_IN_SALE = 'sale-invoice/create-walk-in',
    CREATE_REGISTERED_SALE = 'sale-invoice/create-registered',
    VOID_WALK_IN_SALE = 'sale-invoice/void-walk-in',
    VOID_REGISTERED_SALE = 'sale-invoice/void-registered',
    GET_SALE_BY_ID = 'sale-invoice/get-by-id',
    GET_ALL_SALES = 'sale-invoice/get-all',

    // User channels
    CREATE_USER = "create_user",
    UPDATE_PASSWORD = "update_password",
    GET_CURRENT_USER = "get_current_user",
    RESET_PASSWORD = "reset_password",
    GET_USERS = "get_users",
    DEACTIVATE_USER = "deactivate_user",
    LOGIN = "login",
    ACTIVATE_USER = "activate_user",

    // License channels
    GET_LICENSE = 'get_license',
    VERIFY_LICENSE = 'verify_license',

    // Reset channels
    CLEAR_LEDGER = 'clear_ledger',
    CLEAR_STOCK_ENTRIES = 'clear_stock_entries',
    CLEAR_WALK_IN_SALE_ITEMS = 'clear_walk_in_sale_items',
    CLEAR_SALE_ITEMS = 'clear_sale_items',
    CLEAR_WALK_IN_SALE_INVOICES = 'clear_walk_in_sale_invoices',
    CLEAR_SALE_INVOICES = 'clear_sale_invoices',
    CLEAR_PURCHASE_ITEMS = 'clear_purchase_items',
    CLEAR_STOCK = 'clear_stock',
    CLEAR_PURCHASE_INVOICES = 'clear_purchase_invoices',
    CLEAR_PRODUCTS = 'clear_products',
    CLEAR_CATEGORIES = 'clear_categories',
    CLEAR_PAYMENTS = 'clear_payments',
    CLEAR_BANKS = 'clear_banks',
    CLEAR_SMALL_COUNTER = 'clear_small_counter',
    CLEAR_CASH_VAULT = 'clear_cash_vault',
    CLEAR_EXPENSES = 'clear_expenses',
    CLEAR_INVOICE_NUMBERS = 'clear_invoice_numbers',
    CLEAR_PARTIES = 'clear_parties',
    CLEAR_ADMINS = 'clear_admins',
    CLEAR_ALL_DATA = 'clear_all_data',

    // Backup channels
    CREATE_BACKUP = 'backup/create',
    RESTORE_BACKUP = 'backup/restore',

    // Invoice Number channels
    GENERATE_INVOICE_NUMBER = 'invoice-number/generate',
    CONFIRM_INVOICE_NUMBER = 'invoice-number/confirm',
    CANCEL_INVOICE_NUMBER = 'invoice-number/cancel',
    CLEANUP_EXPIRED_INVOICE_NUMBERS = 'invoice-number/cleanup-expired',

    // Opening Stock channels
    GET_OPENING_STOCK = 'opening-stock/get',
    ADD_OPENING_STOCK = 'opening-stock/add',

    // Report channels
    GENERATE_FINANCIAL_OVERVIEW = 'reports/financial-overview',
    GENERATE_INVENTORY_VALUATION = 'reports/inventory-valuation',
    GENERATE_CASH_FLOW = 'reports/cash-flow',
    GENERATE_SALES_PERFORMANCE = 'reports/sales-performance',
    GENERATE_CUSTOMER_ANALYTICS = 'reports/customer-analytics',
    GENERATE_AGEING_REPORT = 'reports/ageing',
    GENERATE_DAILY_OPERATIONS = 'reports/daily-operations',
    GENERATE_AUDIT_REPORT = 'reports/audit',

    // Return Stock Channels
    GET_RETURNED_STOCK = 'return-stock/get',
    PROCESS_RETURN_STOCK = 'return-stock/process',
    PROCESS_LEGACY_RETURN_STOCK = 'return-stock/process-legacy',
    VOID_RETURN_STOCK = 'return-stock/void',
    GET_SALE_INVOICE_BY_NUMBER = 'return-stock/get-sale-invoice-by-number',

    // Vendor Return Channels
    GET_VENDOR_RETURNS = 'vendor-return/get',
    PROCESS_VENDOR_RETURN = 'vendor-return/process',
    VOID_VENDOR_RETURN = 'vendor-return/void',

    // Manual Entry Channels
    CREATE_MANUAL_ENTRY = 'manual-entry/create',
    GET_MANUAL_ENTRIES = 'manual-entry/get-all',
    GET_MANUAL_ENTRY_BY_ID = 'manual-entry/get-by-id',
    VOID_MANUAL_ENTRY = 'manual-entry/void',

    // Account Transfer Channels
    CREATE_ACCOUNT_TRANSFER = 'account-transfer/create',
    GET_ACCOUNT_TRANSFERS = 'account-transfer/get-all',
    GET_ACCOUNT_TRANSFER_BY_ID = 'account-transfer/get-by-id',
    VOID_ACCOUNT_TRANSFER = 'account-transfer/void',


}


export * from './roles'
export * from './routes'
export * from './localStorage'
export * from './menu'
export * from './rules'
