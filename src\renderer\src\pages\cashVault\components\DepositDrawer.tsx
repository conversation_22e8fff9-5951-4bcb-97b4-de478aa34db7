// import { Button, Drawer, Form, Input, InputNumber, Space, message } from 'antd'
// import { useState } from 'react'
// import { GiReceiveMoney } from 'react-icons/gi'
// import { cashVaultApi } from '@/renderer/services'

// interface DepositDrawerProps {
//   open: boolean
//   onClose: () => void
//   onBalanceUpdate: () => void
//   userId: string
// }

// interface DepositFormValues {
//   amount: number
//   description: string
// }

// export const DepositDrawer = ({ open, onClose, onBalanceUpdate, userId }: DepositDrawerProps) => {
//   const [form] = Form.useForm()
//   const [loading, setLoading] = useState(false)

//   const handleDeposit = async (values: DepositFormValues) => {
//     setLoading(true)
//     const response = await cashVaultApi.depositToVault({
//       amount: values.amount,
//       description: values.description,
//       adminId: userId
//     })
//     setLoading(false)

//     if (response.error.error || response.data.error) {
//       message.error(response.error.message || response.data.error.message)
//       return
//     }

//     message.success('Amount deposited successfully')
//     form.resetFields()
//     onBalanceUpdate()
//     onClose()
//   }

//   return (
//     <Drawer
//       title="Deposit to Vault"
//       placement="right"
//       onClose={onClose}
//       open={open}
//       width={400}
//       extra={
//         <Space>
//           <Button onClick={onClose}>Cancel</Button>
//           <Button
//             type="primary"
//             icon={<GiReceiveMoney />}
//             loading={loading}
//             onClick={() => form.submit()}
//           >
//             Deposit
//           </Button>
//         </Space>
//       }
//     >
//       <Form form={form} layout="vertical" onFinish={handleDeposit}>
//         <Form.Item
//           label="Amount"
//           name="amount"
//           rules={[
//             { required: true, message: 'Please enter amount' },
//             {
//               type: 'number',
//               min: 0.01,
//               message: 'Amount must be greater than 0'
//             }
//           ]}
//         >
//           <InputNumber
//             style={{ width: '100%' }}
//             prefix="Rs. "
//             precision={2}
//             placeholder="Enter amount to deposit"
//           />
//         </Form.Item>

//         <Form.Item
//           label="Description"
//           name="description"
//           rules={[{ required: true, message: 'Please enter description' }]}
//         >
//           <Input.TextArea rows={4} placeholder="Enter transaction description" />
//         </Form.Item>
//       </Form>
//     </Drawer>
//   )
// }
