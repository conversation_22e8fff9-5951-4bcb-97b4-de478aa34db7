import { Card, DatePicker, Space, Switch, Table, Tooltip, Typography, message } from 'antd'
import { ColumnsType } from 'antd/es/table'
import { CashVaultTransaction } from '@/common/types'
import dayjs from 'dayjs'
import { FaMoneyBillTrendUp, FaMoneyBillTransfer } from 'react-icons/fa6'
import { useEffect, useState } from 'react'
import { cashVaultApi } from '@/renderer/services'
import { useTheme } from '@/renderer/contexts'

const { Title, Text } = Typography
const { RangePicker } = DatePicker

export const TransactionList = () => {
  // Local state
  const [transactions, setTransactions] = useState<CashVaultTransaction[]>([])
  const [loading, setLoading] = useState(false)
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0
  })
  const [dateRange, setDateRange] = useState<[Date | null, Date | null]>([null, null])
  const [includeDeleted, setIncludeDeleted] = useState(false)

  const { isDarkMode } = useTheme()

  // Load transactions
  useEffect(() => {
    loadTransactions()
  }, [pagination.current, pagination.pageSize, dateRange, includeDeleted])

  const loadTransactions = async () => {
    setLoading(true)
    const response = await cashVaultApi.getAllTransactions({
      page: pagination.current,
      pageSize: pagination.pageSize,
      startDate: dateRange[0] || undefined,
      endDate: dateRange[1] || undefined,
      includeDeleted
    })

    console.log('cash vault transaction response', response)

    if (response.error.error || response.data.error) {
      message.error(response.error.message || response.data.error.message)
      return
    }

    setTransactions(response.data.data.transactions)
    setPagination((prev) => ({
      ...prev,
      total: response.data.data.pagination.total
    }))

    setLoading(false)
  }

  const columns: ColumnsType<CashVaultTransaction> = [
    {
      title: 'Sr. No.',
      dataIndex: 'index',
      key: 'index',
      width: 70,
      render: (_, __, index) => (pagination.current - 1) * pagination.pageSize + index + 1
    },
    {
      title: 'Date',
      dataIndex: 'date',
      key: 'date',
      render: (date: string) => dayjs(date).format('DD/MM/YYYY HH:mm'),
      width: 140
    },
    {
      title: 'Description',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
      render: (text: string) => text
    },
    {
      title: 'Amount',
      dataIndex: 'amount',
      key: 'amount',
      align: 'center',
      ellipsis: true,
      render: (amount: number, record) => (
        <Space>
          {record.creditOrDebit === 'CREDIT' ? (
            <FaMoneyBillTrendUp className="text-green-500" />
          ) : (
            <FaMoneyBillTransfer className="text-red-500" />
          )}
          <span className={record.creditOrDebit === 'CREDIT' ? 'text-green-500' : 'text-red-500'}>
            Rs. {amount.toFixed(2)}
          </span>
        </Space>
      ),
      width: 190
    },
    {
      title: 'Type',
      dataIndex: 'referenceType',
      key: 'referenceType',
      width: 150
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      width: 100
    },
    {
      title: 'Created By',
      dataIndex: ['createdBy', 'name'],
      key: 'createdBy',
      width: 150
    }
  ]

  return (
    <Card
      className={`bg-[length:200%_200%] bg-[center] shadow-lg ${
        isDarkMode
          ? 'bg-gradient-to-bl from-indigo-950 via-black to-indigo-900 shadow-indigo-950'
          : 'bg-gradient-to-bl from-indigo-200 via-white to-indigo-100 shadow-indigo-200'
      }`}
    >
      <div className="mb-4 flex items-center justify-between">
        <Title level={3} className="!mb-0">
          Transactions
        </Title>
        <Space>
          <RangePicker
            onChange={(_, dateStrings) => {
              setDateRange([
                dateStrings[0] ? new Date(dateStrings[0]) : null,
                dateStrings[1] ? new Date(dateStrings[1]) : null
              ])
            }}
          />
          <Switch
            checkedChildren="Include Deleted"
            unCheckedChildren="Hide Deleted"
            checked={includeDeleted}
            onChange={setIncludeDeleted}
          />
        </Space>
      </div>
      <Table
        columns={columns}
        dataSource={transactions}
        rowKey="id"
        loading={loading}
        size="small"
        virtual
        sticky
        pagination={{
          ...pagination,
          onChange: (page, pageSize) =>
            setPagination((prev) => ({ ...prev, current: page, pageSize })),
          position: ['topRight'],
          showPrevNextJumpers: true,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`
        }}
      />
    </Card>
  )
}
