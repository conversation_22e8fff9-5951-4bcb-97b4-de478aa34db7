import React from 'react'
import <PERSON>actD<PERSON> from 'react-dom/client'
import App from './App'
import { MemoryRouter } from 'react-router-dom'
import { ThemeProvider } from './contexts/ThemeContext'

ReactDOM.createRoot(document.getElementById('root') as HTMLElement).render(
  <React.StrictMode>
    <ThemeProvider>
      <MemoryRouter>
        <App />
      </MemoryRouter>
    </ThemeProvider>
  </React.StrictMode>
)
