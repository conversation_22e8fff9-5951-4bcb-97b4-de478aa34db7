import { But<PERSON>, Drawer, Form, InputNumber, Space } from 'antd'
import { GiReceiveMoney } from 'react-icons/gi'

interface InitializeDrawerProps {
  open: boolean
  onClose: () => void
  onInitialize: (amount: number) => Promise<void>
  loading: boolean
}

export const InitializeDrawer = ({
  open,
  onClose,
  onInitialize,
  loading
}: InitializeDrawerProps) => {
  const [form] = Form.useForm()

  const handleInitialize = async (values: { amount: number }) => {
    await onInitialize(values.amount)
    form.resetFields()
    onClose()
  }

  return (
    <Drawer
      title="Initialize Small Counter"
      placement="right"
      onClose={onClose}
      open={open}
      width={400}
      extra={
        <Space>
          <Button onClick={onClose}>Cancel</Button>
          <Button
            type="primary"
            icon={<GiReceiveMoney />}
            loading={loading}
            onClick={() => form.submit()}
          >
            Initialize
          </Button>
        </Space>
      }
    >
      <Form form={form} layout="vertical" onFinish={handleInitialize}>
        <Form.Item
          label="Opening Balance"
          name="amount"
          rules={[
            { required: true, message: 'Please enter opening balance' },
            {
              type: 'number',
              min: 0,
              message: 'Opening balance cannot be negative'
            }
          ]}
        >
          <InputNumber
            style={{ width: '100%' }}
            prefix="Rs. "
            precision={2}
            placeholder="Enter opening balance"
          />
        </Form.Item>
      </Form>
    </Drawer>
  )
}
