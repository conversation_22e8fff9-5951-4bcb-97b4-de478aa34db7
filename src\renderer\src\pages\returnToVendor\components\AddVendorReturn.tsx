import { useState, useEffect } from 'react'
import {
  Form,
  Input,
  Button,
  Select,
  InputNumber,
  Table,
  Card,
  Typography,
  Space,
  Divider,
  message,
  DatePicker,
  Tooltip
} from 'antd'
import {
  PlusOutlined,
  MinusCircleOutlined,
  SaveOutlined,
  ShopOutlined,
  DollarOutlined,
  BankOutlined
} from '@ant-design/icons'
import { useBankContext, usePartyContext, useProductContext } from '@/renderer/contexts'
import { vendorReturnApi } from '@/renderer/services'
import { useApi } from '@/renderer/hooks'
import dayjs from 'dayjs'
import { IRootState } from '@/renderer/redux'
import { useSelector } from 'react-redux'
import {
  VendorReturnFormItem,
  ProductOption,
  ProcessVendorReturnParams,
  CashLocation
} from '@/common/types'

const { Title, Text } = Typography
const { Option } = Select

export interface AddVendorReturnProps {
  setRefreshTrigger: (trigger: any) => void
}

const AddVendorReturn = ({ setRefreshTrigger }: AddVendorReturnProps) => {
  const [form] = Form.useForm()
  const [items, setItems] = useState<VendorReturnFormItem[]>([])
  const [selectedVendor, setSelectedVendor] = useState<string | null>(null)
  const [totalAmount, setTotalAmount] = useState<number>(0)
  const [paymentSource, setPaymentSource] = useState<CashLocation | null>(null)
  const [selectedBank, setSelectedBank] = useState<string | undefined>(undefined)
  const [isSubmitting, setIsSubmitting] = useState(false)

  const { vendors } = usePartyContext()
  const { products } = useProductContext()
  const { banks } = useBankContext()

  const user = useSelector((state: IRootState) => state.user.data)

  useEffect(() => {
    calculateTotal()
  }, [items])

  const addItem = () => {
    const newItem: VendorReturnFormItem = {
      productId: '',
      quantity: 1,
      purchasePrice: 0,
      key: Date.now().toString()
    }
    setItems([...items, newItem])
  }

  const removeItem = (key: string) => {
    setItems(items.filter((item) => item.key !== key))
  }

  const handleProductChange = (productId: string, key: string) => {
    const updatedItems = items.map((item) => {
      if (item.key === key) {
        const selectedProduct = products.find((p) => p.value === productId) as
          | ProductOption
          | undefined
        return {
          ...item,
          productId,
          purchasePrice: selectedProduct?.purchasePrice || 0
        }
      }
      return item
    })
    setItems(updatedItems)
  }

  const handleQuantityChange = (quantity: number | null, key: string) => {
    const updatedItems = items.map((item) => {
      if (item.key === key) {
        return {
          ...item,
          quantity: quantity || 0
        }
      }
      return item
    })
    setItems(updatedItems)
  }

  const handlePriceChange = (price: number | null, key: string) => {
    const updatedItems = items.map((item) => {
      if (item.key === key) {
        return {
          ...item,
          purchasePrice: price || 0
        }
      }
      return item
    })
    setItems(updatedItems)
  }

  const calculateTotal = () => {
    const total = items.reduce((sum, item) => {
      return sum + item.quantity * item.purchasePrice
    }, 0)
    setTotalAmount(total)
  }

  const handleSubmit = async () => {
    await form.validateFields()

    if (!selectedVendor) {
      message.error('Please select a vendor')
      return
    }

    if (items.length === 0) {
      message.error('Please add at least one item')
      return
    }

    // Validate items
    for (const item of items) {
      if (!item.productId) {
        message.error('Please select a product for all items')
        return
      }
      if (item.quantity <= 0) {
        message.error('Quantity must be greater than zero')
        return
      }
      if (item.purchasePrice <= 0) {
        message.error('Purchase price must be greater than zero')
        return
      }
    }

    setIsSubmitting(true)

    const formValues = form.getFieldsValue()
    const paymentDetails = paymentSource
      ? {
          source: paymentSource,
          bankId: paymentSource === CashLocation.BANK ? selectedBank : undefined
        }
      : undefined

    const returnData: ProcessVendorReturnParams = {
      vendorId: selectedVendor,
      items: items.map(({ key, ...rest }) => rest), // Remove the key property
      adminId: user?.id || '',
      description: formValues.description,
      paymentDetails
    }

    const response = await vendorReturnApi.processVendorReturn(returnData)

    setIsSubmitting(false)

    if (response.error.error || response.data.error) {
      message.error(response.error.message || response.data.error.message)
      return
    }
    message.success('Vendor return processed successfully')
    resetForm()
    setRefreshTrigger((prev) => prev + 1)
  }

  const resetForm = () => {
    form.resetFields()
    setItems([])
    setSelectedVendor(null)
    setPaymentSource(null)
    setSelectedBank(undefined)
    setTotalAmount(0)
  }

  const columns = [
    {
      title: 'Product',
      dataIndex: 'productId',
      key: 'productId',
      render: (productId: string, record: VendorReturnFormItem) => (
        <Select
          showSearch
          placeholder="Select a product"
          optionFilterProp="children"
          value={productId || undefined}
          onChange={(value) => handleProductChange(value, record.key)}
          style={{ width: '100%' }}
        >
          {products.map((product) => (
            <Option key={product.value} value={product.value}>
              {product.label}
            </Option>
          ))}
        </Select>
      )
    },
    {
      title: 'Quantity',
      dataIndex: 'quantity',
      key: 'quantity',
      render: (quantity: number, record: VendorReturnFormItem) => (
        <InputNumber
          min={1}
          value={quantity}
          onChange={(value) => handleQuantityChange(value, record.key)}
          style={{ width: '100%' }}
        />
      )
    },
    {
      title: 'Purchase Price',
      dataIndex: 'purchasePrice',
      key: 'purchasePrice',
      render: (price: number, record: VendorReturnFormItem) => (
        <InputNumber
          min={0}
          step={0.01}
          value={price}
          onChange={(value) => handlePriceChange(value, record.key)}
          prefix="$"
          style={{ width: '100%' }}
        />
      )
    },
    {
      title: 'Total',
      key: 'total',
      render: (_: any, record: VendorReturnFormItem) => (
        <span>${(record.quantity * record.purchasePrice).toFixed(2)}</span>
      )
    },
    {
      title: 'Action',
      key: 'action',
      render: (_: any, record: VendorReturnFormItem) => (
        <Button
          type="text"
          danger
          icon={<MinusCircleOutlined />}
          onClick={() => removeItem(record.key)}
        />
      )
    }
  ]

  return (
    <div className="space-y-6">
      <Card>
        <Form form={form} layout="vertical">
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
            <Form.Item
              label="Vendor"
              name="vendorId"
              rules={[{ required: true, message: 'Please select a vendor' }]}
            >
              <Select
                showSearch
                placeholder="Select a vendor"
                optionFilterProp="children"
                value={selectedVendor || undefined}
                onChange={setSelectedVendor}
                style={{ width: '100%' }}
                options={vendors}
              />
            </Form.Item>

            <Form.Item label="Description" name="description">
              <Input.TextArea rows={1} placeholder="Enter description (optional)" />
            </Form.Item>
          </div>

          <Divider orientation="left">Return Items</Divider>

          <div className="mb-4">
            <Button
              type="dashed"
              onClick={addItem}
              icon={<PlusOutlined />}
              className="w-full md:w-auto"
            >
              Add Item
            </Button>
          </div>

          <Table
            columns={columns}
            dataSource={items}
            rowKey="key"
            pagination={false}
            locale={{ emptyText: 'No items added yet' }}
            className="mb-6"
          />

          <div className="flex flex-col items-end">
            <div className="mb-4 text-right">
              <Text strong className="mr-2">
                Total Amount:
              </Text>
              <Text strong className="text-xl text-blue-600">
                ${totalAmount.toFixed(2)}
              </Text>
            </div>

            <Divider orientation="left">Payment Details (Optional)</Divider>

            <div className="grid w-full grid-cols-1 gap-4 md:grid-cols-2">
              <Form.Item label="Payment Source">
                <Select
                  placeholder="Select payment source"
                  value={paymentSource || undefined}
                  onChange={setPaymentSource}
                  allowClear
                  style={{ width: '100%' }}
                >
                  <Option value={CashLocation.SMALL_COUNTER}>
                    <DollarOutlined className="mr-2" />
                    Small Counter
                  </Option>
                  <Option value={CashLocation.CASH_VAULT}>
                    <ShopOutlined className="mr-2" />
                    Cash Vault
                  </Option>
                  <Option value={CashLocation.BANK}>
                    <BankOutlined className="mr-2" />
                    Bank
                  </Option>
                </Select>
              </Form.Item>

              {paymentSource === CashLocation.BANK && (
                <Form.Item
                  label="Bank"
                  rules={[{ required: true, message: 'Please select a bank' }]}
                >
                  <Select
                    placeholder="Select bank"
                    value={selectedBank}
                    onChange={setSelectedBank}
                    style={{ width: '100%' }}
                    options={banks}
                  />
                </Form.Item>
              )}
            </div>
          </div>

          <div className="mt-6 flex justify-end">
            <Space>
              <Button onClick={resetForm}>Reset</Button>
              <Button
                type="primary"
                icon={<SaveOutlined />}
                onClick={handleSubmit}
                loading={isSubmitting}
                disabled={items.length === 0 || !selectedVendor}
              >
                Process Return
              </Button>
            </Space>
          </div>
        </Form>
      </Card>
    </div>
  )
}

export default AddVendorReturn
