import { useState, useEffect } from 'react'
import {
  Form,
  Input,
  InputNumber,
  Button,
  Space,
  App,
  Select,
  Card,
  Divider,
  Table,
  Tag,
  Typography,
  Tooltip,
  DatePicker
} from 'antd'
import { FaPlus, FaSave } from 'react-icons/fa'
import { MdOutlineHistory } from 'react-icons/md'
import type { ColumnsType } from 'antd/es/table'
import { stockReturnApi } from '@/renderer/services'
import { useSelector } from 'react-redux'
import { IRootState } from '@/renderer/redux'
import { usePartyContext, useBankContext, useProductContext } from '@/renderer/contexts'
import {
  DollarCircleOutlined,
  DeleteOutlined,
  BarcodeOutlined,
  TagsOutlined
} from '@ant-design/icons'
import dayjs from 'dayjs'

const { Text } = Typography

interface LegacyReturnItem {
  key: string
  productId: string
  product: {
    name: string
    productId: string
  }
  quantity: number
  purchasePrice: number
}

interface AddLegacyStockReturnProps {
  setRefreshTrigger: (trigger: any) => void
}

// ProductForm component to handle adding products
const ProductForm = ({ form, products, productsLoading, onAddItem }) => {
  // Update productId when product is selected
  const handleProductChange = (value: string, option: any) => {
    if (option) {
      form.setFieldsValue({
        productId: option.data?.productId || value
      })
    }
  }

  return (
    <>
      <Divider orientation="left">Product Details</Divider>
      <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
        <Form.Item name="product" label="Product" rules={[{ required: false }]}>
          <Select
            placeholder="Select product"
            options={products}
            showSearch
            optionFilterProp="label"
            loading={productsLoading}
            onChange={handleProductChange}
          />
        </Form.Item>

        <Form.Item name="productId" label="Product ID" rules={[{ required: false }]}>
          <Input disabled placeholder="Product ID (auto-filled)" />
        </Form.Item>

        <Form.Item name="quantity" label="Return Quantity" rules={[{ required: false }]}>
          <InputNumber min={0.01} placeholder="Quantity" style={{ width: '100%' }} />
        </Form.Item>

        <Form.Item name="purchasePrice" label="Return Price" rules={[{ required: false }]}>
          <InputNumber
            min={0.01}
            placeholder="Return Price"
            style={{ width: '100%' }}
            prefix="Rs."
          />
        </Form.Item>
      </div>

      <div className="mt-4 flex justify-end">
        <Button type="dashed" icon={<FaPlus />} onClick={onAddItem} className="bg-indigo-50">
          Add Item
        </Button>
      </div>
    </>
  )
}

// ReturnItemsTable component to display items to be returned
const ReturnItemsTable = ({ items, removeItem, totalReturnAmount }) => {
  const columns: ColumnsType<LegacyReturnItem> = [
    {
      title: 'Product Details',
      dataIndex: 'product',
      key: 'product',
      render: (product) => (
        <Space direction="vertical" size="small">
          <Space wrap>
            <Tag>{product.name}</Tag>
            <Tooltip title="Product ID">
              <Tag color="blue" icon={<BarcodeOutlined />}>
                {product.productId}
              </Tag>
            </Tooltip>
          </Space>
        </Space>
      )
    },
    {
      title: 'Quantity',
      dataIndex: 'quantity',
      key: 'quantity',
      width: 120
    },
    {
      title: 'Purchase Price',
      dataIndex: 'purchasePrice',
      key: 'purchasePrice',
      width: 150,
      render: (price: number) => `Rs. ${price.toFixed(2)}`
    },
    {
      title: 'Total',
      key: 'total',
      width: 150,
      render: (_, record) => `Rs. ${(record.quantity * record.purchasePrice).toFixed(2)}`
    },
    {
      title: 'Action',
      key: 'action',
      width: 100,
      render: (_, record) => (
        <Button
          danger
          icon={<DeleteOutlined />}
          onClick={() => removeItem(record.key)}
          size="small"
        />
      )
    }
  ]

  return (
    <Card className="shadow-md">
      <Typography.Title level={5}>Return Items</Typography.Title>
      <Table virtual sticky columns={columns} dataSource={items} rowKey="key" pagination={false} />
      <Divider />
      <div className="mb-4">
        <Typography.Text strong>Total Return Amount: </Typography.Text>
        <Typography.Text className="text-lg">Rs. {totalReturnAmount.toFixed(2)}</Typography.Text>
      </div>
    </Card>
  )
}

// Main component
const AddLegacyStockReturn = ({ setRefreshTrigger }: AddLegacyStockReturnProps) => {
  const { message } = App.useApp()
  const [form] = Form.useForm()
  const [loading, setLoading] = useState(false)
  const [items, setItems] = useState<LegacyReturnItem[]>([])
  const [totalReturnAmount, setTotalReturnAmount] = useState(0)
  const { banks } = useBankContext()
  const { customers } = usePartyContext()
  const { products, loading: productsLoading } = useProductContext()

  const user = useSelector((state: IRootState) => state.user.data)

  const removeItem = (key: string) => {
    const newItems = items.filter((item) => item.key !== key)
    setItems(newItems)
    calculateTotal(newItems)
  }

  const calculateTotal = (itemsList: LegacyReturnItem[]) => {
    const total = itemsList.reduce((sum, item) => sum + item.quantity * item.purchasePrice, 0)
    setTotalReturnAmount(total)
  }

  const handleAddItem = () => {
    const productId = form.getFieldValue('productId')
    const productValue = form.getFieldValue('product')
    const quantity = form.getFieldValue('quantity')
    const purchasePrice = form.getFieldValue('purchasePrice')

    if (!productId || !productValue || !quantity || !purchasePrice) {
      message.error('Please fill in all product details')
      return
    }

    // Find the product details
    const selectedProduct = products.find((p) => p.value === productValue)
    if (!selectedProduct) {
      message.error('Invalid product selected')
      return
    }

    const newItem: LegacyReturnItem = {
      key: Date.now().toString(),
      productId,
      product: {
        name: selectedProduct.label as string,
        productId
      },
      quantity,
      purchasePrice
    }

    const newItems = [...items, newItem]
    setItems(newItems)
    calculateTotal(newItems)

    // Reset product form fields
    form.setFieldsValue({
      productId: undefined,
      product: undefined,
      quantity: undefined,
      purchasePrice: undefined
    })
  }

  const handleSubmit = async () => {
    try {
      if (items.length === 0) {
        message.error('Please add at least one item to return')
        return
      }

      await form.validateFields()
      const values = form.getFieldsValue()
      const customerType = values.customerType

      // Check if payment details are required (for walk-in customers)
      if (customerType === 'WALK_IN' && !values.source) {
        message.error('Please select payment source for walk-in customer')
        return
      }

      if (values.source === 'BANK' && !values.bankId) {
        message.error('Please select bank for bank payment')
        return
      }

      // Check approverNote exists and has minimum length
      if (!values.approverNote) {
        message.error('Please provide an approval note/reason for accepting legacy return')
        return
      }

      if (values.approverNote.trim().length < 10) {
        message.error('Approval note must be at least 10 characters')
        return
      }

      setLoading(true)

      const requestData = {
        items: items.map((item) => ({
          productId: item.productId,
          quantity: item.quantity,
          purchasePrice: item.purchasePrice
        })),
        customerType,
        customerId: customerType === 'REGISTERED' ? values.customerId : undefined,
        customerName: customerType === 'WALK_IN' ? values.customerName : undefined,
        adminId: user?.id || '',
        approverNote: values.approverNote,
        returnDate: values.returnDate ? values.returnDate.toDate() : undefined,
        paymentDetails: values.source
          ? {
              source: values.source,
              bankId: values.source === 'BANK' ? values.bankId : undefined
            }
          : undefined
      }

      const response = await stockReturnApi.processLegacyStockReturn(requestData)

      setLoading(false)
      if (response.error.error || response.data.error) {
        message.error(response.error.message || response.data.error.message)
        return
      }

      message.success('Legacy stock return processed successfully')
      setItems([])
      setTotalReturnAmount(0)
      form.resetFields()
      setRefreshTrigger((prev: number) => prev + 1)
    } catch (error: any) {
      message.error(error.message)
      setLoading(false)
    }
  }

  return (
    <div className="space-y-6">
      <Card className="shadow-md">
        <div className="mb-4 flex items-center gap-2">
          <MdOutlineHistory className="text-2xl text-indigo-600" />
          <span className="text-lg font-semibold">Legacy Stock Return</span>
        </div>
        <Form form={form} layout="vertical">
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
            <Form.Item
              name="customerType"
              label="Customer Type"
              rules={[{ required: true, message: 'Please select customer type' }]}
            >
              <Select
                placeholder="Select Customer Type"
                options={[
                  { label: 'Registered Customer', value: 'REGISTERED' },
                  { label: 'Walk-in Customer', value: 'WALK_IN' }
                ]}
              />
            </Form.Item>

            <Form.Item
              noStyle
              shouldUpdate={(prevValues, currentValues) =>
                prevValues?.customerType !== currentValues?.customerType
              }
            >
              {({ getFieldValue }) =>
                getFieldValue('customerType') === 'REGISTERED' ? (
                  <Form.Item
                    name="customerId"
                    label="Select Customer"
                    rules={[{ required: true, message: 'Please select customer' }]}
                  >
                    <Select
                      placeholder="Select customer"
                      options={customers}
                      showSearch
                      filterOption={(input, option) =>
                        (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                      }
                    />
                  </Form.Item>
                ) : getFieldValue('customerType') === 'WALK_IN' ? (
                  <Form.Item name="customerName" label="Customer Name (Optional)">
                    <Input placeholder="Enter customer name (optional)" />
                  </Form.Item>
                ) : null
              }
            </Form.Item>

            <Form.Item name="returnDate" label="Return Date">
              <DatePicker
                style={{ width: '100%' }}
                placeholder="Select return date"
                disabledDate={(current) => current && current > dayjs().endOf('day')}
              />
            </Form.Item>
          </div>

          <ProductForm
            form={form}
            products={products}
            productsLoading={productsLoading}
            onAddItem={handleAddItem}
          />
        </Form>
      </Card>

      {items.length > 0 && (
        <>
          <ReturnItemsTable
            items={items}
            removeItem={removeItem}
            totalReturnAmount={totalReturnAmount}
          />

          <Card className="shadow-md">
            <Form form={form} layout="vertical" className="grid grid-cols-1 gap-4 md:grid-cols-2">
              <div className="col-span-2 mb-4 flex items-center gap-2">
                <DollarCircleOutlined className="text-2xl text-indigo-600" />
                <span className="text-lg font-semibold">Payment Details</span>
              </div>

              <div className="col-span-2 mb-4">
                <Typography.Text strong>Total Return Amount: </Typography.Text>
                <Typography.Text className="text-lg">
                  Rs. {totalReturnAmount.toFixed(2)}
                </Typography.Text>
              </div>

              <div className="col-span-2 mb-4">
                <Typography.Text strong>Payment: </Typography.Text>
                <Typography.Text className="ml-2">
                  {form.getFieldValue('customerType') === 'WALK_IN'
                    ? 'Required for walk-in customers'
                    : 'Optional for registered customers'}
                </Typography.Text>
              </div>

              <Form.Item
                name="source"
                label="Payment Source"
                rules={[
                  {
                    required: form.getFieldValue('customerType') === 'WALK_IN',
                    message: 'Please select payment source'
                  }
                ]}
              >
                <Select
                  placeholder="Select payment source"
                  options={[
                    { label: 'Small Counter', value: 'SMALL_COUNTER' },
                    { label: 'Cash Vault', value: 'CASH_VAULT' },
                    { label: 'Bank', value: 'BANK' }
                  ]}
                />
              </Form.Item>

              <Form.Item
                noStyle
                shouldUpdate={(prevValues, currentValues) =>
                  prevValues?.source !== currentValues?.source
                }
              >
                {({ getFieldValue }) =>
                  getFieldValue('source') === 'BANK' ? (
                    <Form.Item
                      name="bankId"
                      label="Bank"
                      rules={[{ required: true, message: 'Please select bank' }]}
                    >
                      <Select placeholder="Select bank" options={banks} />
                    </Form.Item>
                  ) : null
                }
              </Form.Item>

              <Form.Item
                name="approverNote"
                label="Approval Reason/Note"
                className="col-span-2"
                rules={[
                  { required: true, message: 'Please provide approval reason' },
                  { min: 10, message: 'Reason must be at least 10 characters' }
                ]}
              >
                <Input.TextArea
                  rows={4}
                  placeholder="Provide detailed reason for accepting this legacy return (e.g., proof of purchase, management approval, etc.)"
                />
              </Form.Item>
            </Form>

            <div className="mt-4 flex justify-end">
              <Button
                type="primary"
                icon={<FaSave />}
                onClick={handleSubmit}
                loading={loading}
                disabled={items.length === 0}
              >
                Process Legacy Return
              </Button>
            </div>
          </Card>
        </>
      )}
    </div>
  )
}

export default AddLegacyStockReturn
