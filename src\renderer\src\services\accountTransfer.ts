import { http } from './http';
import { Channels } from '@/common/constants';
import {
    CreateAccountTransferData,
    GetAccountTransfersParams,
    VoidAccountTransferParams,
    AccountTransferStatus,
    AccountTransferSortOrder
} from '@/common/types/accountTransfer';

export const createAccountTransfer = async (data: CreateAccountTransferData) => {
    return await http.post(Channels.CREATE_ACCOUNT_TRANSFER, {
        body: data
    });
};

export const getAccountTransfers = async (params: GetAccountTransfersParams) => {
    return await http.get(Channels.GET_ACCOUNT_TRANSFERS, {
        body: params
    });
};

export const getAccountTransferById = async (id: string) => {
    return await http.get(Channels.GET_ACCOUNT_TRANSFER_BY_ID, {
        params: { id }
    });
};

export const voidAccountTransfer = async (data: VoidAccountTransferParams) => {
    return await http.post(Channels.VOID_ACCOUNT_TRANSFER, {
        body: data
    });
};

// Export enums for frontend use
export { AccountTransferStatus, AccountTransferSortOrder };