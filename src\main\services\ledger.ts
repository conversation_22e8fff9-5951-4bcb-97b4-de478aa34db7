import { prisma } from '../db';
import { DailyLedgerResponse, GetDailyLedgerParams, GetLedgerParams, PaginatedLedgerResponse } from '../../common/types/ledger';

class LedgerService {
    async getPaginatedLedgerEntries(partyId: string, params: GetLedgerParams): Promise<PaginatedLedgerResponse> {
        // Validate input parameters
        if (!partyId) throw new Error("Party ID is required");
        if (params.page && params.page < 1) throw new Error("Page must be greater than 0");
        if (params.limit && params.limit < 1) throw new Error("Limit must be greater than 0");

        const page = params.page || 1;
        const limit = params.limit || 50;
        const skip = (page - 1) * limit;

        // Build the date filter if either startDate or endDate is provided
        let dateFilter = {};
        let initialBalanceDateFilter = {};
        if (params.startDate || params.endDate) {
            // Handle timezone issues by setting time to start/end of day
            let startDateWithTime = params.startDate ? new Date(params.startDate) : undefined;
            let endDateWithTime = params.endDate ? new Date(params.endDate) : undefined;

            // If we have a start date, set it to the beginning of the day (00:00:00)
            if (startDateWithTime) {
                startDateWithTime.setHours(0, 0, 0, 0);

                // For initial balance calculation, we need all entries before the start date
                initialBalanceDateFilter = {
                    date: {
                        lt: startDateWithTime
                    }
                };
            }

            // If we have an end date, set it to the end of the day (23:59:59.999)
            if (endDateWithTime) {
                endDateWithTime.setHours(23, 59, 59, 999);
            }

            dateFilter = {
                date: {
                    ...(startDateWithTime && { gte: startDateWithTime }),
                    ...(endDateWithTime && { lte: endDateWithTime })
                }
            };
        }

        const where = {
            partyId,
            status: 'ACTIVE' as const,
            ...dateFilter
        };

        // Calculate initial balance from all entries before the date range
        let initialBalance = 0;
        if (params.startDate) {
            const [creditsBefore, debitsBefore] = await Promise.all([
                // Sum all CREDIT entries before the start date
                prisma.ledger.aggregate({
                    where: {
                        partyId,
                        status: 'ACTIVE',
                        creditOrDebit: 'CREDIT',
                        ...initialBalanceDateFilter
                    },
                    _sum: { amount: true }
                }),

                // Sum all DEBIT entries before the start date
                prisma.ledger.aggregate({
                    where: {
                        partyId,
                        status: 'ACTIVE',
                        creditOrDebit: 'DEBIT',
                        ...initialBalanceDateFilter
                    },
                    _sum: { amount: true }
                })
            ]);

            // Calculate initial balance: credits - debits
            const creditSum = creditsBefore._sum.amount || 0;
            const debitSum = debitsBefore._sum.amount || 0;
            initialBalance = creditSum - debitSum;
        }

        // Use Promise.all to run these queries concurrently for better performance
        const [entries, total, overallTotals] = await Promise.all([
            // Get entries for current page, sorting by date in ascending order (oldest first)
            prisma.ledger.findMany({
                where,
                orderBy: { date: 'asc' },
                include: {
                    createdBy: {
                        select: {
                            name: true
                        }
                    }
                },
                skip,
                take: limit
            }),

            // Get total count for pagination
            prisma.ledger.count({ where }),

            // Calculate totals for the entire ledger (not just current page)
            Promise.all([
                // Total credits
                prisma.ledger.aggregate({
                    where: {
                        partyId,
                        status: 'ACTIVE',
                        creditOrDebit: 'CREDIT',
                        ...dateFilter
                    },
                    _sum: { amount: true }
                }),

                // Total debits
                prisma.ledger.aggregate({
                    where: {
                        partyId,
                        status: 'ACTIVE',
                        creditOrDebit: 'DEBIT',
                        ...dateFilter
                    },
                    _sum: { amount: true }
                })
            ])
        ]);

        let runningBalance = initialBalance;

        // For paginated results, we need to calculate the balance of entries before the current page
        if (entries.length > 0 && page > 1) {
            // Get the first entry of this page to use its date as cutoff
            const firstEntryDate = entries[0].date;

            // Use Promise.all to run these queries concurrently
            const [creditsBefore, debitsBefore] = await Promise.all([
                // Sum all CREDIT entries before the first entry of this page but within the date range
                prisma.ledger.aggregate({
                    where: {
                        partyId,
                        status: 'ACTIVE',
                        creditOrDebit: 'CREDIT',
                        date: { lt: firstEntryDate },
                        ...dateFilter
                    },
                    _sum: { amount: true }
                }),

                // Sum all DEBIT entries before the first entry of this page but within the date range
                prisma.ledger.aggregate({
                    where: {
                        partyId,
                        status: 'ACTIVE',
                        creditOrDebit: 'DEBIT',
                        date: { lt: firstEntryDate },
                        ...dateFilter
                    },
                    _sum: { amount: true }
                })
            ]);

            // Add to our initial balance
            const creditSum = creditsBefore._sum.amount || 0;
            const debitSum = debitsBefore._sum.amount || 0;
            runningBalance += (creditSum - debitSum);
        }

        // Calculate running balances for entries on this page
        const entriesWithBalance = entries.map(entry => {
            // Update running balance based on entry type
            if (entry.creditOrDebit === 'CREDIT') {
                runningBalance += entry.amount;
            } else { // DEBIT
                runningBalance -= entry.amount;
            }

            // Return entry with the current balance (AFTER applying this entry's effect)
            return { ...entry, balance: runningBalance };
        });

        // Calculate totals for the current page
        const pageTotals = entriesWithBalance.reduce(
            (acc, entry) => {
                if (entry.creditOrDebit === 'CREDIT') {
                    acc.credits += entry.amount;
                    acc.net += entry.amount;
                } else {
                    acc.debits += entry.amount;
                    acc.net -= entry.amount;
                }
                return acc;
            },
            { credits: 0, debits: 0, net: 0 }
        );

        // Get the overall totals from the aggregation queries
        const overallCredits = overallTotals[0]._sum.amount || 0;
        const overallDebits = overallTotals[1]._sum.amount || 0;
        const filteredNet = overallCredits - overallDebits;

        // Overall balance is the initial balance plus the net of filtered entries
        const overallBalance = initialBalance + filteredNet;

        return {
            entries: entriesWithBalance,
            pagination: {
                total,
                page,
                limit,
                hasMore: total > (skip + limit)
            },
            totals: {
                credits: pageTotals.credits,
                debits: pageTotals.debits,
                net: pageTotals.net,
                overall: {
                    credits: overallCredits,
                    debits: overallDebits,
                    net: filteredNet,
                    balance: overallBalance
                }
            }
        };
    }

    async getDailyLedger(params: GetDailyLedgerParams): Promise<DailyLedgerResponse> {
        const { date } = params;
        if (!date) throw new Error("Date is required");

        const startOfDay = new Date(date);
        startOfDay.setHours(0, 0, 0, 0);
        const endOfDay = new Date(date);
        endOfDay.setHours(23, 59, 59, 999);

        const entries = await prisma.ledger.findMany({
            where: {
                date: {
                    gte: startOfDay,
                    lte: endOfDay
                },
                status: 'ACTIVE' as const
            },
            include: {
                createdBy: {
                    select: {
                        name: true
                    }
                }
            },
            orderBy: { date: 'asc' }
        });

        // Calculate running balance for each entry
        let runningBalance = 0;
        const entriesWithBalance = entries.map((entry) => {
            if (entry.creditOrDebit === 'CREDIT') {
                runningBalance += entry.amount;
            } else {
                runningBalance -= entry.amount;
            }
            return { ...entry, balance: runningBalance };
        });

        // Calculate totals
        const totals = entries.reduce(
            (acc, entry) => {
                if (entry.creditOrDebit === 'CREDIT') {
                    acc.credits += entry.amount;
                    acc.net += entry.amount;
                } else {
                    acc.debits += entry.amount;
                    acc.net -= entry.amount;
                }
                return acc;
            },
            { credits: 0, debits: 0, net: 0 }
        );

        return {
            date,
            entries: entriesWithBalance,
            totals
        };
    }

    async getLedgerEntriesForPDF(partyId: string) {
        return await prisma.$transaction(async (tx) => {
            // First find the party
            const party = await tx.party.findUnique({
                where: { id: partyId }
            });
            if (!party) throw new Error("Party not found");

            // Get all ledger entries
            const entries = await tx.ledger.findMany({
                where: {
                    partyId,
                    status: 'ACTIVE'
                },
                select: {
                    id: true,
                    date: true,
                    description: true,
                    creditOrDebit: true,
                    amount: true,
                    referenceType: true,
                },
                orderBy: { date: 'asc' }
            });

            // Calculate running balance and totals starting from first entry
            let runningBalance = 0;
            let totalCredits = 0;
            let totalDebits = 0;

            const entriesWithBalance = entries.map((entry, index) => {
                if (entry.creditOrDebit === 'CREDIT') {
                    runningBalance += entry.amount;
                    totalCredits += entry.amount;
                } else {
                    runningBalance -= entry.amount;
                    totalDebits += entry.amount;
                }

                return {
                    ...entry,
                    serialNumber: index + 1,
                    balance: runningBalance
                };
            });

            return {
                entries: entriesWithBalance,
                party,
                totals: {
                    credits: totalCredits,
                    debits: totalDebits,
                    net: totalCredits - totalDebits
                }
            };
        });
    }
}

/*
 * Note on inconsistent balance calculation logic:
 * 
 * There are three different methods for calculating balances in this service:
 * 
 * 1. getPaginatedLedgerEntries: Starts with the current balance from the party record and works backwards,
 *    adjusting the balance for each entry in reverse chronological order (newest to oldest).
 *    This is appropriate for paginated views where we need to show the most recent entries first.
 * 
 * 2. getDailyLedger: Starts with a balance of 0 at the beginning of the day and calculates
 *    a running balance throughout the day in chronological order (oldest to newest).
 *    This doesn't reflect the actual balance at the start of the day, just the net change during the day.
 * 
 * 3. getLedgerEntriesForPDF: Similar to getDailyLedger but for all entries, starting from 0
 *    and calculating a running balance in chronological order.
 * 
 * These inconsistencies could lead to confusion when comparing balances across different views.
 * A more consistent approach would be to always calculate the actual balance at the start of the period
 * being viewed (by querying all entries before that point) and then calculate the running balance from there.
 */

export const ledgerService = new LedgerService();








