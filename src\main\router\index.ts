import { ipcMain } from "electron";
import { Channels } from "../../common";
import { catchIpcHandler } from "./handler";
import { bankController, smallCounterController, cashVaultController, categoryController, expenseController, paymentController, productController, purchaseInvoiceController, saleInvoiceController, stockController, userController, backupController, openingStockController, stockReturnController, dashboardController, vendorReturnController, reportsController, manualEntryController } from "../controllers";
import { partyController } from "../controllers/party";
import { ledgerController } from "../controllers/ledger";
import { licenseController } from "../controllers/license";
import { resetController } from "../controllers/reset";
import { invoiceNumberController } from "../controllers/invoiceNumber";
import { bankLedgerController } from "../controllers/bankLedger";
import { accountTransferController } from "../controllers/accountTransfer";

// Product APIs
ipcMain.handle(`POST:${Channels.CREATE_PRODUCT}`, catchIpcHandler(productController.createProduct));
ipcMain.handle(`GET:${Channels.GET_PRODUCT}`, catchIpcHandler(productController.getProduct));
ipcMain.handle(`PUT:${Channels.UPDATE_PRODUCT}`, catchIpcHandler(productController.updateProduct));
ipcMain.handle(`DELETE:${Channels.DELETE_PRODUCT}`, catchIpcHandler(productController.deleteProduct));
ipcMain.handle(`GET:${Channels.GET_PRODUCTS}`, catchIpcHandler(productController.getProducts));
ipcMain.handle(`GET:${Channels.GET_PRODUCTS_SELECT}`, catchIpcHandler(productController.getProductsForSelect));
ipcMain.handle(`GET:${Channels.GET_PRODUCT_QUANTITY_REPORT}`, catchIpcHandler(productController.getProductQuantityReport));

// Party APIs
ipcMain.handle(`POST:${Channels.CREATE_PARTY}`, catchIpcHandler(partyController.createParty));
ipcMain.handle(`GET:${Channels.GET_PARTY}`, catchIpcHandler(partyController.getParty));
ipcMain.handle(`PUT:${Channels.UPDATE_PARTY}`, catchIpcHandler(partyController.updateParty));
ipcMain.handle(`DELETE:${Channels.DELETE_PARTY}`, catchIpcHandler(partyController.deleteParty));
ipcMain.handle(`GET:${Channels.GET_PARTIES}`, catchIpcHandler(partyController.getParties));
ipcMain.handle(`GET:${Channels.GET_PARTIES_BY_TYPE_FOR_PDF}`, catchIpcHandler(partyController.getPartiesByTypeForPDF));
ipcMain.handle(`GET:${Channels.GET_VENDORS_SELECT}`, catchIpcHandler(partyController.getPartiesForSelect));
ipcMain.handle(`GET:${Channels.GET_CUSTOMERS_SELECT}`, catchIpcHandler(partyController.getPartiesForSelect));
ipcMain.handle(`GET:${Channels.GET_CREDITORS_SELECT}`, catchIpcHandler(partyController.getPartiesForSelect));
ipcMain.handle(`GET:${Channels.GET_ALL_PARTIES_SELECT}`, catchIpcHandler(partyController.getPartiesForSelect));
ipcMain.handle(`GET:${Channels.GET_DEBTORS}`, catchIpcHandler(partyController.getPartiesByBalance));
ipcMain.handle(`GET:${Channels.GET_CREDITORS}`, catchIpcHandler(partyController.getPartiesByBalance));

// Expense APIs
ipcMain.handle(`POST:${Channels.CREATE_EXPENSE}`, catchIpcHandler(expenseController.createExpense));
ipcMain.handle(`DELETE:${Channels.DELETE_EXPENSE}`, catchIpcHandler(expenseController.deleteExpense));
ipcMain.handle(`GET:${Channels.GET_EXPENSE}`, catchIpcHandler(expenseController.getExpense));
ipcMain.handle(`GET:${Channels.GET_EXPENSES}`, catchIpcHandler(expenseController.getExpenses));
// ipcMain.handle(`GET:${Channels.GET_EXPENSES_BY_DAY}`, catchIpcHandler(expenseController.getExpensesByDay));
// ipcMain.handle(`GET:${Channels.GET_EXPENSES_BY_DATE_RANGE}`, catchIpcHandler(expenseController.getExpensesByDateRange));

// Purchase Invoice APIs
ipcMain.handle(`POST:${Channels.CREATE_PURCHASE_INVOICE}`, catchIpcHandler(purchaseInvoiceController.createPurchaseInvoice));
ipcMain.handle(`POST:${Channels.VOID_PURCHASE_INVOICE}`, catchIpcHandler(purchaseInvoiceController.voidPurchaseInvoice));
ipcMain.handle(`GET:${Channels.GET_PURCHASE_INVOICE}`, catchIpcHandler(purchaseInvoiceController.getPurchaseInvoiceById));
ipcMain.handle(`GET:${Channels.GET_ALL_PURCHASE_INVOICES}`, catchIpcHandler(purchaseInvoiceController.getAllPurchaseInvoices));
ipcMain.handle(`GET:${Channels.GET_PURCHASE_INVOICES_BY_VENDOR}`, catchIpcHandler(purchaseInvoiceController.getPurchaseInvoicesByVendor));

// Stock APIs
ipcMain.handle(`GET:${Channels.GET_AVAILABLE_STOCK}`, catchIpcHandler(stockController.getAvailableStock));
ipcMain.handle(`GET:${Channels.GET_SOLD_STOCK}`, catchIpcHandler(stockController.getSoldStock));
ipcMain.handle(`GET:${Channels.GET_STOCK_BY_PRODUCT}`, catchIpcHandler(stockController.getStockByProduct));
ipcMain.handle(`GET:${Channels.GET_STOCK_BY_VENDOR}`, catchIpcHandler(stockController.getStockByVendor));
ipcMain.handle(`GET:${Channels.GET_STOCK_BY_PURCHASE_INVOICE}`, catchIpcHandler(stockController.getStockByPurchaseInvoice));

// Ledger APIs
ipcMain.handle(`GET:${Channels.GET_BY_PARTY}`, catchIpcHandler(ledgerController.getLedgerEntriesByParty));
ipcMain.handle(`GET:${Channels.GET_DAILY}`, catchIpcHandler(ledgerController.getDailyLedgerEntries));
ipcMain.handle(`GET:${Channels.GET_LEDGER_ENTRIES_FOR_PDF}`, catchIpcHandler(ledgerController.getLedgerEntriesForPDF));

// Bank APIs
ipcMain.handle(`GET:${Channels.GET_ALL_BANKS}`, catchIpcHandler(bankController.getAllBanks));
ipcMain.handle(`GET:${Channels.GET_BANKS_FOR_SELECT}`, catchIpcHandler(bankController.getBanksForSelect));
ipcMain.handle(`POST:${Channels.CREATE_BANK}`, catchIpcHandler(bankController.createBank));
ipcMain.handle(`GET:${Channels.GET_BANK_BY_ID}`, catchIpcHandler(bankController.getBankById));
ipcMain.handle(`DELETE:${Channels.DELETE_BANK}`, catchIpcHandler(bankController.deleteBank));
ipcMain.handle(`POST:${Channels.DEACTIVATE_BANK}`, catchIpcHandler(bankController.deactivateBank));
ipcMain.handle(`POST:${Channels.REACTIVATE_BANK}`, catchIpcHandler(bankController.reactivateBank));

// Bank Ledger APIs
ipcMain.handle(`GET:${Channels.GET_BANK_LEDGER_ENTRIES}`, catchIpcHandler(bankLedgerController.getBankLedgerEntries));
ipcMain.handle(`GET:${Channels.GET_BANK_TRANSACTION_SUMMARY}`, catchIpcHandler(bankLedgerController.getBankTransactionSummary));
ipcMain.handle(`GET:${Channels.GET_DAILY_BANK_LEDGER}`, catchIpcHandler(bankLedgerController.getDailyBankLedger));
ipcMain.handle(`GET:${Channels.RECONCILE_BANK}`, catchIpcHandler(bankLedgerController.reconcileBank));
ipcMain.handle(`GET:${Channels.SEARCH_BANK_TRANSACTIONS}`, catchIpcHandler(bankLedgerController.searchBankTransactions));
ipcMain.handle(`GET:${Channels.GET_DAILY_CLOSING_BALANCES}`, catchIpcHandler(bankLedgerController.getDailyClosingBalances));

// Category APIs
ipcMain.handle(`POST:${Channels.CREATE_CATEGORY}`, catchIpcHandler(categoryController.createCategory));
ipcMain.handle(`GET:${Channels.GET_ALL_CATEGORIES}`, catchIpcHandler(categoryController.getAllCategories));
ipcMain.handle(`GET:${Channels.GET_CATEGORIES_FOR_SELECT}`, catchIpcHandler(categoryController.getCategoriesForSelect));
ipcMain.handle(`GET:${Channels.GENERATE_PRODUCT_ID}`, catchIpcHandler(categoryController.generateProductId));
ipcMain.handle(`DELETE:${Channels.DELETE_CATEGORY}`, catchIpcHandler(categoryController.deleteCategory));

// Cash Account APIs
ipcMain.handle(`GET:${Channels.GET_SMALL_COUNTER_BALANCE}`, catchIpcHandler(smallCounterController.getSmallCounterBalance));
ipcMain.handle(`GET:${Channels.GET_SMALL_COUNTER_TRANSACTIONS}`, catchIpcHandler(smallCounterController.getAllTransactions));
ipcMain.handle(`POST:${Channels.TRANSFER_TO_VAULT}`, catchIpcHandler(smallCounterController.transferToVault));
ipcMain.handle(`POST:${Channels.INITIALIZE_SMALL_COUNTER}`, catchIpcHandler(smallCounterController.initializeSmallCounter));
ipcMain.handle(`GET:${Channels.RECONCILE_SMALL_COUNTER}`, catchIpcHandler(smallCounterController.reconcileSmallCounter));
ipcMain.handle(`GET:${Channels.GENERATE_SMALL_COUNTER_STATEMENT}`, catchIpcHandler(smallCounterController.generateSmallCounterStatement));


// Cash Vault APIs
ipcMain.handle(`GET:${Channels.IS_VAULT_INITIALIZED}`, catchIpcHandler(cashVaultController.isInitialized));
ipcMain.handle(`GET:${Channels.GET_VAULT_BALANCE}`, catchIpcHandler(cashVaultController.getCurrentBalance));
ipcMain.handle(`POST:${Channels.INITIALIZE_VAULT}`, catchIpcHandler(cashVaultController.initializeVault));
ipcMain.handle(`POST:${Channels.DEPOSIT_TO_VAULT}`, catchIpcHandler(cashVaultController.depositToVault));
ipcMain.handle(`POST:${Channels.WITHDRAW_FROM_VAULT}`, catchIpcHandler(cashVaultController.withdrawFromVault));
ipcMain.handle(`GET:${Channels.GET_VAULT_LEDGER_ENTRIES}`, catchIpcHandler(cashVaultController.getAllTransactions));
ipcMain.handle(`GET:${Channels.RECONCILE_VAULT}`, catchIpcHandler(cashVaultController.reconcileVault));
ipcMain.handle(`GET:${Channels.GENERATE_CASH_VAULT_STATEMENT}`, catchIpcHandler(cashVaultController.generateCashVaultStatement));


// Payment APIs
ipcMain.handle(`POST:${Channels.CREATE_PAYMENT}`, catchIpcHandler(paymentController.createPayment));
ipcMain.handle(`GET:${Channels.GET_PAYMENT_BY_ID}`, catchIpcHandler(paymentController.getPaymentById));
ipcMain.handle(`GET:${Channels.GET_PAYMENTS}`, catchIpcHandler(paymentController.getPayments));
ipcMain.handle(`GET:${Channels.GET_PAYMENTS_BY_DATE_RANGE}`, catchIpcHandler(paymentController.getPaymentsByDateRange));
ipcMain.handle(`POST:${Channels.VOID_PAYMENT}`, catchIpcHandler(paymentController.voidPayment));
ipcMain.handle(`POST:${Channels.TRANSFER_BETWEEN_LOCATIONS}`, catchIpcHandler(paymentController.transferBetweenLocations));
ipcMain.handle(`POST:${Channels.TRANSFER_BETWEEN_PARTIES}`, catchIpcHandler(paymentController.transferBetweenParties));
ipcMain.handle(`GET:${Channels.GET_TRANSFERS}`, catchIpcHandler(paymentController.getTransfers));
ipcMain.handle(`POST:${Channels.VOID_TRANSFER}`, catchIpcHandler(paymentController.voidTransfer));


// Sale Invoice APIs
ipcMain.handle(`POST:${Channels.CREATE_WALK_IN_SALE}`, catchIpcHandler(saleInvoiceController.createWalkInSale));
ipcMain.handle(`POST:${Channels.CREATE_REGISTERED_SALE}`, catchIpcHandler(saleInvoiceController.createRegisteredSale));
ipcMain.handle(`POST:${Channels.VOID_WALK_IN_SALE}`, catchIpcHandler(saleInvoiceController.voidWalkInSale));
ipcMain.handle(`POST:${Channels.VOID_REGISTERED_SALE}`, catchIpcHandler(saleInvoiceController.voidRegisteredSale));
// ipcMain.handle(`GET:${Channels.GET_SALE_BY_ID}`, catchIpcHandler(saleInvoiceController.getSaleInvoiceById));
ipcMain.handle(`GET:${Channels.GET_ALL_SALES}`, catchIpcHandler(saleInvoiceController.getSaleInvoices));


// License APIs
ipcMain.handle(`GET:${Channels.GET_LICENSE}`, catchIpcHandler(licenseController.getLicense));
ipcMain.handle(`POST:${Channels.VERIFY_LICENSE}`, catchIpcHandler(licenseController.verifyLicense));

// User APIs
ipcMain.handle(`POST:${Channels.CREATE_USER}`, catchIpcHandler(userController.createUser));
ipcMain.handle(`PUT:${Channels.UPDATE_PASSWORD}`, catchIpcHandler(userController.updatePassword));
ipcMain.handle(`PUT:${Channels.RESET_PASSWORD}`, catchIpcHandler(userController.resetPassword));
ipcMain.handle(`GET:${Channels.GET_USERS}`, catchIpcHandler(userController.getUsers));
ipcMain.handle(`PUT:${Channels.DEACTIVATE_USER}`, catchIpcHandler(userController.deactivateUser));
ipcMain.handle(`POST:${Channels.LOGIN}`, catchIpcHandler(userController.login));

// Reset routes
ipcMain.handle(`POST:${Channels.CLEAR_LEDGER}`, catchIpcHandler(resetController.clearLedger))
ipcMain.handle(`POST:${Channels.CLEAR_STOCK_ENTRIES}`, catchIpcHandler(resetController.clearStockEntries))
ipcMain.handle(`POST:${Channels.CLEAR_WALK_IN_SALE_ITEMS}`, catchIpcHandler(resetController.clearWalkInSaleItems))
ipcMain.handle(`POST:${Channels.CLEAR_SALE_ITEMS}`, catchIpcHandler(resetController.clearSaleItems))
ipcMain.handle(`POST:${Channels.CLEAR_WALK_IN_SALE_INVOICES}`, catchIpcHandler(resetController.clearWalkInSaleInvoices))
ipcMain.handle(`POST:${Channels.CLEAR_SALE_INVOICES}`, catchIpcHandler(resetController.clearSaleInvoices))
ipcMain.handle(`POST:${Channels.CLEAR_PURCHASE_ITEMS}`, catchIpcHandler(resetController.clearPurchaseItems))
ipcMain.handle(`POST:${Channels.CLEAR_STOCK}`, catchIpcHandler(resetController.clearStock))
ipcMain.handle(`POST:${Channels.CLEAR_PURCHASE_INVOICES}`, catchIpcHandler(resetController.clearPurchaseInvoices))
ipcMain.handle(`POST:${Channels.CLEAR_PRODUCTS}`, catchIpcHandler(resetController.clearProducts))
ipcMain.handle(`POST:${Channels.CLEAR_CATEGORIES}`, catchIpcHandler(resetController.clearCategories))
ipcMain.handle(`POST:${Channels.CLEAR_PAYMENTS}`, catchIpcHandler(resetController.clearPayments))
ipcMain.handle(`POST:${Channels.CLEAR_BANKS}`, catchIpcHandler(resetController.clearBanks))
ipcMain.handle(`POST:${Channels.CLEAR_SMALL_COUNTER}`, catchIpcHandler(resetController.clearSmallCounter))
ipcMain.handle(`POST:${Channels.CLEAR_CASH_VAULT}`, catchIpcHandler(resetController.clearCashVault))
ipcMain.handle(`POST:${Channels.CLEAR_EXPENSES}`, catchIpcHandler(resetController.clearExpenses))
ipcMain.handle(`POST:${Channels.CLEAR_INVOICE_NUMBERS}`, catchIpcHandler(resetController.clearInvoiceNumbers))
ipcMain.handle(`POST:${Channels.CLEAR_PARTIES}`, catchIpcHandler(resetController.clearParties))
ipcMain.handle(`POST:${Channels.CLEAR_ADMINS}`, catchIpcHandler(resetController.clearAdmins))
ipcMain.handle(`POST:${Channels.CLEAR_ALL_DATA}`, catchIpcHandler(resetController.clearAllData))

// Database backup handlers
ipcMain.handle(`POST:${Channels.CREATE_BACKUP}`, catchIpcHandler(backupController.createBackup));
ipcMain.handle(`POST:${Channels.RESTORE_BACKUP}`, catchIpcHandler(backupController.restoreBackup));


// Invoice Number handlers
ipcMain.handle(`POST:${Channels.GENERATE_INVOICE_NUMBER}`, catchIpcHandler(invoiceNumberController.generateInvoiceNumber));
ipcMain.handle(`POST:${Channels.CONFIRM_INVOICE_NUMBER}`, catchIpcHandler(invoiceNumberController.confirmInvoiceNumber));
ipcMain.handle(`POST:${Channels.CANCEL_INVOICE_NUMBER}`, catchIpcHandler(invoiceNumberController.cancelInvoiceNumber));
ipcMain.handle(`POST:${Channels.CLEANUP_EXPIRED_INVOICE_NUMBERS}`, catchIpcHandler(invoiceNumberController.cleanupExpiredNumbers));


// Opening Stock handlers
ipcMain.handle(`GET:${Channels.GET_OPENING_STOCK}`, catchIpcHandler(openingStockController.getOpeningStock));
ipcMain.handle(`POST:${Channels.ADD_OPENING_STOCK}`, catchIpcHandler(openingStockController.addOpeningStock));


// Return Stock handlers
ipcMain.handle(`GET:${Channels.GET_RETURNED_STOCK}`, catchIpcHandler(stockReturnController.getStockReturn));
ipcMain.handle(`POST:${Channels.PROCESS_RETURN_STOCK}`, catchIpcHandler(stockReturnController.processStockReturn));
ipcMain.handle(`POST:${Channels.VOID_RETURN_STOCK}`, catchIpcHandler(stockReturnController.voidReturnStock));
ipcMain.handle(`GET:${Channels.GET_SALE_INVOICE_BY_NUMBER}`, catchIpcHandler(stockReturnController.getSaleInvoiceByNumber));
ipcMain.handle(`POST:${Channels.PROCESS_LEGACY_RETURN_STOCK}`, catchIpcHandler(stockReturnController.processLegacyStockReturn));

// Dashboard APIs
ipcMain.handle(`GET:${Channels.GET_DAILY_SALES_OVERVIEW}`, catchIpcHandler(dashboardController.getDailySalesOverview));
ipcMain.handle(`GET:${Channels.GET_SALES_TIMELINE}`, catchIpcHandler(dashboardController.getSalesTimeline));
ipcMain.handle(`GET:${Channels.GET_TOP_PRODUCTS}`, catchIpcHandler(dashboardController.getTopProducts));
ipcMain.handle(`GET:${Channels.GET_LOW_STOCK_PRODUCTS}`, catchIpcHandler(dashboardController.getLowStockProducts));
ipcMain.handle(`GET:${Channels.GET_STOCK_VALUE_OVERVIEW}`, catchIpcHandler(dashboardController.getStockValueOverview));
ipcMain.handle(`GET:${Channels.GET_CASH_FLOW_OVERVIEW}`, catchIpcHandler(dashboardController.getCashFlowOverview));
ipcMain.handle(`GET:${Channels.GET_PARTY_OVERVIEW}`, catchIpcHandler(dashboardController.getPartyOverview));
ipcMain.handle(`GET:${Channels.GET_SALES_DISTRIBUTION}`, catchIpcHandler(dashboardController.getSalesDistribution));
ipcMain.handle(`GET:${Channels.GET_PAYMENT_METHOD_DISTRIBUTION}`, catchIpcHandler(dashboardController.getPaymentMethodDistribution));


// Vendor Return Apis
ipcMain.handle(`POST:${Channels.PROCESS_VENDOR_RETURN}`, catchIpcHandler(vendorReturnController.processVendorReturn));
ipcMain.handle(`GET:${Channels.GET_VENDOR_RETURNS}`, catchIpcHandler(vendorReturnController.getVendorReturns));
ipcMain.handle(`POST:${Channels.VOID_VENDOR_RETURN}`, catchIpcHandler(vendorReturnController.voidVendorReturn));


// Reports APIs
ipcMain.handle(`GET:${Channels.GENERATE_FINANCIAL_OVERVIEW}`, catchIpcHandler(reportsController.generateFinancialOverview));
ipcMain.handle(`GET:${Channels.GENERATE_CUSTOMER_ANALYTICS}`, catchIpcHandler(reportsController.generateCustomerAnalytics));
ipcMain.handle(`GET:${Channels.GENERATE_SALES_PERFORMANCE}`, catchIpcHandler(reportsController.generateSalesPerformance));
ipcMain.handle(`GET:${Channels.GENERATE_INVENTORY_VALUATION}`, catchIpcHandler(reportsController.generateInventoryValuation));
ipcMain.handle(`GET:${Channels.GENERATE_CASH_FLOW}`, catchIpcHandler(reportsController.generateCashFlow));
ipcMain.handle(`GET:${Channels.GENERATE_AGEING_REPORT}`, catchIpcHandler(reportsController.generateAgeingReport));
ipcMain.handle(`GET:${Channels.GENERATE_DAILY_OPERATIONS}`, catchIpcHandler(reportsController.generateDailyOperations));
ipcMain.handle(`GET:${Channels.GENERATE_AUDIT_REPORT}`, catchIpcHandler(reportsController.generateAuditReport));

// Manual Entry APIs
ipcMain.handle(`POST:${Channels.CREATE_MANUAL_ENTRY}`, catchIpcHandler(manualEntryController.createManualEntry));
ipcMain.handle(`GET:${Channels.GET_MANUAL_ENTRIES}`, catchIpcHandler(manualEntryController.getManualEntries));
ipcMain.handle(`GET:${Channels.GET_MANUAL_ENTRY_BY_ID}`, catchIpcHandler(manualEntryController.getManualEntryById));
ipcMain.handle(`POST:${Channels.VOID_MANUAL_ENTRY}`, catchIpcHandler(manualEntryController.voidManualEntry));

// Account Transfer APIs
ipcMain.handle(`POST:${Channels.CREATE_ACCOUNT_TRANSFER}`, catchIpcHandler(accountTransferController.createAccountTransfer));
ipcMain.handle(`GET:${Channels.GET_ACCOUNT_TRANSFERS}`, catchIpcHandler(accountTransferController.getAccountTransfers));
ipcMain.handle(`GET:${Channels.GET_ACCOUNT_TRANSFER_BY_ID}`, catchIpcHandler(accountTransferController.getAccountTransferById));
ipcMain.handle(`POST:${Channels.VOID_ACCOUNT_TRANSFER}`, catchIpcHandler(accountTransferController.voidAccountTransfer));