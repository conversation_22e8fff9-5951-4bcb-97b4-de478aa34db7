import { IRequest } from "../../common";

const catchIpcHandler = (
    fn: (event: Electron.IpcMainInvokeEvent, data: IRequest) => any,
) => {
    return async (event: Electron.IpcMainInvokeEvent, data: IRequest) => {
        let result = {
            data: null,
            error: null,
        };
        await Promise.resolve(fn(event, data))
            .then((data) => {
                result.data = data;
            })
            .catch((err: any) => {
                console.error(err);
                result.error = {
                    ...err,
                    message: err.message,
                };
            });
        return result;
    };
};

export {
    catchIpcHandler
}