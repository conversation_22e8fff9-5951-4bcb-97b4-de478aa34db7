export interface CashVaultTransactionFilters {
    page?: number;
    pageSize?: number;
    startDate?: Date;
    endDate?: Date;
    includeDeleted?: boolean;
}

export interface CashVaultTransaction {
    id: string;
    date: Date;
    amount: number;
    description: string;
    creditOrDebit: 'CREDIT' | 'DEBIT';
    referenceType: string;
    status: 'ACTIVE' | 'VOID';
    createdBy: {
        name: string;
    };
}

export interface CashVaultTransactionsResponse {
    transactions: CashVaultTransaction[];
    pagination: {
        total: number;
        pages: number;
        currentPage: number;
        pageSize: number;
    };
}

export interface InitializeVaultData {
    amount: number;
    adminId: string;
}

export interface VaultTransactionData {
    amount: number;
    adminId: string;
    description: string;
}

export interface CashVaultReconcileResponse {
    isReconciled: boolean;
    currentBalance: number;
    calculatedBalance: number;
    difference: number;
}

export interface GetCashVaultStatementParams {
    startDate: Date;
    endDate: Date;
    page?: number;
    pageSize?: number;
}

export interface CashVaultStatementEntry {
    id: string;
    date: Date;
    description: string;
    credit: number | null;
    debit: number | null;
    runningBalance: number;
    createdBy: {
        name: string;
    };
}

export interface CashVaultStatementSummary {
    totalCredits: number;
    totalDebits: number;
    net: number;
}

export interface CashVaultStatement {
    startDate: Date;
    endDate: Date;
    entries: CashVaultStatementEntry[];
    openingBalance: number;
    closingBalance: number;
    summary: CashVaultStatementSummary;
    pagination: {
        total: number;
        pages: number;
        currentPage: number;
        pageSize: number;
    };
}
