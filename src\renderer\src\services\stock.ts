import { http } from "./http";
import { Channels } from "@/common/constants";

interface GetStockParams {
    page?: number;
    limit?: number;
    search?: string;
    where?: any;
}

export const getAvailableStock = async ({ page = 1, limit = 10, search, where }: GetStockParams = {}) => {
    return await http.get(Channels.GET_AVAILABLE_STOCK, {
        query: { page, limit, search, where }
    });
};

export const getSoldStock = async ({ page = 1, limit = 10, search, where }: GetStockParams = {}) => {
    return await http.get(Channels.GET_SOLD_STOCK, {
        query: { page, limit, search, where }
    });
};

export const getStockByProduct = async (productId: string, { page = 1, limit = 10, status }: GetStockParams & { status?: string } = {}) => {
    return await http.get(Channels.GET_STOCK_BY_PRODUCT, {
        params: { productId },
        query: { page, limit, status }
    });
};

export const getStockByVendor = async (vendorId: string, { page = 1, limit = 10, status }: GetStockParams & { status?: string } = {}) => {
    return await http.get(Channels.GET_STOCK_BY_VENDOR, {
        params: { vendorId },
        query: { page, limit, status }
    });
};

export const getStockByPurchaseInvoice = async (purchaseInvoiceId: string, { page = 1, limit = 10, status }: GetStockParams & { status?: string } = {}) => {
    return await http.get(Channels.GET_STOCK_BY_PURCHASE_INVOICE, {
        params: { purchaseInvoiceId },
        query: { page, limit, status }
    });
};