import { CreditDebit, LedgerType, Status, } from "@prisma/client";

// DefineLedgerType,  types that mirror Prisma's enums
// export enum CreditDebit {
//     CREDIT = 'CREDIT',
//     DEBIT = 'DEBIT'
// }

// export enum Status {
//     ACTIVE = 'ACTIVE',
//     VOID = 'VOID'
// }

// Base params interface for pagination
interface PaginationParams {
    page?: number;
    limit?: number;
}

// Parameters for getting bank ledger entries
export interface GetBankLedgerParams extends PaginationParams {
    bankId?: string;
    startDate?: Date;
    endDate?: Date;
    creditOrDebit?: CreditDebit;
    referenceType?: LedgerType;
    status?: Status;
}

// Parameters for transaction summary
export interface BankTransactionSummaryParams {
    bankId?: string;
    startDate: Date;
    endDate: Date;
}

// Parameters for daily ledger
export interface DailyBankLedgerParams {
    bankId?: string;
    date: Date;
}

// Parameters for searching transactions
export interface SearchBankTransactionsParams extends PaginationParams {
    bankId?: string;
    searchQuery: string;
    transactionType?: LedgerType;
}

// Parameters for daily closing balances
export interface DailyClosingBalancesParams {
    bankId?: string;
    startDate: Date;
    endDate: Date;
}

// Response interfaces
export interface BankLedgerEntry {
    id: string;
    date: Date;
    amount: number;
    creditOrDebit: CreditDebit;
    description: string;
    referenceType: LedgerType;
    status: Status;
    bankId: string;
    bank?: {
        name: string;
    };
    createdBy: {
        name: string;
    };
    balance?: number; // Running balance
}

export interface PaginatedResponse<T> {
    data: T[];
    total: number;
    page: number;
    totalPages: number;
}

export interface BankLedgerResponse extends PaginatedResponse<BankLedgerEntry> {
    totals: {
        credits: number;
        debits: number;
        net: number;
    };
}

export interface TransactionSummaryByType {
    referenceType: LedgerType;
    creditTotal: number;
    debitTotal: number;
    count: number;
}

export interface BankTransactionSummary {
    summaryByType: TransactionSummaryByType[];
    overall: {
        totalCredits: number;
        totalDebits: number;
        netAmount: number;
        totalCount: number;
    };
}

export interface DailyBankLedger {
    date: Date;
    entries: BankLedgerEntry[];
    totals: {
        credits: number;
        debits: number;
        net: number;
    };
}

export interface BankReconciliation {
    bankId: string;
    bankName: string;
    currentBalance: number;
    calculatedBalance: number;
    isReconciled: boolean;
    difference: number;
}

export interface DailyClosingBalance {
    date: Date;
    closingBalance: number;
    totalCredits?: number;
    totalDebits?: number;
}