import { http } from "./http";
import { Channels } from "@/common/constants";
import { CreateExpenseData, GetExpenseParams, VoidExpenseParams } from "@/common/types/expense";


export const createExpense = async (params: CreateExpenseData) => {
    return await http.post(Channels.CREATE_EXPENSE, { body: params });
};

export const voidExpense = async ({ id, voidedById, reason }: VoidExpenseParams) => {
    return await http.delete(Channels.DELETE_EXPENSE, {
        params: { id, voidedById, reason }
    });
};

export const getExpense = async (id: string) => {
    return await http.get(Channels.GET_EXPENSE, { params: { id } });
};

export const getExpenses = async (params: GetExpenseParams) => {
    const { page = 1, limit = 10, ...rest } = params;
    return await http.get(Channels.GET_EXPENSES, {
        query: {
            page,
            limit,
            ...rest,
            // startDate: rest.startDate?.toISOString(),
            // endDate: rest.endDate?.toISOString(),
        }
    });
};

// export const getExpensesByDay = async (params: GetExpensesByDateParams) => {
//     return await http.get(Channels.GET_EXPENSES_BY_DAY, {
//         query: {
//             ...params,
//             date: params.date.toISOString()
//         }
//     });
// };

// export const getExpensesByDateRange = async (params: GetExpensesByDateRangeParams) => {
//     return await http.get(Channels.GET_EXPENSES_BY_DATE_RANGE, {
//         query: {
//             ...params,
//             startDate: params.startDate.toISOString(),
//             endDate: params.endDate.toISOString()
//         }
//     });
// };