import { createContext, useContext, useState, useEffect } from 'react'
import { productApi } from '../services'
import { useApi } from '../hooks'

export interface ProductOption {
  value: string
  label: string
}

interface ProductContextType {
  products: ProductOption[]
  loading: boolean
  error: any
  errorMessage: string | null
  refreshProducts: () => Promise<void>
}

const ProductContext = createContext<ProductContextType>({
  products: [],
  loading: false,
  error: null,
  errorMessage: null,
  refreshProducts: async () => {}
})

export const useProductContext = () => useContext(ProductContext)

export const ProductProvider = ({ children }: { children: React.ReactNode }) => {
  const [products, setProducts] = useState<ProductOption[]>([])

  const {
    data: productsData,
    isLoading: loading,
    request: getProducts,
    error,
    errorMessage
  } = useApi<ProductOption[], []>(productApi.getProductsForSelect)

  const fetchProducts = async () => {
    if (!products.length) {
      await getProducts()
    }
  }

  const refreshProducts = async (): Promise<void> => {
    setProducts([])
    await getProducts()
  }

  useEffect(() => {
    fetchProducts()
  }, [])

  useEffect(() => {
    if (productsData && productsData.length > 0) {
      setProducts(productsData)
    } else {
      setProducts([])
    }
  }, [productsData])

  return (
    <ProductContext.Provider
      value={{
        products,
        loading,
        error,
        errorMessage,
        refreshProducts
      }}
    >
      {children}
    </ProductContext.Provider>
  )
}
