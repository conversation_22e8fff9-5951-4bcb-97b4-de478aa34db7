import { useState } from 'react'



export default function useApi<T, P extends any[]>(apiCall: (...args: P) => Promise<any>) {
    const [isLoading, setIsLoading] = useState(false)
    const [error, setError] = useState(null)
    const [data, setData] = useState<T | null>(null)
    const [response, setResponse] = useState(null)
    const [errorMessage, setErrorMessage] = useState(null)

    const request = async (...args: P) => {
        setIsLoading(true)
        setError(null)
        setData(null)
        setErrorMessage(null)



        const response = await apiCall(...args)
        // console.log('useApi response', response)

        if (response.error?.error) {
            setErrorMessage(response.error?.error?.message)
            setError(response.error?.error)
            console.log(response.error?.error)
        } else if (response.data.error) {
            setErrorMessage(response.data.error?.message)
            setError(response.data.error)
            console.log(response.data.error)
        } else {
            setData(response.data.data)
            setResponse(response)
            // console.log('useApi data', response.data.data)
        }

        setIsLoading(false)

    }

    const clearData = () => {
        setIsLoading(false)
        setError(null)
        setData(null)
        setResponse(null)
        setErrorMessage(null)
    }

    return { request, isLoading, error, data, response, errorMessage, clearData }
}
