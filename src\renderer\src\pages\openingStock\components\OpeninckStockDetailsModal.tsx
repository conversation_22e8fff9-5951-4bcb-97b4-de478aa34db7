import React from 'react'
import { Modal, Typography, Descriptions, Table, Tag, Tooltip, Divider, Space } from 'antd'
import { TagOutlined, ShopOutlined, InfoCircleOutlined } from '@ant-design/icons'
import dayjs from 'dayjs'

const { Text, Title } = Typography

interface OpeningStockItem {
  id: string
  quantity: number
  purchasePrice: number
  total: number
  product: {
    name: string
    productId: string
    tag: string
    nature: string
    category: {
      name: string
    }
  }
}

interface OpeningStockData {
  id: string
  invoiceNumber: string
  date: string | Date
  status: string
  createdBy: {
    name: string
  }
  createdAt: string | Date
  voidedBy?: {
    name: string
  }
  voidedAt?: string | Date
  voidingReason?: string
  items: OpeningStockItem[]
}

interface OpeningStockDetailsModalProps {
  visible: boolean
  onClose: () => void
  openingStock: OpeningStockData | null
}

const OpeningStockDetailsModal: React.FC<OpeningStockDetailsModalProps> = ({
  visible,
  onClose,
  openingStock
}) => {
  if (!openingStock) return null

  const itemColumns = [
    {
      title: 'Sr. No.',
      dataIndex: 'index',
      key: 'index',
      width: 70,
      render: (_, __, index) => index + 1
    },
    {
      title: 'Product Details',
      dataIndex: 'product',
      key: 'product',
      render: (product) => (
        <Space direction="horizontal" size="small" wrap>
          <Tag>{product.name}</Tag>
          <Tag icon={<TagOutlined />} color="blue">
            {product.productId}
          </Tag>
          <Tag icon={<ShopOutlined />} color="cyan">
            {product.tag}
          </Tag>
          <Tag color="purple">{product.nature}</Tag>
        </Space>
      )
    },
    {
      title: 'Quantity',
      dataIndex: 'quantity',
      key: 'quantity',
      width: 100,
      render: (qty) => qty.toLocaleString('en-US')
    },
    {
      title: 'Purchase Price',
      dataIndex: 'purchasePrice',
      key: 'purchasePrice',
      width: 150,
      render: (price) => price.toLocaleString('en-US', { minimumFractionDigits: 2 })
    },
    {
      title: 'Total',
      dataIndex: 'total',
      key: 'total',
      width: 150,
      render: (total) => (
        <Text strong>{total.toLocaleString('en-US', { minimumFractionDigits: 2 })}</Text>
      )
    }
  ]

  const totalAmount = openingStock.items.reduce((sum, item) => sum + item.total, 0)

  return (
    <Modal
      open={visible}
      onCancel={onClose}
      footer={null}
      width={800}
      title={
        <Space align="center">
          <InfoCircleOutlined />
          <span>Opening Stock Details</span>
        </Space>
      }
    >
      <div className="mb-6">
        <Descriptions bordered column={2} size="small">
          <Descriptions.Item label="Invoice Number" span={1}>
            <Text strong>{openingStock.invoiceNumber}</Text>
          </Descriptions.Item>
          <Descriptions.Item label="Date" span={1}>
            {dayjs(openingStock.date).format('DD/MM/YYYY HH:mm')}
          </Descriptions.Item>
          <Descriptions.Item label="Status" span={1}>
            <Tag color={openingStock.status === 'ACTIVE' ? 'green' : 'red'}>
              {openingStock.status}
            </Tag>
          </Descriptions.Item>
          <Descriptions.Item label="Created By" span={1}>
            {openingStock.createdBy.name} (
            {dayjs(openingStock.createdAt).format('DD/MM/YYYY HH:mm')})
          </Descriptions.Item>
          <Descriptions.Item label="Total Amount" span={2}>
            <Text strong className="text-lg text-indigo-600">
              {totalAmount.toLocaleString('en-US', { minimumFractionDigits: 2 })}
            </Text>
          </Descriptions.Item>
        </Descriptions>

        {openingStock.voidingReason && (
          <div className="mt-4">
            <Title level={5} className="text-red-500">
              Void Information:
            </Title>
            <Descriptions bordered size="small">
              <Descriptions.Item label="Voided By" span={1}>
                {openingStock.voidedBy?.name}
              </Descriptions.Item>
              <Descriptions.Item label="Voided At" span={2}>
                {openingStock.voidedAt && dayjs(openingStock.voidedAt).format('DD/MM/YYYY HH:mm')}
              </Descriptions.Item>
              <Descriptions.Item label="Reason" span={3}>
                {openingStock.voidingReason}
              </Descriptions.Item>
            </Descriptions>
          </div>
        )}
      </div>

      <Divider orientation="left">Items ({openingStock.items.length})</Divider>

      <Table
        columns={itemColumns}
        dataSource={openingStock.items}
        pagination={false}
        rowKey="id"
        size="small"
        bordered
        summary={(pageData) => {
          const total = pageData.reduce((sum, item) => sum + item.total, 0)
          return (
            <Table.Summary.Row>
              <Table.Summary.Cell index={0} colSpan={4} align="right">
                <Text strong>Total:</Text>
              </Table.Summary.Cell>
              <Table.Summary.Cell index={1}>
                <Text strong className="text-indigo-600">
                  {total.toLocaleString('en-US', { minimumFractionDigits: 2 })}
                </Text>
              </Table.Summary.Cell>
            </Table.Summary.Row>
          )
        }}
      />
    </Modal>
  )
}

export default OpeningStockDetailsModal
