import { Layout, Typography, Card, Space, Button, Tabs } from 'antd'
import { PlusOutlined } from '@ant-design/icons'
import { useState } from 'react'
import { PaymentList } from './components/PaymentList'
import { TransferList } from './components/TransferList'
import { AccountTransferList } from './components/AccountTransferList'
import { AddPaymentDrawer } from './components/AddPaymentDrawer'
import { AddTransferDrawer } from './components/AddTransferDrawer'
import { AddAccountTransferDrawer } from './components/AddAccountTransferDrawer'
import { useTheme } from '@/renderer/contexts'

const { Content } = Layout
const { Title } = Typography

const Payments = () => {
  const [isAddPaymentDrawerOpen, setIsAddPaymentDrawerOpen] = useState(false)
  const [isAddTransferDrawerOpen, setIsAddTransferDrawerOpen] = useState(false)
  const [isAddAccountTransferDrawerOpen, setIsAddAccountTransferDrawerOpen] = useState(false)
  const [activeTab, setActiveTab] = useState('payments')
  const [refreshTrigger, setRefreshTrigger] = useState(0)

  const { isDarkMode } = useTheme()

  return (
    <Content className="p-6">
      <Card
        className={`mb-4 bg-[length:200%_200%] bg-[center] shadow-md ${
          isDarkMode
            ? 'bg-gradient-to-bl from-indigo-950 via-black to-indigo-900 shadow-indigo-950'
            : 'bg-gradient-to-bl from-indigo-200 via-white to-indigo-100 shadow-indigo-200'
        }`}
      >
        <Space className="w-full justify-between" size={'small'}>
          <Title level={4} className={`!mb-0 ${isDarkMode ? 'text-white' : 'text-gray-800'}`}>
            Payments & Transfers
          </Title>
          <Space>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => setIsAddPaymentDrawerOpen(true)}
              className="bg-green-500 hover:!bg-green-600"
            >
              Add Payment
            </Button>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => setIsAddTransferDrawerOpen(true)}
              className="bg-green-500 hover:!bg-green-600"
            >
              Add Location Transfer
            </Button>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => setIsAddAccountTransferDrawerOpen(true)}
              className="bg-blue-500 hover:!bg-blue-600"
            >
              Add Account Transfer
            </Button>
          </Space>
        </Space>
      </Card>

      <Card
        className={`mb-2 bg-[length:200%_200%] bg-[center] shadow-md ${
          isDarkMode
            ? 'bg-gradient-to-br from-indigo-950 via-black to-indigo-900 shadow-indigo-950'
            : 'bg-gradient-to-br from-indigo-200 via-white to-indigo-100 shadow-indigo-200'
        }`}
      >
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          items={[
            {
              key: 'payments',
              label: 'Payments',
              children: <PaymentList refreshTrigger={refreshTrigger} />
            },
            {
              key: 'location-transfers',
              label: 'Location Transfers',
              children: <TransferList refreshTrigger={refreshTrigger} />
            },
            {
              key: 'account-transfers',
              label: 'Account Transfers',
              children: <AccountTransferList refreshTrigger={refreshTrigger} />
            }
          ]}
        />

        <AddPaymentDrawer
          open={isAddPaymentDrawerOpen}
          onClose={() => setIsAddPaymentDrawerOpen(false)}
          setRefreshTrigger={setRefreshTrigger}
        />

        <AddTransferDrawer
          open={isAddTransferDrawerOpen}
          onClose={() => setIsAddTransferDrawerOpen(false)}
          setRefreshTrigger={setRefreshTrigger}
        />

        <AddAccountTransferDrawer
          open={isAddAccountTransferDrawerOpen}
          onClose={() => setIsAddAccountTransferDrawerOpen(false)}
          setRefreshTrigger={setRefreshTrigger}
        />
      </Card>
    </Content>
  )
}

export default Payments
