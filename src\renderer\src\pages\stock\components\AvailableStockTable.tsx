import { Table } from 'antd'
import { formatDate, formatCurrency } from '@/renderer/utils'
import type { TableProps } from 'antd'

export interface StockEntry {
  id: string
  saleItem?: {
    saleInvoice: {
      invoiceNumber: string
      date: string
    }
  }
  WalkInSaleItem?: {
    saleInvoice: {
      invoiceNumber: string
      date: string
    }
  }
}

export interface Stock {
  id: string
  purchasePrice: number
  quantity: number
  status: 'IN_STOCK' | 'SOLD_OUT'
  createdAt: Date
  updatedAt: Date
  vendor: {
    id: string
    name: string
  }
  purchaseInvoice: {
    id: string
    invoiceNumber: string
    date: string
    status: string
  }
  StockEntry: StockEntry[]
}

interface AvailableStockTableProps {
  data: Stock[] | undefined
  loading: boolean
  pagination: any
}

export const AvailableStockTable = ({ data, loading, pagination }: AvailableStockTableProps) => {
  const columns = [
    {
      title: 'Sr. No.',
      dataIndex: 'serialNumber',
      key: 'serialNumber',
      render: (_, __, index) => (pagination.current - 1) * pagination.pageSize + index + 1
    },
    {
      title: 'Invoice Number',
      dataIndex: ['purchaseInvoice', 'invoiceNumber'],
      key: 'invoiceNumber'
    },
    {
      title: 'Product ID',
      dataIndex: ['product', 'productId'],
      key: 'productId'
    },
    {
      title: 'Vendor',
      dataIndex: ['vendor', 'name'],
      key: 'vendorName'
    },
    {
      title: 'Purchase Price',
      dataIndex: 'purchasePrice',
      key: 'purchasePrice',
      render: (price: number) => formatCurrency(price)
    },
    {
      title: 'Quantity',
      dataIndex: 'quantity',
      key: 'quantity'
    },
    {
      title: 'Purchase Date',
      dataIndex: ['purchaseInvoice', 'date'],
      key: 'purchaseDate',
      render: (date: string) => formatDate(date)
    }
  ]

  return (
    <Table
      columns={columns}
      dataSource={data}
      loading={loading}
      sticky
      virtual
      pagination={{
        ...pagination,
        position: ['topRight'],
        showPrevNextJumpers: true,
        showSizeChanger: true,
        showQuickJumper: true,
        showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`
      }}
      rowKey="id"
    />
  )
}
