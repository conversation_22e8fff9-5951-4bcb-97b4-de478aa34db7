import { useEffect } from 'react'
import { <PERSON><PERSON>, Card, Col, Row, Table, Spin } from 'antd'
import { useTheme } from '@/renderer/contexts'
import { useApi } from '@/renderer/hooks'
import { reportsApi } from '@/renderer/services'
import { CustomerAnalyticsReport, ReportFormat } from '@/common/types'
import { CustomerType } from '@/common/types'
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell
} from 'recharts'

interface Props {
  format: ReportFormat
  shouldGenerate: boolean
  onGenerateComplete: () => void
  currentReportId: string
  dateRange?: [Date, Date]
  minPurchases?: number
  customerType?: CustomerType
}

const CustomerAnalytics = ({
  format,
  shouldGenerate,
  onGenerateComplete,
  currentReportId,
  dateRange,
  minPurchases,
  customerType
}: Props) => {
  const { isDarkMode } = useTheme()
  const { request, data, isLoading, errorMessage } = useApi<
    CustomerAnalyticsReport,
    [
      {
        format: ReportFormat
        startDate?: Date
        endDate?: Date
        minPurchases?: number
        customerType?: CustomerType
      }
    ]
  >(reportsApi.generateCustomerAnalytics)

  useEffect(() => {
    if (shouldGenerate && currentReportId === 'customer-analytics') {
      request({
        format,
        startDate: dateRange?.[0],
        endDate: dateRange?.[1],
        minPurchases,
        customerType
      }).finally(() => {
        onGenerateComplete()
      })
    }
  }, [shouldGenerate])

  if (isLoading) {
    return <Spin size="large" className="flex w-full justify-center p-8" />
  }

  if (errorMessage) {
    return <Alert type="error" message={errorMessage} className="mb-4" />
  }

  if (!data) return null

  const COLORS = ['#6366f1', '#8b5cf6', '#d946ef', '#f43f5e', '#f97316']

  return (
    <div className="space-y-6">
      {/* Summary Cards */}
      <Row gutter={[16, 16]}>
        <Col span={8}>
          <Card className={isDarkMode ? 'bg-black' : 'bg-white'}>
            <div className="text-center">
              <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Total Customers
              </p>
              <h2 className={`text-2xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-800'}`}>
                {data.summary.totalCustomers}
              </h2>
            </div>
          </Card>
        </Col>
        <Col span={8}>
          <Card className={isDarkMode ? 'bg-black' : 'bg-white'}>
            <div className="text-center">
              <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Active Customers
              </p>
              <h2 className={`text-2xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-800'}`}>
                {data.summary.activeCustomers}
              </h2>
            </div>
          </Card>
        </Col>
        <Col span={8}>
          <Card className={isDarkMode ? 'bg-black' : 'bg-white'}>
            <div className="text-center">
              <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Average Customer Value
              </p>
              <h2 className={`text-2xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-800'}`}>
                ₹{data.summary.averageCustomerValue.toLocaleString()}
              </h2>
            </div>
          </Card>
        </Col>
      </Row>

      {/* Charts */}
      <Row gutter={[16, 16]}>
        <Col span={12}>
          <Card title="Purchase Frequency" className={isDarkMode ? 'bg-black' : 'bg-white'}>
            <div style={{ height: 300 }}>
              <ResponsiveContainer width="100%" height="100%">
                <LineChart data={data.purchaseFrequency}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="frequency" />
                  <YAxis />
                  <Tooltip />
                  <Line
                    type="monotone"
                    dataKey="customerCount"
                    stroke="#6366f1"
                    name="Customers"
                    strokeWidth={2}
                  />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </Card>
        </Col>
        <Col span={12}>
          <Card title="Customer Segmentation" className={isDarkMode ? 'bg-black' : 'bg-white'}>
            <div style={{ height: 300 }}>
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={data.customerSegmentation}
                    dataKey="count"
                    nameKey="segment"
                    cx="50%"
                    cy="50%"
                    outerRadius={80}
                    label={({ segment, percentage }) => `${segment} (${percentage.toFixed(1)}%)`}
                  >
                    {data.customerSegmentation.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <Tooltip />
                </PieChart>
              </ResponsiveContainer>
            </div>
          </Card>
        </Col>
      </Row>

      {/* Top Customers Table */}
      <Card title="Top Customers" className={isDarkMode ? 'bg-black' : 'bg-white'}>
        <Table
          dataSource={data.topCustomers}
          columns={[
            {
              title: 'Customer ID',
              dataIndex: 'id',
              key: 'id'
            },
            {
              title: 'Name',
              dataIndex: 'name',
              key: 'name'
            },
            {
              title: 'Purchases',
              dataIndex: 'purchases',
              key: 'purchases'
            },
            {
              title: 'Total Spent',
              dataIndex: 'totalSpent',
              key: 'totalSpent',
              render: (value) => `₹${value.toLocaleString()}`
            },
            {
              title: 'Last Purchase',
              dataIndex: 'lastPurchase',
              key: 'lastPurchase',
              render: (date) => new Date(date).toLocaleDateString()
            },
            {
              title: 'Credit Limit',
              dataIndex: 'creditLimit',
              key: 'creditLimit',
              render: (value) => (value ? `₹${value.toLocaleString()}` : '-')
            },
            {
              title: 'Current Balance',
              dataIndex: 'currentBalance',
              key: 'currentBalance',
              render: (value) => `₹${value.toLocaleString()}`
            }
          ]}
          pagination={false}
          scroll={{ x: true }}
        />
      </Card>
    </div>
  )
}

export default CustomerAnalytics
