import { Typo<PERSON>, Space, Button, Alert, Skeleton, Table, Tag } from 'antd'
import { useApi } from '@/renderer/hooks'
import { dashboardApi } from '@/renderer/services'
import { ProductWithStock } from '@/common/types/dashBoard'
import { MdWarning, MdRefresh } from 'react-icons/md'
import type { ColumnsType } from 'antd/es/table'
import { useEffect } from 'react'

const { Title } = Typography

const LowStockProducts = () => {
  const {
    data,
    isLoading,
    error,
    request: fetchLowStockProducts
  } = useApi<ProductWithStock[], []>(dashboardApi.getLowStockProducts)

  useEffect(() => {
    fetchLowStockProducts()
  }, [])

  const columns: ColumnsType<ProductWithStock> = [
    {
      title: 'Product',
      dataIndex: 'name',
      key: 'name',
      render: (text, record) => (
        <Space direction="vertical" size={0}>
          <span>{text}</span>
          <small className="text-gray-500">{record.productId}</small>
        </Space>
      )
    },
    {
      title: 'Category',
      dataIndex: ['category', 'name'],
      key: 'category'
    },
    {
      title: 'Stock',
      key: 'stock',
      render: (_, record) => (
        <Space>
          <Tag color={record.quantityInStock <= record.minStockLevel / 2 ? 'red' : 'orange'}>
            {record.quantityInStock}
          </Tag>
          <small className="text-gray-500">/ {record.minStockLevel}</small>
        </Space>
      )
    }
  ]

  if (isLoading) {
    return (
      <div className="flex flex-col gap-4">
        <Space className="flex justify-between">
          <Space>
            <MdWarning className="text-2xl text-orange-500" />
            <Title level={5} className="!mb-0">
              Low Stock Alert
            </Title>
          </Space>
          <Button icon={<MdRefresh />} loading />
        </Space>
        <Skeleton active paragraph={{ rows: 4 }} />
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex flex-col gap-4">
        <Space className="flex justify-between">
          <Space>
            <MdWarning className="text-2xl text-orange-500" />
            <Title level={5} className="!mb-0">
              Low Stock Alert
            </Title>
          </Space>
          <Button icon={<MdRefresh />} onClick={() => fetchLowStockProducts()} />
        </Space>
        <Alert message="Error" description="Failed to load low stock data" type="error" showIcon />
      </div>
    )
  }

  if (!data) return null

  return (
    <div className="flex flex-col gap-4">
      <Space className="flex justify-between">
        <Space>
          <MdWarning className="text-2xl text-orange-500" />
          <Title level={5} className="!mb-0">
            Low Stock Alert
          </Title>
        </Space>
        <Button icon={<MdRefresh />} onClick={() => fetchLowStockProducts()} size="small" />
      </Space>

      <Table
        columns={columns}
        dataSource={data}
        size="small"
        pagination={false}
        rowKey="id"
        scroll={{ y: 200 }}
      />
    </div>
  )
}

export default LowStockProducts
