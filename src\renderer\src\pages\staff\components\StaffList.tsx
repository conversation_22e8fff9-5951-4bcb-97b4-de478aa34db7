import { Table, Button, Switch, Popconfirm, message, Tag } from 'antd'
import { useEffect, useState } from 'react'
import { useApi } from '@/renderer/hooks'
import { userApi } from '@/renderer/services'
import { formatDate } from '@/renderer/utils'
import type { GetUsersResponse } from '@/common/types'
import { useSelector } from 'react-redux'
import { IRootState } from '@/renderer/redux'

interface StaffListProps {
  refreshTrigger: number
}

export const StaffList = ({ refreshTrigger }: StaffListProps) => {
  const [page, setPage] = useState(1)
  const [pageSize, setPageSize] = useState(10)
  const [includeInactive, setIncludeInactive] = useState(false)

  const user = useSelector((state: IRootState) => state.user.data)

  const adminId = user?.id || ''

  const {
    data,
    isLoading,
    request: fetchUsers
  } = useApi<GetUsersResponse, [{ page: number; limit: number; includeInactive: boolean }]>(
    userApi.getUsers
  )

  useEffect(() => {
    fetchUsers({ page, limit: pageSize, includeInactive })
  }, [page, pageSize, includeInactive, refreshTrigger])

  const handleDeactivate = async (userId: string) => {
    const response = await userApi.deactivateUser(userId, adminId)

    if (response.error.error || response.data.error) {
      message.error(response.error.message || response.data.error.message)
      return
    }

    message.success('User deactivated successfully')
    fetchUsers({ page, limit: pageSize, includeInactive })
  }

  const columns = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name'
    },
    {
      title: 'Username',
      dataIndex: 'username',
      key: 'username'
    },
    {
      title: 'Role',
      dataIndex: 'role',
      key: 'role',
      render: (role: string) => <Tag color={role === 'ADMIN' ? 'red' : 'blue'}>{role}</Tag>
    },
    {
      title: 'Status',
      dataIndex: 'isActive',
      key: 'isActive',
      render: (isActive: boolean) => (
        <Tag color={isActive ? 'green' : 'red'}>{isActive ? 'Active' : 'Inactive'}</Tag>
      )
    },
    {
      title: 'Created At',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (date: string) => formatDate(date)
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_: any, record: any) =>
        record.isActive && (
          <Popconfirm
            title="Deactivate User"
            description="Are you sure you want to deactivate this user?"
            onConfirm={() => handleDeactivate(record.id)}
          >
            <Button
              disabled={user?.role !== 'DEVELOPER' && user?.role !== 'SUPER_ADMIN'}
              danger
              type="link"
            >
              Deactivate
            </Button>
          </Popconfirm>
        )
    }
  ]

  return (
    <div>
      <div className="mb-4 flex justify-end">
        <Switch
          checked={includeInactive}
          onChange={setIncludeInactive}
          checkedChildren="Show Inactive"
          unCheckedChildren="Hide Inactive"
        />
      </div>

      <Table
        columns={columns}
        dataSource={data?.users}
        rowKey="id"
        loading={isLoading}
        pagination={{
          current: page,
          pageSize,
          total: data?.total,
          position: ['topRight'],
          showPrevNextJumpers: true,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`,
          onChange: (page, pageSize) => {
            setPage(page)
            setPageSize(pageSize)
          }
        }}
      />
    </div>
  )
}
