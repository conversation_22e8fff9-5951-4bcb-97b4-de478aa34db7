import { Channels } from "@/common/constants";
import { http } from './http'
import { InvoiceNumberType } from "@/common/types";

export const generateInvoiceNumber = (type: InvoiceNumberType, adminId: string) => {
    return http.post(Channels.GENERATE_INVOICE_NUMBER, { body: { type, adminId } });
}

export const confirmInvoiceNumber = (invoiceNumber: string) => {
    return http.post(Channels.CONFIRM_INVOICE_NUMBER, { body: { invoiceNumber } });
}

export const cancelInvoiceNumber = (invoiceNumber: string) => {
    return http.post(Channels.CANCEL_INVOICE_NUMBER, { body: { invoiceNumber } });
}

export const cleanupExpiredNumbers = () => {
    return http.post(Channels.CLEANUP_EXPIRED_INVOICE_NUMBERS, { body: {} });
}
