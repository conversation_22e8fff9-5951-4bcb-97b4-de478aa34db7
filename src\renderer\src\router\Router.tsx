import { Spin } from 'antd'
import { LazyExoticComponent, Suspense, useEffect } from 'react'
import { Route, Routes, useLocation, Navigate } from 'react-router'
import { AppRoutes, LazyPages } from './routes'
import { App_Routes } from '@/common/constants'
import { useSelector } from 'react-redux'
import { IRootState } from '../redux'

const Router = () => {
  const license = useSelector((state: IRootState) => state.user.license)
  const isAuthenticated = useSelector((state: IRootState) => state.user.isAuthenticated)
  const location = useLocation()

  const SuspenseWrapper = (Component: LazyExoticComponent<() => JSX.Element | undefined>) => {
    return (
      <Suspense
        fallback={
          <div className="flex h-screen items-center justify-center">
            <Spin size="large" />
          </div>
        }
      >
        <Component />
      </Suspense>
    )
  }

  // If we're not on the license page and license is invalid, redirect to license page
  if (!license?.isValid && location.pathname !== App_Routes.LICENSE) {
    return <Navigate to={App_Routes.LICENSE} replace state={{ from: location }} />
  }

  // If we have a valid license but not authenticated and not on login page, redirect to login
  if (license?.isValid && !isAuthenticated && location.pathname !== App_Routes.LOGIN) {
    return <Navigate to={App_Routes.LOGIN} replace state={{ from: location }} />
  }

  // If we have a valid license and are authenticated but still on login/license pages, redirect to dashboard
  if (
    license?.isValid &&
    isAuthenticated &&
    (location.pathname === App_Routes.LOGIN || location.pathname === App_Routes.LICENSE)
  ) {
    return <Navigate to={App_Routes.DASHBOARD} replace />
  }

  return (
    <Routes>
      {/* License route - always accessible */}
      <Route path={App_Routes.LICENSE} element={SuspenseWrapper(LazyPages.License)} />

      {/* Auth routes - only when license is valid */}
      {license?.isValid && (
        <Route element={SuspenseWrapper(LazyPages.AuthLayout)}>
          <Route path={App_Routes.LOGIN} element={SuspenseWrapper(LazyPages.Login)} />
        </Route>
      )}

      {/* App routes - only when licensed and authenticated */}
      {license?.isValid && isAuthenticated && (
        <Route element={SuspenseWrapper(LazyPages.AppLayout)}>
          {AppRoutes.map((el, i) => (
            <Route key={i} path={el.path} element={SuspenseWrapper(el.component)} />
          ))}
          <Route path={App_Routes.LOGOUT} element={SuspenseWrapper(LazyPages.Logout)} />
        </Route>
      )}

      {/* Catch all route */}
      <Route
        path="*"
        element={
          <Navigate
            to={
              license?.isValid
                ? isAuthenticated
                  ? App_Routes.DASHBOARD
                  : App_Routes.LOGIN
                : App_Routes.LICENSE
            }
            replace
          />
        }
      />
    </Routes>
  )
}

export default Router
