.app-layout {
  height: 100vh;
  width: 100vw;
  .app-header {
    height: var(--header-height);
    box-shadow: rgba(99, 99, 99, 0.2) 0px 2px 8px 0px;
    z-index: 1;
    display: flex;
    align-items: center;
    gap: 5px;
    padding-left: 5px;
    padding-right: 15px;
    .sider-control-btn {
      @media screen and (max-width: 991px) {
        display: none;
      }
    }
    .logo {
      cursor: pointer;
      color: var(--text-color);
      padding-block: 10px;
      height: var(--header-height);
      display: flex;
      align-items: center;
      gap: 10px;
      img {
        height: 100%;
      }
    }
    .user-area {
      .ant-divider-vertical {
        height: 2.5em;
      }
    }

    @media screen and (max-width: 580px) {
      gap: 0px;
      padding: 0px;
      .app-inner-header {
        padding-top: 10px;
        padding-inline: 10px;
        flex-direction: column;
        justify-content: flex-start;
        gap: 0px;
        .logo {
          height: 30px;
          padding-block: 0px;
        }
        .user-area {
          justify-content: flex-end;
        }
      }
    }
  }
  .app-sider {
    .ant-menu-inline-collapsed > .ant-menu-item {
      padding-inline: calc(50% - 8px - 5px);
    }
    .ant-menu {
      padding-top: 10px;
    }
  }
  .app-content,
  .app-sider {
    height: calc(100vh - var(--header-height));
    overflow-y: auto;

    /* Custom Scrollbar Styles */
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
    }

    /* Light mode scrollbar */
    &::-webkit-scrollbar-thumb {
      background: rgba(0, 0, 0, 0.3);
      border-radius: 3px;
      border: 1px solid rgba(0, 0, 0, 0.1);

      &:hover {
        background: rgba(0, 0, 0, 0.5);
      }
    }
  }

  /* Dark mode specific scrollbar for app layout */
  &[data-theme="dark"] .app-content,
  &[data-theme="dark"] .app-sider,
  &.dark .app-content,
  &.dark .app-sider {
    &::-webkit-scrollbar-thumb {
      background: #ffffff !important;
      border: 1px solid #cccccc !important;

      &:hover {
        background: #ffffff !important;
        opacity: 1 !important;
      }
    }

    &::-webkit-scrollbar-track {
      background: rgba(255, 255, 255, 0.1) !important;
    }
  }

/* Global dark mode scrollbar override */
body.dark,
body[data-theme="dark"] {
  .app-content::-webkit-scrollbar-thumb,
  .app-sider::-webkit-scrollbar-thumb {
    background: #ffffff !important;
    border: 1px solid #cccccc !important;
  }

  .app-content::-webkit-scrollbar-track,
  .app-sider::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1) !important;
  }
}
  .app-content {
    border-radius: 0px;
    section {
      padding: 10px;
    }
  }
}

.user-dropdown {
  ul {
    padding-inline: 0px !important;
    padding-bottom: 0px !important;
    li {
      width: 210px !important;
      height: 40px;
    }
  }
}
