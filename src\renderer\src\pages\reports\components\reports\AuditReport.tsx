import { useEffect } from 'react'
import { <PERSON><PERSON>, Card, Col, Row, Table, Spin } from 'antd'
import { useTheme } from '@/renderer/contexts'
import { useApi } from '@/renderer/hooks'
import { reportsApi } from '@/renderer/services'
import { AuditReport as AuditReportType, ReportFormat } from '@/common/types'
// import { LedgerType } from '@/common/types'
import { Pie<PERSON>hart, Pie, Cell, ResponsiveContainer, Tooltip } from 'recharts'

interface Props {
  format: ReportFormat
  shouldGenerate: boolean
  onGenerateComplete: () => void
  currentReportId: string
  dateRange?: [Date, Date]
  userId?: string
  transactionType?: any
}

const AuditReport = ({
  format,
  shouldGenerate,
  onGenerateComplete,
  currentReportId,
  dateRange,
  userId,
  transactionType
}: Props) => {
  const { isDarkMode } = useTheme()
  const { request, data, isLoading, errorMessage } = useApi<
    AuditReportType,
    [
      {
        format: ReportFormat
        startDate?: Date
        endDate?: Date
        userId?: string
        transactionType?: any
      }
    ]
  >(reportsApi.generateAuditReport)

  useEffect(() => {
    if (shouldGenerate && currentReportId === 'audit-report') {
      request({
        format,
        startDate: dateRange?.[0],
        endDate: dateRange?.[1],
        userId,
        transactionType
      }).finally(() => {
        onGenerateComplete()
      })
    }
  }, [shouldGenerate])

  if (isLoading) {
    return <Spin size="large" className="flex w-full justify-center p-8" />
  }

  if (errorMessage) {
    return <Alert type="error" message={errorMessage} className="mb-4" />
  }

  if (!data) return null

  const COLORS = ['#6366f1', '#8b5cf6', '#d946ef', '#f43f5e', '#f97316']

  return (
    <div className="space-y-6">
      {/* Summary Cards */}
      <Row gutter={[16, 16]}>
        <Col span={6}>
          <Card className={isDarkMode ? 'bg-black' : 'bg-white'}>
            <div className="text-center">
              <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Total Transactions
              </p>
              <h2 className={`text-2xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-800'}`}>
                {data.summary.totalTransactions}
              </h2>
            </div>
          </Card>
        </Col>
        <Col span={6}>
          <Card className={isDarkMode ? 'bg-black' : 'bg-white'}>
            <div className="text-center">
              <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Void Transactions
              </p>
              <h2 className={`text-2xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-800'}`}>
                {data.summary.voidTransactions}
              </h2>
            </div>
          </Card>
        </Col>
        <Col span={6}>
          <Card className={isDarkMode ? 'bg-black' : 'bg-white'}>
            <div className="text-center">
              <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Modifications
              </p>
              <h2 className={`text-2xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-800'}`}>
                {data.summary.modifications}
              </h2>
            </div>
          </Card>
        </Col>
        <Col span={6}>
          <Card className={isDarkMode ? 'bg-black' : 'bg-white'}>
            <div className="text-center">
              <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Unusual Activity
              </p>
              <h2 className={`text-2xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-800'}`}>
                {data.summary.unusualActivity}
              </h2>
            </div>
          </Card>
        </Col>
      </Row>

      {/* Action Distribution Chart */}
      <Card title="Action Distribution" className={isDarkMode ? 'bg-black' : 'bg-white'}>
        <div style={{ height: 400 }}>
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie
                data={data.actionDistribution}
                dataKey="count"
                nameKey="action"
                cx="50%"
                cy="50%"
                outerRadius={120}
                label={({ action, percentage }) => `${action} (${percentage.toFixed(1)}%)`}
              >
                {data.actionDistribution.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                ))}
              </Pie>
              <Tooltip />
            </PieChart>
          </ResponsiveContainer>
        </div>
      </Card>

      {/* Transactions Table */}
      <Card title="Audit Trail" className={isDarkMode ? 'bg-black' : 'bg-white'}>
        <Table
          dataSource={data.transactions}
          columns={[
            {
              title: 'Date',
              dataIndex: 'date',
              key: 'date',
              render: (date) => new Date(date).toLocaleString()
            },
            {
              title: 'User',
              dataIndex: 'user',
              key: 'user'
            },
            {
              title: 'Action',
              dataIndex: 'action',
              key: 'action'
            },
            {
              title: 'Details',
              dataIndex: 'details',
              key: 'details'
            },
            {
              title: 'Original Value',
              dataIndex: 'originalValue',
              key: 'originalValue'
            },
            {
              title: 'New Value',
              dataIndex: 'newValue',
              key: 'newValue'
            },
            {
              title: 'Status',
              dataIndex: 'status',
              key: 'status'
            }
          ]}
          pagination={false}
          scroll={{ x: true }}
        />
      </Card>
    </div>
  )
}

export default AuditReport
