import { IRequest } from '../../common';
import { dashboardService } from '../services/dashboard';
import { TimeRange } from '@/common/types/dashBoard';

class DashboardController {
    async getDailySalesOverview(_event: Electron.IpcMainInvokeEvent, _req: IRequest) {
        return await dashboardService.getDailySalesOverview();
    }

    async getSalesTimeline(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { days } = req.body as TimeRange;
        return await dashboardService.getSalesTimeline({ days });
    }

    async getTopProducts(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { days } = req.body as TimeRange;
        return await dashboardService.getTopProducts({ days });
    }

    async getLowStockProducts(_event: Electron.IpcMainInvokeEvent, _req: IRequest) {
        return await dashboardService.getLowStockProducts();
    }

    async getStockValueOverview(_event: Electron.IpcMainInvokeEvent, _req: IRequest) {
        return await dashboardService.getStockValueOverview();
    }

    async getCashFlowOverview(_event: Electron.IpcMainInvokeEvent, _req: IRequest) {
        return await dashboardService.getCashFlowOverview();
    }

    async getPartyOverview(_event: Electron.IpcMainInvokeEvent, _req: IRequest) {
        return await dashboardService.getPartyOverview();
    }

    async getSalesDistribution(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { days } = req.body as TimeRange;
        return await dashboardService.getSalesDistribution({ days });
    }

    async getPaymentMethodDistribution(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { days } = req.body as TimeRange;
        return await dashboardService.getPaymentMethodDistribution({ days });
    }
}

export const dashboardController = new DashboardController();
