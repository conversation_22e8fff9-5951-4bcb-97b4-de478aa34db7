import { Modal, Form, Input, Typography } from 'antd'
import { useTheme } from '@/renderer/contexts'

const { Text } = Typography

interface Props {
  open: boolean
  loading: boolean
  onConfirm: (reason: string) => void
  onCancel: () => void
}

const VoidConfirmationModal = ({ open, loading, onConfirm, onCancel }: Props) => {
  const [form] = Form.useForm()
  const { isDarkMode } = useTheme()

  const handleOk = async () => {
    try {
      const values = await form.validateFields()
      onConfirm(values.reason)
      form.resetFields()
    } catch (error) {
      // Form validation error
    }
  }

  const handleCancel = () => {
    form.resetFields()
    onCancel()
  }

  return (
    <Modal
      title="Void Sale Invoice"
      open={open}
      onOk={handleOk}
      onCancel={handleCancel}
      okText="Yes, Void"
      cancelText="No, Cancel"
      confirmLoading={loading}
      okButtonProps={{
        danger: true
      }}
      className={isDarkMode ? 'dark-theme-modal' : ''}
    >
      <div>
        <Text type="danger" strong>
          Warning: This action cannot be undone!
        </Text>
        <Form form={form} layout="vertical" className="mt-4">
          <Form.Item
            name="reason"
            label="Reason"
            rules={[{ required: true, message: 'Please enter a reason for voiding' }]}
          >
            <Input.TextArea rows={3} placeholder="Enter reason for voiding" />
          </Form.Item>
        </Form>
      </div>
    </Modal>
  )
}

export default VoidConfirmationModal
