import { useEffect } from 'react'
import { <PERSON><PERSON>, Card, Col, Row, Table, Spin } from 'antd'
import { useTheme } from '@/renderer/contexts'
import { useApi } from '@/renderer/hooks'
import { reportsApi } from '@/renderer/services'
import { SalesPerformanceReport, ReportFormat } from '@/common/types'
import { CustomerType } from '@/common/types'
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  Legend
} from 'recharts'

interface Props {
  format: ReportFormat
  shouldGenerate: boolean
  onGenerateComplete: () => void
  currentReportId: string
  dateRange?: [Date, Date]
  customerType?: CustomerType
  categoryId?: string
  productId?: string
  minAmount?: number
}

const SalesPerformance = ({
  format,
  shouldGenerate,
  onGenerateComplete,
  currentReportId,
  dateRange,
  customerType,
  categoryId,
  productId,
  minAmount
}: Props) => {
  const { isDarkMode } = useTheme()
  const { request, data, isLoading, errorMessage } = useApi<
    SalesPerformanceReport,
    [
      {
        format: ReportFormat
        startDate?: Date
        endDate?: Date
        customerType?: CustomerType
        categoryId?: string
        productId?: string
        minAmount?: number
      }
    ]
  >(reportsApi.generateSalesPerformance)

  useEffect(() => {
    if (shouldGenerate && currentReportId === 'sales-performance') {
      request({
        format,
        startDate: dateRange?.[0],
        endDate: dateRange?.[1],
        customerType,
        categoryId,
        productId,
        minAmount
      }).finally(() => {
        onGenerateComplete()
      })
    }
  }, [shouldGenerate])

  if (isLoading) {
    return <Spin size="large" className="flex w-full justify-center p-8" />
  }

  if (errorMessage) {
    return <Alert type="error" message={errorMessage} className="mb-4" />
  }

  if (!data) return null

  const COLORS = ['#6366f1', '#8b5cf6', '#d946ef', '#f43f5e', '#f97316']

  return (
    <div className="space-y-6">
      {/* Summary Cards */}
      <Row gutter={[16, 16]}>
        <Col span={6}>
          <Card className={isDarkMode ? 'bg-black' : 'bg-white'}>
            <div className="text-center">
              <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Total Sales
              </p>
              <h2 className={`text-2xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-800'}`}>
                ₹{data.summary.totalSales.toLocaleString()}
              </h2>
            </div>
          </Card>
        </Col>
        <Col span={6}>
          <Card className={isDarkMode ? 'bg-black' : 'bg-white'}>
            <div className="text-center">
              <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Average Order Value
              </p>
              <h2 className={`text-2xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-800'}`}>
                ₹{data.summary.averageOrderValue.toLocaleString()}
              </h2>
            </div>
          </Card>
        </Col>
        <Col span={6}>
          <Card className={isDarkMode ? 'bg-black' : 'bg-white'}>
            <div className="text-center">
              <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Total Orders
              </p>
              <h2 className={`text-2xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-800'}`}>
                {data.summary.totalOrders}
              </h2>
            </div>
          </Card>
        </Col>
        <Col span={6}>
          <Card className={isDarkMode ? 'bg-black' : 'bg-white'}>
            <div className="text-center">
              <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Return Rate
              </p>
              <h2 className={`text-2xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-800'}`}>
                {(data.summary.returnRate * 100).toFixed(1)}%
              </h2>
            </div>
          </Card>
        </Col>
      </Row>

      {/* Sales Trend */}
      <Card title="Sales Trend" className={isDarkMode ? 'bg-black' : 'bg-white'}>
        <div style={{ height: 400 }}>
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={data.salesTrend}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="period" />
              <YAxis yAxisId="left" />
              <YAxis yAxisId="right" orientation="right" />
              <Tooltip formatter={(value) => `₹${Number(value).toLocaleString()}`} />
              <Legend />
              <Line
                yAxisId="left"
                type="monotone"
                dataKey="amount"
                stroke="#6366f1"
                name="Sales Amount"
                strokeWidth={2}
              />
              <Line
                yAxisId="right"
                type="monotone"
                dataKey="orders"
                stroke="#f43f5e"
                name="Orders"
                strokeWidth={2}
              />
            </LineChart>
          </ResponsiveContainer>
        </div>
      </Card>

      {/* Distribution Charts */}
      <Row gutter={[16, 16]}>
        <Col span={12}>
          <Card title="Sales by Customer Type" className={isDarkMode ? 'bg-black' : 'bg-white'}>
            <div style={{ height: 300 }}>
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={data.salesByCustomerType}
                    dataKey="amount"
                    nameKey="type"
                    cx="50%"
                    cy="50%"
                    outerRadius={80}
                    label={({ type, percentage }) => `${type} (${percentage.toFixed(1)}%)`}
                  >
                    {data.salesByCustomerType.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <Tooltip formatter={(value) => `₹${Number(value).toLocaleString()}`} />
                </PieChart>
              </ResponsiveContainer>
            </div>
          </Card>
        </Col>
        <Col span={12}>
          <Card
            title="Payment Method Distribution"
            className={isDarkMode ? 'bg-black' : 'bg-white'}
          >
            <div style={{ height: 300 }}>
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={data.paymentMethodDistribution}
                    dataKey="amount"
                    nameKey="method"
                    cx="50%"
                    cy="50%"
                    outerRadius={80}
                    label={({ method, percentage }) => `${method} (${percentage.toFixed(1)}%)`}
                  >
                    {data.paymentMethodDistribution.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <Tooltip formatter={(value) => `₹${Number(value).toLocaleString()}`} />
                </PieChart>
              </ResponsiveContainer>
            </div>
          </Card>
        </Col>
      </Row>

      {/* Top Products Table */}
      <Card title="Top Products" className={isDarkMode ? 'bg-black' : 'bg-white'}>
        <Table
          dataSource={data.topProducts}
          columns={[
            {
              title: 'Product ID',
              dataIndex: 'productId',
              key: 'productId'
            },
            {
              title: 'Name',
              dataIndex: 'name',
              key: 'name'
            },
            {
              title: 'Quantity',
              dataIndex: 'quantity',
              key: 'quantity'
            },
            {
              title: 'Amount',
              dataIndex: 'amount',
              key: 'amount',
              render: (value) => `₹${value.toLocaleString()}`
            },
            {
              title: 'Profit',
              dataIndex: 'profit',
              key: 'profit',
              render: (value) => `₹${value.toLocaleString()}`
            }
          ]}
          pagination={false}
          scroll={{ x: true }}
        />
      </Card>
    </div>
  )
}

export default SalesPerformance
