import { resolve } from 'path'
import { defineConfig, externalizeDepsPlugin } from 'electron-vite'
import react from '@vitejs/plugin-react'

export default defineConfig({
  main: {
    plugins: [externalizeDepsPlugin()],
    resolve: {
      alias: {
        '@/renderer': resolve('src/renderer/src'),
        '@/common': resolve('src/common/'),
      }
    },
    build: {
      rollupOptions: {
        external: [
          '@prisma/client',
          '.prisma'
        ]
      }
    }
  },
  preload: {
    plugins: [externalizeDepsPlugin()]
  },
  renderer: {
    resolve: {
      alias: {
        '@/renderer': resolve('src/renderer/src'),
        '@/common': resolve('src/common/'),
      }
    },
    plugins: [react()],
    server: {
      hmr: {
        overlay: false
      }
    },
    optimizeDeps: {
      force: true
    }
  }
})