import {
  Table,
  Tag,
  Card,
  Space,
  DatePicker,
  Select,
  Form,
  Button,
  Modal,
  Input,
  App,
  Tooltip,
  Descriptions
} from 'antd'
import { useEffect, useState } from 'react'
import { useApi } from '@/renderer/hooks'
import { paymentApi } from '@/renderer/services'
import { formatDate, formatCurrency } from '@/renderer/utils'
import { useTheme } from '@/renderer/contexts'
import { GetPaymentsParams } from '@/common/types'
import { usePartyContext } from '@/renderer/contexts'
import { useSelector } from 'react-redux'
import { IRootState } from '@/renderer/redux'
import { DeleteOutlined, EyeOutlined } from '@ant-design/icons'

const { RangePicker } = DatePicker
const { TextArea } = Input

interface PaymentListProps {
  refreshTrigger: number
}

interface PaymentResponse {
  payments: Array<{
    id: string
    date: string
    amount: number
    description?: string
    type: 'PAID' | 'RECEIVED'
    party: { name: string }
    cashSource: string
    bank: { name: string }
    status: 'ACTIVE' | 'VOID'
    createdBy: { name: string }
    voidedBy?: { name: string }
    voidedAt?: string
    voidingReason?: string
  }>
  total: number
  page: number
  totalPages: number
}

export const PaymentList = ({ refreshTrigger }: PaymentListProps) => {
  const { isDarkMode } = useTheme()
  const [form] = Form.useForm()
  const [voidForm] = Form.useForm()
  const { message } = App.useApp()
  const user = useSelector((state: IRootState) => state.user.data)
  const { vendors, customers, creditors } = usePartyContext()

  const [dateRange, setDateRange] = useState<[Date, Date] | null>(null)
  const [type, setType] = useState<'PAID' | 'RECEIVED' | null>(null)
  const [status, setStatus] = useState<'ACTIVE' | 'VOID' | 'ALL'>('ACTIVE')
  const [partyType, setPartyType] = useState<'ALL' | 'VENDOR' | 'CUSTOMER' | 'CREDITOR'>('ALL')
  const [selectedParty, setSelectedParty] = useState<string | null>(null)
  const [isVoidModalOpen, setIsVoidModalOpen] = useState(false)
  const [selectedPaymentId, setSelectedPaymentId] = useState<string | null>(null)
  const [voidLoading, setVoidLoading] = useState(false)
  const [isDetailsModalOpen, setIsDetailsModalOpen] = useState(false)
  const [selectedPayment, setSelectedPayment] = useState<any>(null)

  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0
  })

  const {
    data: payments,
    isLoading,
    request: fetchPayments
  } = useApi<PaymentResponse, [GetPaymentsParams]>(paymentApi.getPayments)

  useEffect(() => {
    fetchPayments({
      startDate: dateRange?.[0],
      endDate: dateRange?.[1],
      type: type || undefined,
      status: status === 'ALL' ? undefined : status,
      partyId: selectedParty || undefined,
      page: pagination.current,
      limit: pagination.pageSize
    })
  }, [
    dateRange,
    type,
    status,
    selectedParty,
    refreshTrigger,
    pagination.current,
    pagination.pageSize
  ])

  useEffect(() => {
    if (payments) {
      setPagination((prev) => ({
        ...prev,
        total: payments.total
      }))
    }
  }, [payments])

  const handleVoidPayment = async () => {
    if (!selectedPaymentId || !user?.id) return

    try {
      setVoidLoading(true)
      const values = await voidForm.validateFields()

      const response = await paymentApi.voidPayment(selectedPaymentId, user.id, values.reason)

      if (response.error.error || response.data.error) {
        throw new Error(response.error.message || response.data.error.message)
      }

      message.success('Payment voided successfully')
      setIsVoidModalOpen(false)
      voidForm.resetFields()
      setSelectedPaymentId(null)
      fetchPayments({
        startDate: dateRange?.[0],
        endDate: dateRange?.[1],
        type: type || undefined,
        status: status === 'ALL' ? undefined : status,
        partyId: selectedParty || undefined,
        page: pagination.current,
        limit: pagination.pageSize
      })
    } catch (error: any) {
      message.error(error.message || 'Failed to void payment')
    } finally {
      setVoidLoading(false)
    }
  }

  const getFilteredParties = () => {
    switch (partyType) {
      case 'VENDOR':
        return vendors
      case 'CUSTOMER':
        return customers
      case 'CREDITOR':
        return creditors
      default:
        return [...vendors, ...customers, ...creditors]
    }
  }

  const typeOptions = [
    { label: 'Paid', value: 'PAID' },
    { label: 'Received', value: 'RECEIVED' }
  ]

  const statusOptions = [
    { label: 'Active', value: 'ACTIVE' },
    { label: 'Void', value: 'VOID' },
    { label: 'All', value: 'ALL' }
  ]

  const partyTypeOptions = [
    { label: 'All Parties', value: 'ALL' },
    { label: 'Vendors', value: 'VENDOR' },
    { label: 'Customers', value: 'CUSTOMER' },
    { label: 'Creditors', value: 'CREDITOR' }
  ]

  const truncateDescription = (description: string) => {
    if (!description) return '-'
    if (description.length <= 40) return description
    return description.substring(0, 40) + '...'
  }

  const columns = [
    {
      title: 'Sr. No.',
      dataIndex: 'index',
      key: 'index',
      render: (_, __, index) => (pagination.current - 1) * pagination.pageSize + index + 1,
      width: 70
    },
    {
      title: 'Date',
      dataIndex: 'date',
      key: 'date',
      render: (date: string) => formatDate(date),
      width: 100
    },
    {
      title: 'Party',
      dataIndex: ['party', 'name'],
      key: 'party'
    },

    {
      title: 'Description',
      dataIndex: 'description',
      key: 'description',
      render: (description: string) =>
        description ? (
          <Tooltip title={description.length > 40 ? description : null} mouseEnterDelay={0.5}>
            <span>{truncateDescription(description)}</span>
          </Tooltip>
        ) : (
          '-'
        )
    },
    {
      title: 'Type',
      dataIndex: 'type',
      key: 'type',
      render: (type: string) => <Tag color={type === 'PAID' ? 'red' : 'green'}>{type}</Tag>,
      width: 110
    },
    {
      title: 'Amount',
      dataIndex: 'amount',
      key: 'amount',
      align: 'center' as const,
      render: (amount: number) => formatCurrency(amount),
      width: 130
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => <Tag color={status === 'ACTIVE' ? 'green' : 'red'}>{status}</Tag>,
      width: 100
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_: any, record: any) => (
        <Space>
          <Button
            type="text"
            size="small"
            onClick={() => {
              setSelectedPayment(record)
              setIsDetailsModalOpen(true)
            }}
            icon={<EyeOutlined />}
          />
          {record.status === 'ACTIVE' && (
            <Button
              type="text"
              danger
              size="small"
              onClick={() => {
                setSelectedPaymentId(record.id)
                setIsVoidModalOpen(true)
              }}
              icon={<DeleteOutlined />}
            />
          )}
        </Space>
      ),
      width: 120
    }
  ]

  return (
    <>
      <Card
        className={`bg-[length:200%_200%] bg-[center] shadow-lg ${
          isDarkMode
            ? 'bg-gradient-to-bl from-indigo-950 via-black to-indigo-900 shadow-indigo-950'
            : 'bg-gradient-to-bl from-indigo-200 via-white to-indigo-100 shadow-indigo-200'
        }`}
      >
        <Form form={form} layout="vertical" className="mb-4">
          <Space wrap className="w-full">
            <Form.Item label="Date Range" className="!mb-0">
              <RangePicker
                onChange={(_, dateStrings) => {
                  if (dateStrings[0] && dateStrings[1]) {
                    setDateRange([new Date(dateStrings[0]), new Date(dateStrings[1])])
                  } else {
                    setDateRange(null)
                  }
                }}
              />
            </Form.Item>
            <Form.Item label="Type" className="!mb-0">
              <Select
                allowClear
                placeholder="Select type"
                options={typeOptions}
                value={type}
                onChange={setType}
                style={{ width: 150 }}
              />
            </Form.Item>
            <Form.Item label="Party Type" className="!mb-0">
              <Select
                placeholder="Select party type"
                options={partyTypeOptions}
                value={partyType}
                onChange={(value) => {
                  setPartyType(value)
                  setSelectedParty(null)
                }}
                style={{ width: 150 }}
              />
            </Form.Item>
            <Form.Item label="Party" className="!mb-0 w-96">
              <Select
                className="w-full"
                allowClear
                showSearch
                placeholder="Select party"
                options={getFilteredParties()}
                value={selectedParty}
                onChange={setSelectedParty}
                filterOption={(input, option) =>
                  (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                }
              />
            </Form.Item>
            <Form.Item label="Status" className="!mb-0">
              <Select
                placeholder="Select status"
                options={statusOptions}
                value={status}
                onChange={setStatus}
                style={{ width: 150 }}
              />
            </Form.Item>
          </Space>
        </Form>

        <Table
          columns={columns}
          dataSource={payments?.payments || []}
          loading={isLoading}
          rowKey="id"
          virtual
          sticky
          size="small"
          pagination={{
            ...pagination,
            onChange: (page, pageSize) =>
              setPagination((prev) => ({
                ...prev,
                current: page,
                pageSize: pageSize || prev.pageSize
              })),
            position: ['topRight'],
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`
          }}
        />
      </Card>

      {/* Details Modal */}
      <Modal
        title="Payment Details"
        open={isDetailsModalOpen}
        onCancel={() => {
          setIsDetailsModalOpen(false)
          setSelectedPayment(null)
        }}
        footer={null}
        width={800}
      >
        {selectedPayment && (
          <Descriptions bordered column={2}>
            {/* Basic Information */}
            <Descriptions.Item label="Date" span={2}>
              {formatDate(selectedPayment.date)}
            </Descriptions.Item>
            <Descriptions.Item label="Party" span={2}>
              {selectedPayment.party.name}
            </Descriptions.Item>
            <Descriptions.Item label="Type">
              <Tag color={selectedPayment.type === 'PAID' ? 'red' : 'green'}>
                {selectedPayment.type}
              </Tag>
            </Descriptions.Item>
            <Descriptions.Item label="Amount">
              {formatCurrency(selectedPayment.amount)}
            </Descriptions.Item>

            {/* Transaction Details */}
            <Descriptions.Item label="Description" span={2}>
              {selectedPayment.description || '-'}
            </Descriptions.Item>
            <Descriptions.Item label="Payment Method">
              {selectedPayment.paymentMethod?.replace('_', ' ')}
            </Descriptions.Item>
            <Descriptions.Item label="Status">
              <Tag color={selectedPayment.status === 'ACTIVE' ? 'green' : 'red'}>
                {selectedPayment.status}
              </Tag>
            </Descriptions.Item>

            {/* Location Information */}
            <Descriptions.Item label="Source">
              {selectedPayment.sourceLocation?.replace('_', ' ')}
            </Descriptions.Item>
            <Descriptions.Item label="Destination">
              {selectedPayment.destinationLocation?.replace('_', ' ')}
            </Descriptions.Item>

            {/* Audit Information */}
            <Descriptions.Item label="Created By" span={2}>
              {selectedPayment.createdBy.name}
            </Descriptions.Item>

            {/* Voiding Information - Only show if payment is voided */}
            {selectedPayment.status === 'VOID' && (
              <>
                <Descriptions.Item label="Voided By" span={1} className="border-t">
                  {selectedPayment.voidedBy?.name}
                </Descriptions.Item>
                <Descriptions.Item label="Voided At" span={1} className="border-t">
                  {selectedPayment.voidedAt ? formatDate(selectedPayment.voidedAt) : '-'}
                </Descriptions.Item>
                <Descriptions.Item label="Voiding Reason" span={2} className="border-t">
                  {selectedPayment.voidingReason || '-'}
                </Descriptions.Item>
              </>
            )}
          </Descriptions>
        )}
      </Modal>

      <Modal
        title="Void Payment"
        open={isVoidModalOpen}
        onOk={handleVoidPayment}
        onCancel={() => {
          setIsVoidModalOpen(false)
          setSelectedPaymentId(null)
          voidForm.resetFields()
        }}
        confirmLoading={voidLoading}
        okText="Void"
        okButtonProps={{ danger: true }}
      >
        <div>
          <p className="mb-4 text-red-500">Warning: This action cannot be undone!</p>
          <Form form={voidForm} layout="vertical">
            <Form.Item
              name="reason"
              label="Reason"
              rules={[{ required: true, message: 'Please enter a reason for voiding' }]}
            >
              <TextArea rows={3} placeholder="Enter reason for voiding" />
            </Form.Item>
          </Form>
        </div>
      </Modal>
    </>
  )
}
