import { useState, useEffect } from 'react'
import { Table, Space, Tag, Typography, Tooltip, Card, Modal, Input, Button } from 'antd'
import {
  InfoCircleOutlined,
  TagOutlined,
  ShopOutlined,
  DeleteOutlined,
  EyeOutlined
} from '@ant-design/icons'
import { openingStockApi } from '@/renderer/services'
import { purchaseInvoiceApi } from '@/renderer/services'
import dayjs from 'dayjs'
import { useTheme } from '@/renderer/contexts'
import { App } from 'antd'
import { useSelector } from 'react-redux'
import { IRootState } from '@/renderer/redux'
import OpeningStockDetailsModal from './OpeningStockDetailsModal'

const { Text } = Typography

interface OpeningStockListProps {
  refreshTrigger: number
}

export const OpeningStockList = ({ refreshTrigger }: OpeningStockListProps) => {
  const [loading, setLoading] = useState(false)
  const [data, setData] = useState<any>([])
  const { isDarkMode } = useTheme()
  const { message } = App.useApp()
  const [voidReason, setVoidReason] = useState('')
  const [selectedInvoice, setSelectedInvoice] = useState<any | null>(null)
  const [isVoidModalVisible, setIsVoidModalVisible] = useState(false)
  const [isDetailsModalVisible, setIsDetailsModalVisible] = useState(false)
  const user = useSelector((state: IRootState) => state.user.data)

  const fetchOpeningStock = async () => {
    setLoading(true)
    const response = await openingStockApi.getOpeningStock()
    setLoading(false)

    if (response.error.error || response.data.error) {
      message.error(response.error.message || response.data.error.message)
      return
    }

    setData(response.data.data)
  }

  const handleVoidInvoice = async () => {
    if (!selectedInvoice || !voidReason.trim()) {
      message.error('Please enter the reason for voiding this opening stock')
      return
    }

    const response = await purchaseInvoiceApi.voidPurchaseInvoice({
      id: selectedInvoice.id,
      adminId: user?.id || '',
      reason: voidReason
    })

    if (response.error.error || response.data.error) {
      message.error(response.error.message || response.data.error.message)
      return
    }

    message.success('Opening stock voided successfully')
    setIsVoidModalVisible(false)
    setSelectedInvoice(null)
    setVoidReason('')
    fetchOpeningStock()
  }

  useEffect(() => {
    fetchOpeningStock()
  }, [refreshTrigger])

  const columns = [
    {
      title: 'Sr. No.',
      dataIndex: 'index',
      key: 'index',
      render: (_, __, index) => index + 1
    },
    {
      title: 'Invoice Number',
      dataIndex: 'invoiceNumber',
      key: 'invoiceNumber',
      render: (text: string) => <Text strong>{text}</Text>
    },
    {
      title: 'Date',
      dataIndex: 'date',
      key: 'date',
      render: (date: string) => <Text>{dayjs(date).format('DD/MM/YYYY HH:mm')}</Text>
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status: string, record: any) => (
        <Space>
          <Tag color={status === 'ACTIVE' ? 'success' : 'error'}>{status}</Tag>
          <Tooltip
            overlayInnerStyle={{
              backgroundColor: isDarkMode ? '#000' : '#fff',
              color: isDarkMode ? '#fff' : '#000'
            }}
            title={
              <div className="space-y-2">
                <div>
                  <Text style={{ color: isDarkMode ? '#a3a3a3' : '#666' }}>Created by:</Text>
                  <Text strong style={{ color: isDarkMode ? '#fff' : '#000' }} className="ml-2">
                    {record.createdBy?.name}
                  </Text>
                </div>
                <div>
                  <Text style={{ color: isDarkMode ? '#a3a3a3' : '#666' }}>Created at:</Text>
                  <Text strong style={{ color: isDarkMode ? '#fff' : '#000' }} className="ml-2">
                    {dayjs(record.createdAt).format('DD/MM/YYYY HH:mm')}
                  </Text>
                </div>
                {record.status === 'VOID' && (
                  <>
                    <div>
                      <Text style={{ color: isDarkMode ? '#a3a3a3' : '#666' }}>Voided by:</Text>
                      <Text strong style={{ color: isDarkMode ? '#fff' : '#000' }} className="ml-2">
                        {record.voidedBy?.name}
                      </Text>
                    </div>
                    <div>
                      <Text style={{ color: isDarkMode ? '#a3a3a3' : '#666' }}>Voided at:</Text>
                      <Text strong style={{ color: isDarkMode ? '#fff' : '#000' }} className="ml-2">
                        {dayjs(record.voidedAt).format('DD/MM/YYYY HH:mm')}
                      </Text>
                    </div>
                    <div>
                      <Text style={{ color: isDarkMode ? '#a3a3a3' : '#666' }}>Reason:</Text>
                      <Text strong style={{ color: isDarkMode ? '#fff' : '#000' }} className="ml-2">
                        {record.voidingReason}
                      </Text>
                    </div>
                  </>
                )}
              </div>
            }
          >
            <InfoCircleOutlined
              className={`text-lg ${isDarkMode ? 'text-gray-400' : 'text-gray-600'} hover:text-blue-500`}
            />
          </Tooltip>
        </Space>
      )
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record: any) => (
        <Space>
          <Button
            type="text"
            icon={<EyeOutlined />}
            onClick={() => {
              setSelectedInvoice(record)
              setIsDetailsModalVisible(true)
            }}
          />
          {record.status === 'ACTIVE' && (
            <Button
              type="text"
              danger
              icon={<DeleteOutlined />}
              onClick={() => {
                setSelectedInvoice(record)
                setIsVoidModalVisible(true)
              }}
            />
          )}
        </Space>
      )
    }
  ]

  return (
    <>
      <Table
        // virtual={true}
        columns={columns}
        dataSource={data.openingStock || []}
        loading={loading}
        size="middle"
        rowKey="id"
        pagination={{
          position: ['topRight'],
          showPrevNextJumpers: true,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`
        }}
        className="overflow-hidden"
      />

      <Modal
        title={<Text strong>Void Opening Stock</Text>}
        open={isVoidModalVisible}
        onOk={handleVoidInvoice}
        onCancel={() => {
          setIsVoidModalVisible(false)
          setSelectedInvoice(null)
          setVoidReason('')
        }}
        okButtonProps={{ danger: true }}
      >
        <Space direction="vertical" size="large" style={{ width: '100%' }}>
          <div className="space-y-2">
            <div>
              <Text type="secondary">Invoice Number:</Text>
              <Text strong className="ml-2">
                {selectedInvoice?.invoiceNumber}
              </Text>
            </div>
            <div>
              <Text type="secondary">Date:</Text>
              <Text strong className="ml-2">
                {selectedInvoice && dayjs(selectedInvoice.date).format('DD/MM/YYYY HH:mm')}
              </Text>
            </div>
            <div>
              <Text type="secondary">Total Items:</Text>
              <Text strong className="ml-2">
                {selectedInvoice?.items?.length || 0}
              </Text>
            </div>
          </div>

          <div className="border-l-4 border-yellow-400 bg-yellow-50 p-4 dark:bg-yellow-400/20">
            <Text className="text-yellow-700 dark:text-yellow-400">
              Warning: Voiding this opening stock will remove all associated stock entries and
              adjust product quantities. This action cannot be undone.
            </Text>
          </div>

          <Input.TextArea
            placeholder="Enter reason for voiding this opening stock"
            value={voidReason}
            onChange={(e) => setVoidReason(e.target.value)}
            rows={4}
            required
          />
        </Space>
      </Modal>

      <OpeningStockDetailsModal
        visible={isDetailsModalVisible}
        onClose={() => {
          setIsDetailsModalVisible(false)
          setSelectedInvoice(null)
        }}
        openingStock={selectedInvoice}
      />
    </>
  )
}
