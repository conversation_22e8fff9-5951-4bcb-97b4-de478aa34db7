-- CreateEnum
CREATE TYPE "ManualEntryTarget" AS ENUM ('CASH_VAULT', 'SMALL_COUNTER');

-- AlterEnum
ALTER TYPE "LedgerType" ADD VALUE 'ManualEntry';

-- CreateTable
CREATE TABLE "ManualEntry" (
    "id" TEXT NOT NULL,
    "amount" DOUBLE PRECISION NOT NULL,
    "description" TEXT,
    "transactionDate" TIMESTAMP(3) NOT NULL,
    "entryType" "CreditDebit" NOT NULL,
    "partyId" TEXT,
    "bankId" TEXT,
    "targetType" "ManualEntryTarget",
    "ledgerId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "createdById" TEXT NOT NULL,
    "isDeleted" BOOLEAN NOT NULL DEFAULT false,
    "deletedAt" TIMESTAMP(3),
    "deletedById" TEXT,
    "deletionReason" TEXT,

    CONSTRAINT "ManualEntry_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "ManualEntry_ledgerId_key" ON "ManualEntry"("ledgerId");

-- CreateIndex
CREATE INDEX "ManualEntry_partyId_idx" ON "ManualEntry"("partyId");

-- CreateIndex
CREATE INDEX "ManualEntry_bankId_idx" ON "ManualEntry"("bankId");

-- CreateIndex
CREATE INDEX "ManualEntry_targetType_idx" ON "ManualEntry"("targetType");

-- CreateIndex
CREATE INDEX "ManualEntry_transactionDate_idx" ON "ManualEntry"("transactionDate");

-- CreateIndex
CREATE INDEX "ManualEntry_entryType_idx" ON "ManualEntry"("entryType");

-- CreateIndex
CREATE INDEX "ManualEntry_isDeleted_idx" ON "ManualEntry"("isDeleted");

-- AddForeignKey
ALTER TABLE "ManualEntry" ADD CONSTRAINT "ManualEntry_partyId_fkey" FOREIGN KEY ("partyId") REFERENCES "Party"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ManualEntry" ADD CONSTRAINT "ManualEntry_bankId_fkey" FOREIGN KEY ("bankId") REFERENCES "Banks"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ManualEntry" ADD CONSTRAINT "ManualEntry_ledgerId_fkey" FOREIGN KEY ("ledgerId") REFERENCES "Ledger"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ManualEntry" ADD CONSTRAINT "ManualEntry_createdById_fkey" FOREIGN KEY ("createdById") REFERENCES "Admin"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ManualEntry" ADD CONSTRAINT "ManualEntry_deletedById_fkey" FOREIGN KEY ("deletedById") REFERENCES "Admin"("id") ON DELETE SET NULL ON UPDATE CASCADE;
