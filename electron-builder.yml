appId: com.sj-lace.app
productName: sj-lace
directories:
  buildResources: build
  output: dist
files:
  - out/**/*
  - '!**/.vscode/*'
  - '!src/*'
extraResources:
  - from: 'node_modules/.prisma/client'
    to: 'node_modules/.prisma/client'
  - from: 'node_modules/@prisma/client'
    to: 'node_modules/@prisma/client'
  - from: 'node_modules/@prisma/engines'
    to: 'node_modules/@prisma/engines'
  - from: 'prisma'
    to: 'prisma'
  - from: 'config/production.json'
    to: 'config/production.json'
asar: true
asarUnpack:
  - 'node_modules/.prisma/**/*'
  - 'node_modules/@prisma/**/*'
  - 'prisma/**/*'
win:
  executableName: sj-lace
nsis:
  artifactName: ${name}-${version}-setup.${ext}
  shortcutName: ${productName}
  uninstallDisplayName: ${productName}
  createDesktopShortcut: always
mac:
  entitlementsInherit: build/entitlements.mac.plist
  extendInfo:
    - NSCameraUsageDescription: Application requests access to the device's camera.
    - NSMicrophoneUsageDescription: Application requests access to the device's microphone.
    - NSDocumentsFolderUsageDescription: Application requests access to the user's Documents folder.
    - NSDownloadsFolderUsageDescription: Application requests access to the user's Downloads folder.
  notarize: false
dmg:
  artifactName: ${name}-${version}.${ext}
linux:
  target:
    - AppImage
    - snap
    - deb
  maintainer: Kaleemullah.com
  category: Utility
appImage:
  artifactName: ${name}-${version}.${ext}
npmRebuild: false
# publish:
#   provider: generic
#   url: https://example.com/auto-updates
