import { useState } from 'react'
import { <PERSON>, <PERSON><PERSON>, Tooltip } from 'antd'
import { FileTextOutlined, RightOutlined } from '@ant-design/icons'
import { Report, ReportListProps } from '../types'
import ReportModal from './ReportModal'
import { reportsList } from '../data'
import { useTheme } from '@/renderer/contexts'

const ReportList = ({ searchQuery }: ReportListProps) => {
  const { isDarkMode } = useTheme()
  const [selectedReport, setSelectedReport] = useState<Report | null>(null)

  const filteredReports = reportsList.filter(
    (report) =>
      report.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      report.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      report.category.toLowerCase().includes(searchQuery.toLowerCase())
  )

  const handleGenerateReport = (report: Report) => {
    setSelectedReport(report)
  }

  return (
    <>
      <List
        itemLayout="horizontal"
        dataSource={filteredReports}
        renderItem={(report) => (
          <List.Item
            className={`rounded-lg p-4 transition-all duration-300 ${
              isDarkMode ? 'hover:bg-indigo-900/20' : 'hover:bg-indigo-50'
            }`}
          >
            <List.Item.Meta
              avatar={
                <div
                  className={`rounded-lg p-2 ${isDarkMode ? 'bg-indigo-900/50' : 'bg-indigo-100'}`}
                >
                  <FileTextOutlined className="text-xl text-indigo-500" />
                </div>
              }
              title={
                <div className="flex items-center justify-between">
                  <span className={isDarkMode ? 'text-white' : 'text-gray-800'}>
                    {report.title}
                  </span>
                  <Tooltip title="Configure and generate report">
                    <Button
                      type="text"
                      icon={<RightOutlined />}
                      onClick={() => handleGenerateReport(report)}
                      className={
                        isDarkMode
                          ? 'text-indigo-400 hover:text-indigo-300'
                          : 'text-indigo-600 hover:text-indigo-500'
                      }
                    >
                      Generate Report
                    </Button>
                  </Tooltip>
                </div>
              }
              description={
                <div>
                  <p className={`m-0 text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                    {report.category}
                  </p>
                  <p className={`mt-2 text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                    {report.description}
                  </p>
                </div>
              }
            />
          </List.Item>
        )}
      />

      <ReportModal
        report={selectedReport}
        open={!!selectedReport}
        onClose={() => setSelectedReport(null)}
      />
    </>
  )
}

export default ReportList
