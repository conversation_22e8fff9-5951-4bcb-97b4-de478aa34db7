import { IRequest } from '../../common';
import { partyService } from '../services';
import { PartyType } from '@prisma/client';
import { GetPartiesByTypeParams } from '@/common/types/party';

interface UpdatePartyData {
    contact?: string;
    address?: string;
    phoneNumber?: string;
}

class PartyController {
    async createParty(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { name, type, contact, address, phoneNumber, openingBalance, createdById } = req.body ?? {};

        if (!name || !type) {
            throw new Error("Name and type are required for party creation");
        }

        return await partyService.createParty({ name, type, contact, address, phoneNumber, openingBalance, createdById });
    }

    async getParty(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { id } = req.params ?? {};
        if (!id) throw new Error("Party ID is required");

        return await partyService.getPartyById(id);
    }

    async updateParty(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { id } = req.params ?? {};
        const updateData: UpdatePartyData = req.body ?? {};

        if (!id) throw new Error("Party ID is required");

        return await partyService.updateParty(id, updateData);
    }

    async deleteParty(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { id } = req.params ?? {};
        if (!id) throw new Error("Party ID is required");

        return await partyService.deleteParty(id);
    }

    async getParties(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { page = 1, limit = 10, search, type } = req.query ?? {};
        return await partyService.getParties({ page, limit, search, type });
    }

    async getPartiesByBalance(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { page = 1, limit = 10, balanceType } = req.query ?? {};
        return await partyService.getPartiesByBalance({ page, limit, balanceType });
    }

    async getPartiesForSelect(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { type } = req.query ?? {};
        return await partyService.getPartiesForSelect(type as PartyType | undefined);
    }

    async getPartiesByTypeForPDF(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const params = req.query as GetPartiesByTypeParams;
        return await partyService.getPartiesByTypeForPDF(params);
    }
}

export const partyController = new PartyController();