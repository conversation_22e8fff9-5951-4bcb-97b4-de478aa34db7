import { App, Card, Statistic } from 'antd'
import { useEffect, useState } from 'react'
import { useApi } from '@/renderer/hooks'
import { bankApi, smallCounterApi, cashVaultApi } from '@/renderer/services'
import { formatCurrency } from '@/renderer/utils'
import { useTheme } from '@/renderer/contexts'

interface PaymentDetailsCardProps {
  source: 'SMALL_COUNTER' | 'CASH_VAULT' | 'BANK' | null
  selectedBank: string | null
  paidAmount: number
}

export const PaymentDetailsCard = ({
  source,
  selectedBank,
  paidAmount
}: PaymentDetailsCardProps) => {
  const [balance, setBalance] = useState<number | null>(null)
  const [bankName, setBankName] = useState('')
  const [isVaultInitialized, setIsVaultInitialized] = useState(true)
  const { isDarkMode } = useTheme()

  const { message } = App.useApp()

  const fetchBalance = async () => {
    switch (source) {
      case 'SMALL_COUNTER':
        const cashResponse = await smallCounterApi.getCurrentBalance()
        if (cashResponse.error.error || cashResponse.data.error) {
          message.error(cashResponse.error.message || cashResponse.data.error.message)
          setIsVaultInitialized(false)
          return
        }
        setBalance(cashResponse.data.data)
        break

      case 'CASH_VAULT':
        const vaultResponse = await cashVaultApi.getCurrentBalance()
        if (vaultResponse.error.error || vaultResponse.data.error) {
          message.error(vaultResponse.error.message || vaultResponse.data.error.message)
          setIsVaultInitialized(false)
          return
        }
        setBalance(vaultResponse.data.data)
        break

      case 'BANK':
        if (selectedBank) {
          const bankResponse = await bankApi.getBankById(selectedBank)
          if (bankResponse.error.error || bankResponse.data.error) {
            message.error(bankResponse.error.message || bankResponse.data.error.message)
            setIsVaultInitialized(false)
            return
          }
          setBankName(bankResponse.data.data.name)
          setBalance(bankResponse.data.data.balance)
        }
        break
    }
  }

  useEffect(() => {
    if (source) {
      fetchBalance()
    } else {
      setBalance(null)
      setBankName('')
    }
  }, [source, selectedBank])

  if (!source || (source === 'BANK' && !selectedBank)) return null

  const isBalanceInsufficient = balance !== null && paidAmount > balance

  return (
    <Card
      className={`mb-4 overflow-hidden transition-all duration-300 ${
        isDarkMode
          ? 'bg-gradient-to-br from-slate-800 to-slate-900'
          : 'bg-gradient-to-br from-white to-gray-50'
      } ${isBalanceInsufficient ? 'border-red-500' : ''}`}
    >
      <Statistic
        title={
          source === 'CASH_VAULT' && !isVaultInitialized
            ? 'Cash Vault Status'
            : source === 'BANK'
              ? `${bankName}'s Balance:`
              : `${source.charAt(0).toUpperCase() + source.slice(1)} Balance`
        }
        value={
          source === 'CASH_VAULT' && !isVaultInitialized
            ? 'Not Initialized'
            : formatCurrency(balance || 0)
        }
        valueStyle={{ color: isBalanceInsufficient ? '#ff4d4f' : undefined }}
      />
    </Card>
  )
}
