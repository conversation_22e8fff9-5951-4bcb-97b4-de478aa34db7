import { http } from './http';
import { Channels } from '@/common/constants';
import { TimeRange } from '@/common/types/dashBoard';

export const getDailySalesOverview = async () => {
    return await http.get(Channels.GET_DAILY_SALES_OVERVIEW);
}

export const getSalesTimeline = async (data: TimeRange) => {
    return await http.get(Channels.GET_SALES_TIMELINE, { body: data });
}

export const getTopProducts = async (data: TimeRange) => {
    return await http.get(Channels.GET_TOP_PRODUCTS, { body: data });
}

export const getLowStockProducts = async () => {
    return await http.get(Channels.GET_LOW_STOCK_PRODUCTS);
}

export const getStockValueOverview = async () => {
    return await http.get(Channels.GET_STOCK_VALUE_OVERVIEW);
}

export const getCashFlowOverview = async () => {
    return await http.get(Channels.GET_CASH_FLOW_OVERVIEW);
}

export const getPartyOverview = async () => {
    return await http.get(Channels.GET_PARTY_OVERVIEW);
}

export const getSalesDistribution = async (data: TimeRange) => {
    return await http.get(Channels.GET_SALES_DISTRIBUTION, { body: data });
}

export const getPaymentMethodDistribution = async (data: TimeRange) => {
    return await http.get(Channels.GET_PAYMENT_METHOD_DISTRIBUTION, { body: data });
}