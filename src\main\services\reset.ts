import { prisma } from '../db'

class ResetService {
    // Ledger
    async clearLedger() {
        await prisma.ledger.deleteMany()
    }

    // Sales
    async clearStockEntries() {
        await prisma.stockEntry.deleteMany()
    }

    async clearWalkInSaleItems() {
        await prisma.walkInSaleItem.deleteMany()
    }

    async clearSaleItems() {
        await prisma.saleItem.deleteMany()
    }

    async clearWalkInSaleInvoices() {
        await prisma.walkInSaleInvoice.deleteMany()
    }

    async clearSaleInvoices() {
        await prisma.saleInvoice.deleteMany()
    }

    // Purchases
    async clearPurchaseItems() {
        await prisma.purchaseItem.deleteMany()
    }

    async clearStock() {
        await prisma.stock.deleteMany()
    }

    async clearPurchaseInvoices() {
        await prisma.purchaseInvoice.deleteMany()
    }

    // Products & Categories
    async clearProducts() {
        await prisma.product.deleteMany()
    }

    async clearCategories() {
        await prisma.category.deleteMany()
    }

    // Payments & Banks
    async clearPayments() {
        await prisma.payments.deleteMany()
    }

    async clearBanks() {
        await prisma.banks.deleteMany()
    }

    // Cash Management
    async clearSmallCounter() {
        await prisma.smallCounter.deleteMany()
    }

    async clearCashVault() {
        await prisma.cashVault.deleteMany()
    }

    // Expenses
    async clearExpenses() {
        await prisma.expense.deleteMany()
    }

    // Invoice Numbers
    async clearInvoiceNumbers() {
        await prisma.invoiceNumber.deleteMany()
    }

    // Parties
    async clearParties() {
        await prisma.party.deleteMany()
    }

    // Admin
    async clearAdmins() {
        await prisma.admin.deleteMany()
    }

    // Clear All Data
    async clearAllData() {
        // Clear in order of dependencies
        await this.clearLedger()
        await this.clearStockEntries()
        await this.clearWalkInSaleItems()
        await this.clearSaleItems()
        await this.clearWalkInSaleInvoices()
        await this.clearSaleInvoices()
        await this.clearPurchaseItems()
        await this.clearStock()
        await this.clearPurchaseInvoices()
        await this.clearProducts()
        await this.clearCategories()
        await this.clearPayments()
        await this.clearBanks()
        await this.clearSmallCounter()
        await this.clearCashVault()
        await this.clearExpenses()
        await this.clearInvoiceNumbers()
        await this.clearParties()
        await this.clearAdmins()
    }
}

export const resetService = new ResetService()
