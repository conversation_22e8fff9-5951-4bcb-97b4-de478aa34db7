import React, { useState } from 'react'
import { Button, Table } from 'antd'

const dataSource = [
  {
    key: '1',
    firstName: '<PERSON>',
    lastName: '<PERSON>',
    email: '<EMAIL>,',
    action:(
      <Button type='primary' >Update</Button>
    )
  },

]
// Columns configuration
const columns = [
  { title: '# First Name', dataIndex: 'firstName', key: 'firstName' },
  { title: 'Last Name', dataIndex: 'lastName', key: 'lastName' },
  { title: 'Email', dataIndex: 'email', key: 'email' },
  { title: 'Action', dataIndex: 'action', key: 'action' },
]

const Usertable = () => {
  const [selectedRowKeys, setSelectedRowKeys] = useState([])

  // Handle row selection
  const onSelectChange = (newSelectedRowKeys: any) => {
    setSelectedRowKeys(newSelectedRowKeys)
  }

  // Config for row selection
  const rowSelection = {
    selectedRowKeys,
    onChange: onSelectChange
  }

  return <Table rowSelection={rowSelection} columns={columns} dataSource={dataSource} scroll={{ x: 'max-content' }} />
}

export default Usertable
