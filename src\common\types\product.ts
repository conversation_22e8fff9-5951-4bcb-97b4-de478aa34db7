export interface UpdateProductData {
    salePrice?: number;
    minStockLevel?: number;
    nature?: string;
    tag?: string;
}

export interface ProductDetails {
    id: string;
    name: string;
    productId: string;
    tag?: string;
    nature?: string;
    minStockLevel?: number;
    salePrice: number;
    category: {
        name: string;
        prefix: string;
    };
    stockValue?: {
        totalPurchaseValue: number;
        averagePurchasePrice: number;
        totalQuantity: number;
    };
}

export interface GetProductsParams {
    page: number;
    limit: number;
    search?: string;
}

export interface CreateProductData {
    name: string;
    categoryId: string;
    productId: string;
    nature?: string;
    tag?: string;
    minStockLevel?: number;
    salePrice: number;
}

export interface ProductQuantityReportParams {
    categoryId?: string;
    sortBy?: 'name' | 'quantity' | 'value';
    sortOrder?: 'asc' | 'desc';
}

export interface ProductQuantityReportItem {
    id: string;
    name: string;
    productId: string;
    tag?: string;
    nature?: string;
    category: {
        name: string;
    };
    quantityInStock: number;
    minStockLevel?: number;
    salePrice: number;
    purchaseValue: number;
    averagePurchasePrice: number;
}

export interface ProductQuantityReportResponse {
    products: ProductQuantityReportItem[];
    summary: {
        totalProducts: number;
        totalValue: number;
        lowStockItems: number;
        outOfStockItems: number;
    };
}
