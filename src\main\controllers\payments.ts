import { CreatePaymentData, PaymentDateRangeParams, IRequest, LocationTransferData, PartyTransferData } from '../../common';
import { paymentService } from '../services';



class PaymentController {
    async createPayment(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const data = req.body as CreatePaymentData;

        if (!data.amount) throw new Error('Amount is required');
        if (!data.source) throw new Error('Payment source is required');
        if (!data.description) throw new Error('Description is required');
        if (!data.createdById) throw new Error('Creator ID is required');

        if (data.source === 'BANK' && !data.locationId) {
            throw new Error('Source bank ID is required');
        }

        if ((data.source === 'BANK') && !data.locationId) {
            throw new Error('Destination ID is required');
        }

        return await paymentService.createPayment({
            ...data,
            date: new Date(data.date)
        });
    }

    async getPaymentById(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { id } = req.params ?? {};
        if (!id) throw new Error('Payment ID is required');
        return await paymentService.getPaymentById(id);
    }

    async getPayments(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { startDate, endDate, page = 1, limit = 10, type, status, partyId } = req.query ?? {};
        return await paymentService.getPayments({
            startDate: startDate ? new Date(startDate) : undefined,
            endDate: endDate ? new Date(endDate) : undefined,
            page,
            limit,
            type,
            status,
            partyId
        });
    }

    // TODO: remove this get payments by date range function it is useless and wont be used
    async getPaymentsByDateRange(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { startDate, endDate, page = 1, limit = 10 } = req.query as PaymentDateRangeParams;

        return await paymentService.getPaymentsByDateRange(req.query as PaymentDateRangeParams);
    }

    async voidPayment(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { id, adminId, reason } = req.body ?? {};

        if (!id) throw new Error('Payment ID is required');
        if (!adminId) throw new Error('Admin ID is required');
        if (!reason) throw new Error('Void reason is required');

        return await paymentService.voidPayment(id, adminId, reason);
    }

    async transferBetweenLocations(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const data = req.body as LocationTransferData;
        console.log(data);

        if (!data.amount) throw new Error('Amount is required');
        if (!data.fromLocation) throw new Error('Source location is required');
        if (!data.toLocation) throw new Error('Destination location is required');
        if (!data.createdById) throw new Error('Creator ID is required');
        if (!data.description) throw new Error('Description is required');

        if (data.fromLocation === 'BANK' && !data.fromLocationId) {
            throw new Error('Source bank ID is required');
        }

        if (data.toLocation === 'BANK' && !data.toLocationId) {
            throw new Error('Destination bank ID is required');
        }

        return await paymentService.transferBetweenLocations({
            ...data,
            date: new Date(data.date)
        });
    }

    async transferBetweenParties(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const data = req.body as PartyTransferData;

        if (!data.amount) throw new Error('Amount is required');
        if (!data.fromPartyId) throw new Error('Source party is required');
        if (!data.toPartyId) throw new Error('Destination party is required');
        if (!data.createdById) throw new Error('Creator ID is required');
        if (!data.description) throw new Error('Description is required');

        return await paymentService.transferBetweenParties({
            ...data,
            date: new Date(data.date)
        });
    }

    async getTransfers(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { page = 1, limit = 10, startDate, endDate, fromLocation, toLocation, status } = req.query ?? {};

        return await paymentService.getTransfers({
            page,
            limit,
            startDate: startDate ? new Date(startDate) : undefined,
            endDate: endDate ? new Date(endDate) : undefined,
            fromLocation,
            toLocation,
            status
        });
    }

    async voidTransfer(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { id, adminId, reason } = req.body ?? {};

        if (!id) throw new Error('Transfer ID is required');
        if (!adminId) throw new Error('Admin ID is required');
        if (!reason) throw new Error('Void reason is required');

        return await paymentService.voidTransfer({ id, adminId, reason });
    }
}

export const paymentController = new PaymentController();