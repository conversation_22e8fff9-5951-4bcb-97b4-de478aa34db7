import { IRequest } from '@/common/types';
import { openingStockService } from '../services';

class OpeningStockController {

    async getOpeningStock(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const openingStock = await openingStockService.getOpeningStock();
        return openingStock;
    }

    async addOpeningStock(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { items, adminId } = req.body ?? {};
        if (!adminId) throw new Error('Admin ID is required');
        if (!items) throw new Error('Items are required');
        const openingStock = await openingStockService.addOpeningStock(items, adminId);
        return openingStock;
    }
}

export const openingStockController = new OpeningStockController();

