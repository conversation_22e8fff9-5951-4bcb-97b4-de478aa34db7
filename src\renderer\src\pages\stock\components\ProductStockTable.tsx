import { Table } from 'antd'
import { formatDate, formatCurrency } from '@/renderer/utils'
import type { TableProps } from 'antd'

export interface StockEntry {
  id: string
  saleItem?: {
    saleInvoice: {
      invoiceNumber: string
      date: string
    }
  }
  WalkInSaleItem?: {
    saleInvoice: {
      invoiceNumber: string
      date: string
    }
  }
}

export interface Stock {
  id: string
  purchasePrice: number
  quantity: number
  status: 'IN_STOCK' | 'SOLD_OUT'
  createdAt: Date
  updatedAt: Date
  vendor: {
    id: string
    name: string
  }
  purchaseInvoice: {
    id: string
    invoiceNumber: string
    date: string
    status: string
  }
  StockEntry: StockEntry[]
}

interface ProductStockTableProps {
  data: Stock[] | undefined
  loading: boolean
  pagination: TableProps['pagination']
}

export const ProductStockTable = ({ data, loading, pagination }: ProductStockTableProps) => {
  const columns = [
    {
      title: 'Sr. No.',
      dataIndex: 'serialNumber',
      key: 'serialNumber',
      render: (text: string, record: Stock, index: number) => index + 1
    },
    {
      title: 'Invoice Number',
      dataIndex: ['purchaseInvoice', 'invoiceNumber'],
      key: 'invoiceNumber'
    },
    {
      title: 'Vendor',
      dataIndex: ['vendor', 'name'],
      key: 'vendorName'
    },
    {
      title: 'Purchase Price',
      dataIndex: 'purchasePrice',
      key: 'purchasePrice',
      render: (price: number) => formatCurrency(price)
    },
    {
      title: 'Quantity',
      dataIndex: 'quantity',
      key: 'quantity'
    },
    {
      title: 'Purchase Date',
      dataIndex: ['purchaseInvoice', 'date'],
      key: 'purchaseDate',
      render: (date: string) => formatDate(date)
    }
  ]

  return (
    <Table
      columns={columns}
      dataSource={data}
      loading={loading}
      pagination={pagination}
      rowKey="id"
      sticky
      virtual
      expandable={{
        expandedRowRender: (record) => (
          <Table
            columns={[
              {
                title: 'Sale Invoice',
                key: 'saleInvoice',
                render: (_, entry: StockEntry) => {
                  const invoice = entry.saleItem?.saleInvoice || entry.WalkInSaleItem?.saleInvoice
                  return invoice ? `#${invoice.invoiceNumber} (${formatDate(invoice.date)})` : 'N/A'
                }
              }
            ]}
            dataSource={record.StockEntry}
            pagination={false}
            rowKey="id"
          />
        ),
        rowExpandable: (record) => record.StockEntry?.length > 0
      }}
    />
  )
}
