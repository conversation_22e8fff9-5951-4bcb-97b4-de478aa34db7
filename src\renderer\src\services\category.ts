import { http } from './http';
import { Channels } from '@/common/constants';

interface CreateCategoryData {
    name: string;
    description?: string;
}


export const createCategory = async (data: CreateCategoryData) => {
    return await http.post(Channels.CREATE_CATEGORY, {
        body: data
    });
}

export const getAllCategories = async () => {
    return await http.get(Channels.GET_ALL_CATEGORIES);
}

export const getCategoriesForSelect = async () => {
    return await http.get(Channels.GET_CATEGORIES_FOR_SELECT);
}

export const generateProductId = async (categoryId: string) => {
    return await http.get(Channels.GENERATE_PRODUCT_ID, {
        params: { categoryId }
    });
}

export const deleteCategory = async (id: string) => {
    return await http.delete(Channels.DELETE_CATEGORY, {
        params: { id }
    });
}



