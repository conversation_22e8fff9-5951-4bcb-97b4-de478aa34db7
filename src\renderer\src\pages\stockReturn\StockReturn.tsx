import { Tabs, Card } from 'antd'
import { useTheme } from '@/renderer/contexts'
import { AddStockReturn, ReturnStockList, AddLegacyStockReturn } from './components'
import { useState } from 'react'

const StockReturn = () => {
  const [refreshTrigger, setRefreshTrigger] = useState(0)

  const { isDarkMode } = useTheme()

  const items = [
    {
      key: 'list',
      label: 'Return Stock List',
      children: <ReturnStockList refreshTrigger={refreshTrigger} />
    },
    {
      key: 'add',
      label: 'Return Stock',
      children: <AddStockReturn setRefreshTrigger={setRefreshTrigger} />
    },
    {
      key: 'legacy',
      label: 'Legacy Returns',
      children: <AddLegacyStockReturn setRefreshTrigger={setRefreshTrigger} />
    }
  ]

  return (
    <Card
      className={`m-6 bg-[length:200%_200%] bg-[center] shadow-lg ${
        isDarkMode
          ? 'bg-gradient-to-bl from-indigo-950 via-black to-indigo-900 shadow-indigo-950'
          : 'bg-gradient-to-bl from-indigo-200 via-white to-indigo-100 shadow-indigo-200'
      }`}
    >
      <Tabs items={items} />
    </Card>
  )
}

export default StockReturn
