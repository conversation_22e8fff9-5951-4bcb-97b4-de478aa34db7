import { IRequest } from '../../common';
import { bankService } from '../services';
import { CreateBankData, GetAllBanksFilter } from '../../common/types/banks';

class BankController {
    async createBank(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const data = req.body as CreateBankData;

        if (!data.adminId) throw new Error('Admin ID is required');
        if (!data.name) throw new Error('Bank name is required');
        if (!data.accountNo) throw new Error('Bank account number is required');

        return await bankService.createBank(data);
    }

    async getBankById(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { id } = req.params ?? {};
        if (!id) throw new Error('Bank ID is required');
        return await bankService.getBankById(id);
    }

    async deleteBank(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { id } = req.params ?? {};
        if (!id) throw new Error('Bank ID is required');
        return await bankService.deleteBank(id);
    }

    async getBanksForSelect(_event: Electron.IpcMainInvokeEvent) {
        return await bankService.getBanksForSelect();
    }

    async getAllBanks(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const data = req.body as GetAllBanksFilter;
        return await bankService.getAllBanks(data);

    }

    async deactivateBank(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { id } = req.params ?? {};
        if (!id) throw new Error('Bank ID is required');
        return await bankService.deactivateBank(id);
    }

    async reactivateBank(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { id } = req.params ?? {};
        if (!id) throw new Error('Bank ID is required');
        return await bankService.reactivateBank(id);
    }
}

export const bankController = new BankController();