import { IRequest } from '../../common';
import { saleInvoiceService } from '../services';
import { GetSaleInvoicesParams, SaleInvoiceFormData, VoidSaleData } from '../../common/types';

class SaleInvoiceController {
    async createWalkInSale(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const data = req.body as SaleInvoiceFormData;

        if (!data.items?.length) throw new Error('Sale items are required');
        if (!data.paymentMethod) throw new Error('Payment method is required');
        if (!data.source) throw new Error('Payment source is required');
        if (data.source === 'BANK' && !data.bankId) {
            throw new Error('Bank ID is required for bank payment');
        }

        return await saleInvoiceService.createWalkInSale(data);
    }

    async createRegisteredSale(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const data = req.body as SaleInvoiceFormData;

        if (!data.customerId) throw new Error('Customer ID is required');
        if (!data.items?.length) throw new Error('Sale items are required');
        if (data.paidAmount > 0 && !data.source) {
            throw new Error('Payment source is required when payment is made');
        }
        if (data.source === 'BANK' && !data.bankId) {
            throw new Error('Bank ID is required for bank payment');
        }

        return await saleInvoiceService.createRegisteredSale(data);
    }

    async voidWalkInSale(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const data = req.body as VoidSaleData;
        if (!data.id || !data.adminId || !data.reason) {
            throw new Error('Missing required fields for voiding sale');
        }
        return await saleInvoiceService.voidWalkInSale(data.id, data.adminId, data.reason);
    }

    async voidRegisteredSale(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const data = req.body as VoidSaleData;
        if (!data.id || !data.adminId || !data.reason) {
            throw new Error('Missing required fields for voiding sale');
        }
        return await saleInvoiceService.voidRegisteredSale(data.id, data.adminId, data.reason);
    }

    async getSaleInvoices(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const data = req.params as GetSaleInvoicesParams;
        return await saleInvoiceService.getSaleInvoices(data);
    }
}

export const saleInvoiceController = new SaleInvoiceController();