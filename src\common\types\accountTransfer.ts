// Request/Response types following the pattern
export interface CreateAccountTransferData {
    description?: string
    amount: number
    transferDate: Date
    fromPartyId: string
    toPartyId: string
    createdById: string
}

export interface VoidAccountTransferParams {
    id: string
    deletedById: string
    deletionReason: string
}

export interface GetAccountTransfersParams {
    page?: number
    pageSize?: number
    startDate?: Date
    endDate?: Date
    fromPartyId?: string
    toPartyId?: string
    accountId?: string // Filter where this party is either sender or receiver
    status?: AccountTransferStatus
    sortOrder?: AccountTransferSortOrder
    search?: string
    amountRange?: {
        fromAmount: number
        toAmount: number
    }
}

export interface AccountTransferItem {
    id: string
    description?: string
    amount: number
    transferDate: Date
    fromPartyId: string
    toPartyId: string
    ledgerId: string // Currently stores FROM party ledger ID (TO party ledger tracked separately)
    createdAt: Date
    updatedAt: Date
    createdById: string
    isDeleted: boolean
    deletedAt?: Date
    deletedById?: string
    deletionReason?: string
    fromParty?: {
        id: string
        name: string
        type: string
    }
    toParty?: {
        id: string
        name: string
        type: string
    }
    createdBy?: {
        id: string
        name: string
    }
    deletedBy?: {
        id: string
        name: string
    }
}

export interface CreateAccountTransferResult {
    accountTransfer: AccountTransferItem
    message: string
}

export interface VoidAccountTransferResult {
    success: boolean
    message: string
}

export interface AccountTransfersResponse {
    accountTransfers: AccountTransferItem[]
    total: number
    page: number
    limit: number
    totalPages: number
}

export enum AccountTransferStatus {
    ACTIVE = 'ACTIVE',
    VOIDED = 'VOIDED',
    ALL = 'ALL'
}

export enum AccountTransferSortOrder {
    NEWEST_FIRST = 'NEWEST_FIRST',
    OLDEST_FIRST = 'OLDEST_FIRST',
    AMOUNT_HIGH_TO_LOW = 'AMOUNT_HIGH_TO_LOW',
    AMOUNT_LOW_TO_HIGH = 'AMOUNT_LOW_TO_HIGH'
}