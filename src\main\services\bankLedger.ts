import { prisma } from '../db';
import { Prisma } from '@prisma/client';
import {
    GetBankLedgerParams,
    BankLedgerResponse,
    BankTransactionSummaryParams,
    BankTransactionSummary,
    DailyBankLedgerParams,
    DailyBankLedger,
    BankReconciliation,
    SearchBankTransactionsParams,
    DailyClosingBalancesParams,
    DailyClosingBalance,
    BankLedgerEntry,
    TransactionSummaryByType
} from '@/common/types';
import { processDateRange } from '../utils/helperFunctions';

class BankLedgerService {
    async getBankLedgerEntries(params: GetBankLedgerParams): Promise<BankLedgerResponse> {
        const {
            page = 1,
            limit = 10,
            bankId,
            startDate,
            endDate,
            creditOrDebit,
            referenceType,
            status
        } = params;

        const where: Prisma.LedgerWhereInput = {
            bankId: bankId || undefined,
            ...(creditOrDebit && { creditOrDebit }),
            ...(referenceType && { referenceType }),
            ...(status && { status }),
            ...(startDate || endDate ? {
                date: processDateRange(startDate, endDate)
            } : {})
        };

        const [entries, total] = await Promise.all([
            prisma.ledger.findMany({
                where,
                include: {
                    bank: {
                        select: { name: true }
                    },
                    createdBy: {
                        select: { name: true }
                    }
                },
                orderBy: { date: 'desc' },
                skip: (page - 1) * limit,
                take: limit
            }),
            prisma.ledger.count({ where })
        ]);

        // Calculate totals
        const totals = entries.reduce(
            (acc, entry) => {
                if (entry.creditOrDebit === 'CREDIT') {
                    acc.credits += entry.amount;
                    acc.net += entry.amount;
                } else {
                    acc.debits += entry.amount;
                    acc.net -= entry.amount;
                }
                return acc;
            },
            { credits: 0, debits: 0, net: 0 }
        );

        // Map entries to match BankLedgerEntry type
        const mappedEntries: BankLedgerEntry[] = entries.map(entry => ({
            id: entry.id,
            date: entry.date,
            amount: entry.amount,
            creditOrDebit: entry.creditOrDebit,
            description: entry.description,
            referenceType: entry.referenceType,
            status: entry.status,
            bankId: entry.bankId!,
            bank: entry.bank ? { name: entry.bank.name } : { name: '' },
            createdBy: { name: entry.createdBy.name }
        }));

        return {
            data: mappedEntries,
            total,
            page,
            totalPages: Math.ceil(total / limit),
            totals
        };
    }

    async getBankTransactionSummary(params: BankTransactionSummaryParams): Promise<BankTransactionSummary> {
        const { bankId, startDate, endDate } = params;

        const where: Prisma.LedgerWhereInput = {
            ...(bankId && { bankId }),
            date: {
                gte: startDate,
                lte: endDate
            },
            status: 'ACTIVE'
        };

        // Get all transactions for the period
        const transactions = await prisma.ledger.findMany({
            where,
            select: {
                amount: true,
                creditOrDebit: true,
                referenceType: true
            }
        });

        // Group by reference type
        const summaryByType = transactions.reduce((acc, trans) => {
            const type = trans.referenceType;
            if (!acc[type]) {
                acc[type] = { referenceType: type, creditTotal: 0, debitTotal: 0, count: 0 };
            }

            if (trans.creditOrDebit === 'CREDIT') {
                acc[type].creditTotal += trans.amount;
            } else {
                acc[type].debitTotal += trans.amount;
            }
            acc[type].count++;
            return acc;
        }, {} as Record<string, TransactionSummaryByType>);

        // Calculate overall totals
        const overall = transactions.reduce(
            (acc, trans) => {
                if (trans.creditOrDebit === 'CREDIT') {
                    acc.totalCredits += trans.amount;
                } else {
                    acc.totalDebits += trans.amount;
                }
                acc.totalCount++;
                return acc;
            },
            { totalCredits: 0, totalDebits: 0, totalCount: 0, netAmount: 0 }
        );

        overall.netAmount = overall.totalCredits - overall.totalDebits;

        return {
            summaryByType: Object.values(summaryByType),
            overall
        };
    }

    async getDailyBankLedger(params: DailyBankLedgerParams): Promise<DailyBankLedger> {
        const { bankId, date } = params;

        const startOfDay = new Date(date);
        startOfDay.setHours(0, 0, 0, 0);
        const endOfDay = new Date(date);
        endOfDay.setHours(23, 59, 59, 999);

        const entries = await prisma.ledger.findMany({
            where: {
                bankId: bankId || undefined,
                date: {
                    gte: startOfDay,
                    lte: endOfDay
                },
                status: 'ACTIVE'
            },
            include: {
                bank: {
                    select: { name: true }
                },
                createdBy: {
                    select: { name: true }
                }
            },
            orderBy: { date: 'asc' }
        });

        const totals = entries.reduce(
            (acc, entry) => {
                if (entry.creditOrDebit === 'CREDIT') {
                    acc.credits += entry.amount;
                    acc.net += entry.amount;
                } else {
                    acc.debits += entry.amount;
                    acc.net -= entry.amount;
                }
                return acc;
            },
            { credits: 0, debits: 0, net: 0 }
        );

        // Map entries to match BankLedgerEntry type
        const mappedEntries: BankLedgerEntry[] = entries.map(entry => ({
            id: entry.id,
            date: entry.date,
            amount: entry.amount,
            creditOrDebit: entry.creditOrDebit,
            description: entry.description,
            referenceType: entry.referenceType,
            status: entry.status,
            bankId: entry.bankId!,
            bank: entry.bank ? { name: entry.bank.name } : { name: '' },
            createdBy: { name: entry.createdBy.name }
        }));

        return {
            date,
            entries: mappedEntries,
            totals
        };
    }

    async reconcileBank(bankId: string): Promise<BankReconciliation> {
        const bank = await prisma.banks.findUnique({
            where: { id: bankId },
            select: { id: true, name: true, balance: true }
        });

        if (!bank) {
            throw new Error('Bank not found');
        }

        const [credits, debits] = await Promise.all([
            prisma.ledger.aggregate({
                where: {
                    bankId,
                    status: 'ACTIVE',
                    creditOrDebit: 'CREDIT'
                },
                _sum: { amount: true }
            }),
            prisma.ledger.aggregate({
                where: {
                    bankId,
                    status: 'ACTIVE',
                    creditOrDebit: 'DEBIT'
                },
                _sum: { amount: true }
            })
        ]);

        const calculatedBalance = (credits._sum.amount || 0) - (debits._sum.amount || 0);
        const difference = bank.balance - calculatedBalance;

        return {
            bankId: bank.id,
            bankName: bank.name,
            currentBalance: bank.balance,
            calculatedBalance,
            isReconciled: difference === 0,
            difference
        };
    }

    async searchBankTransactions(params: SearchBankTransactionsParams): Promise<BankLedgerResponse> {
        const {
            bankId,
            searchQuery,
            transactionType,
            page = 1,
            limit = 10
        } = params;

        const where: Prisma.LedgerWhereInput = {
            bankId: bankId || undefined,
            ...(transactionType && { referenceType: transactionType }),
            description: {
                contains: searchQuery,
                mode: 'insensitive'
            },
            status: 'ACTIVE'
        };

        const [entries, total] = await Promise.all([
            prisma.ledger.findMany({
                where,
                include: {
                    bank: {
                        select: { name: true }
                    },
                    createdBy: {
                        select: { name: true }
                    }
                },
                orderBy: { date: 'desc' },
                skip: (page - 1) * limit,
                take: limit
            }),
            prisma.ledger.count({ where })
        ]);

        const totals = entries.reduce(
            (acc, entry) => {
                if (entry.creditOrDebit === 'CREDIT') {
                    acc.credits += entry.amount;
                    acc.net += entry.amount;
                } else {
                    acc.debits += entry.amount;
                    acc.net -= entry.amount;
                }
                return acc;
            },
            { credits: 0, debits: 0, net: 0 }
        );

        // Map entries to match BankLedgerEntry type
        const mappedEntries: BankLedgerEntry[] = entries.map(entry => ({
            id: entry.id,
            date: entry.date,
            amount: entry.amount,
            creditOrDebit: entry.creditOrDebit,
            description: entry.description,
            referenceType: entry.referenceType,
            status: entry.status,
            bankId: entry.bankId!,
            bank: entry.bank ? { name: entry.bank.name } : { name: '' },
            createdBy: { name: entry.createdBy.name }
        }));

        return {
            data: mappedEntries,
            total,
            page,
            totalPages: Math.ceil(total / limit),
            totals
        };
    }

    async getDailyClosingBalances(params: DailyClosingBalancesParams): Promise<DailyClosingBalance[]> {
        const { bankId, startDate, endDate } = params;

        // Get all transactions for the period
        const transactions = await prisma.ledger.findMany({
            where: {
                ...(bankId && { bankId }),
                date: {
                    gte: startDate,
                    lte: endDate
                },
                status: 'ACTIVE'
            },
            select: {
                date: true,
                amount: true,
                creditOrDebit: true
            },
            orderBy: { date: 'asc' }
        });

        // Group transactions by date
        const dailyTotals = transactions.reduce((acc, trans) => {
            const dateKey = trans.date.toISOString().split('T')[0];
            if (!acc[dateKey]) {
                acc[dateKey] = {
                    credits: 0,
                    debits: 0
                };
            }

            if (trans.creditOrDebit === 'CREDIT') {
                acc[dateKey].credits += trans.amount;
            } else {
                acc[dateKey].debits += trans.amount;
            }
            return acc;
        }, {} as Record<string, { credits: number; debits: number }>);

        // Calculate running balance and create daily entries
        let runningBalance = 0;
        if (bankId) {
            const bank = await prisma.banks.findUnique({
                where: { id: bankId },
                select: { balance: true }
            });
            if (bank) {
                runningBalance = bank.balance;
            }
        }

        return Object.entries(dailyTotals).map(([dateStr, totals]) => ({
            date: new Date(dateStr),
            closingBalance: runningBalance + totals.credits - totals.debits,
            totalCredits: totals.credits,
            totalDebits: totals.debits
        }));
    }
}

export const bankLedgerService = new BankLedgerService();