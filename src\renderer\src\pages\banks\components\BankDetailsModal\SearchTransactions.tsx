import { useEffect, useState } from 'react'
import { Input, Select, Table, Space, Card } from 'antd'
import type { ColumnsType } from 'antd/es/table'
import dayjs from 'dayjs'
import { useApi } from '@/renderer/hooks'
import { bankLedgerApi } from '@/renderer/services'
import {
  BankLedgerEntry,
  BankLedgerResponse,
  SearchBankTransactionsParams
} from '@/common/types/bankLedger'
import { LedgerType } from '@/common/types'

interface SearchTransactionsProps {
  bankId?: string
}

export const SearchTransactions = ({ bankId }: SearchTransactionsProps) => {
  const [filters, setFilters] = useState<SearchBankTransactionsParams>({
    page: 1,
    limit: 10,
    searchQuery: '',
    bankId: bankId
  })

  const { request, data, isLoading } = useApi<BankLedgerResponse, [SearchBankTransactionsParams]>(
    bankLedgerApi.searchBankTransactions
  )

  useEffect(() => {
    if (filters.searchQuery) {
      loadTransactions()
    }
  }, [filters])

  const loadTransactions = async () => {
    await request(filters)
  }

  const columns: ColumnsType<BankLedgerEntry> = [
    {
      title: 'Date',
      dataIndex: 'date',
      key: 'date',
      render: (date) => dayjs(date).format('YYYY-MM-DD')
    },
    {
      title: 'Description',
      dataIndex: 'description',
      key: 'description'
    },
    {
      title: 'Bank',
      dataIndex: ['bank', 'name'],
      key: 'bank'
    },
    {
      title: 'Type',
      dataIndex: 'referenceType',
      key: 'referenceType'
    },
    {
      title: 'Credit/Debit',
      dataIndex: 'creditOrDebit',
      key: 'creditOrDebit'
    },
    {
      title: 'Amount',
      dataIndex: 'amount',
      key: 'amount',
      render: (amount) => `Rs. ${amount.toLocaleString()}`
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status'
    }
  ]

  return (
    <div className="space-y-4">
      <Card>
        <Space size="large">
          <Input.Search
            placeholder="Search transactions..."
            style={{ width: 300 }}
            value={filters.searchQuery}
            onChange={(e) => setFilters((prev) => ({ ...prev, searchQuery: e.target.value }))}
            onSearch={() => loadTransactions()}
            enterButton
          />
          <Select
            placeholder="Transaction Type"
            allowClear
            style={{ width: 200 }}
            value={filters.transactionType}
            onChange={(value) => setFilters((prev) => ({ ...prev, transactionType: value }))}
          >
            {Object.values(LedgerType).map((type) => (
              <Select.Option key={type} value={type}>
                {type}
              </Select.Option>
            ))}
          </Select>
        </Space>
      </Card>

      <Table
        size="small"
        columns={columns}
        dataSource={data?.data}
        loading={isLoading}
        rowKey="id"
        pagination={{
          current: filters.page,
          pageSize: filters.limit,
          total: data?.total,
          onChange: (page) => setFilters((prev) => ({ ...prev, page }))
        }}
        summary={() => (
          <Table.Summary>
            <Table.Summary.Row>
              <Table.Summary.Cell index={0} colSpan={5}>
                Totals
              </Table.Summary.Cell>
              <Table.Summary.Cell index={1}>
                Credits: Rs. {data?.totals.credits.toLocaleString()}
              </Table.Summary.Cell>
              <Table.Summary.Cell index={2}>
                Debits: Rs. {data?.totals.debits.toLocaleString()}
              </Table.Summary.Cell>
              <Table.Summary.Cell index={3}>
                Net: Rs. {data?.totals.net.toLocaleString()}
              </Table.Summary.Cell>
            </Table.Summary.Row>
          </Table.Summary>
        )}
      />
    </div>
  )
}
