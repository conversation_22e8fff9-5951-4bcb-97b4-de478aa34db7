import { useTheme } from '@/renderer/contexts'
import { Button, Card, Space, Statistic, Typography } from 'antd'
import { FaMoneyBillTransfer, FaScaleBalanced } from 'react-icons/fa6'
import { GiReceiveMoney } from 'react-icons/gi'

const { Title } = Typography

interface SmallCounterHeaderProps {
  balance: number
  // onTransfer: () => void
  onReconcile: () => void
  onInitialize: () => void
  isInitialized: boolean
}

export const SmallCounterHeader = ({
  balance,
  // onTransfer,
  onReconcile,
  onInitialize,
  isInitialized
}: SmallCounterHeaderProps) => {
  const { isDarkMode } = useTheme()

  return (
    <Card
      size="small"
      className={`mb-2 bg-[length:200%_200%] bg-[center] shadow-lg ${
        isDarkMode
          ? 'bg-gradient-to-bl from-indigo-950 via-black to-indigo-900 shadow-indigo-950'
          : 'bg-gradient-to-bl from-indigo-200 via-white to-indigo-100 shadow-indigo-200'
      }`}
    >
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-6">
          <Title level={2} className="!mb-0">
            Small Counter
          </Title>
          {isInitialized && (
            <Statistic
              title="Current Balance"
              value={balance}
              precision={2}
              prefix="Rs. "
              valueStyle={{ color: balance >= 0 ? '#3f8600' : '#cf1322' }}
            />
          )}
        </div>

        {isInitialized ? (
          <Button icon={<FaScaleBalanced />} onClick={onReconcile}>
            Reconcile
          </Button>
        ) : (
          <Button type="primary" icon={<GiReceiveMoney />} onClick={onInitialize}>
            Initialize Counter
          </Button>
        )}
      </div>
    </Card>
  )
}
