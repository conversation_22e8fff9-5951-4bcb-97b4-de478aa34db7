import { useEffect, useState } from 'react'
import { Card, Form, Input, Button, message, Spin, Alert, Typography } from 'antd'
import { useDispatch } from 'react-redux'
import { userActions } from '@/renderer/redux'
import { licenseApi } from '@/renderer/services'
import { useNavigate } from 'react-router-dom'
import { App_Routes } from '@/common/constants'
import { useTheme } from '@/renderer/contexts'

const { Text } = Typography

/**
 * Interface defining the structure of license status messages
 */
interface LicenseStatus {
  type: 'error' | 'warning' | 'info' | 'success'
  message: string
  description?: string
}

/**
 * License Component
 * Handles license verification, display, and management
 * Supports various license states including:
 * - No license
 * - Invalid license
 * - Expired license
 * - Valid license with expiration warning
 * - Valid license
 */
const License = () => {
  // Form instance for license key input
  const [form] = Form.useForm()

  // State management
  const [loading, setLoading] = useState(false)
  const [licenseStatus, setLicenseStatus] = useState<LicenseStatus | null>(null)
  const [licenseChecking, setLicenseChecking] = useState(false)

  // Hooks
  const dispatch = useDispatch()
  const navigate = useNavigate()
  const { isDarkMode } = useTheme()

  /**
   * Effect hook to check license status on component mount
   */
  useEffect(() => {
    const checkLicense = async () => {
      setLicenseChecking(true)

      try {
        const response = await licenseApi.getLicense()

        // Add artificial delay for UX
        await new Promise((resolve) => setTimeout(resolve, 1000))

        // Handle server/unexpected errors
        if (response.error?.error) {
          console.error('Failed to check license:', response.error.message)
          setLicenseStatus({
            type: 'error',
            message: 'License Check Failed',
            description:
              response.error.message || 'An unexpected error occurred while checking the license.'
          })
          return
        }

        // Process license verification result
        if (response.data?.data) {
          const verification = response.data.data

          if (!verification.isValid) {
            handleInvalidLicense(verification)
          } else if (verification.license) {
            handleValidLicense(verification)
          }
        }
      } catch (error) {
        console.error('License check failed:', error)
        setLicenseStatus({
          type: 'error',
          message: 'License Check Failed',
          description: 'Failed to verify license status. Please try again.'
        })
      } finally {
        setLicenseChecking(false)
      }
    }

    // Start license check
    checkLicense()

    // Cleanup function
    return () => {
      setLicenseChecking(false)
      setLicenseStatus(null)
    }
  }, [])

  /**
   * Handles invalid license scenarios
   * @param verification - License verification result
   */
  const handleInvalidLicense = (verification: any) => {
    switch (verification.error) {
      case 'No license found':
        setLicenseStatus({
          type: 'info',
          message: 'License Required',
          description: 'Please enter your license key to continue using the application.'
        })
        break
      case 'License has expired':
        setLicenseStatus({
          type: 'error',
          message: 'License Expired',
          description: `Your license expired on ${verification.expiryDate}. Please renew your license to continue.`
        })
        break
      default:
        setLicenseStatus({
          type: 'error',
          message: 'Invalid License',
          description: verification.error || 'Your license is not valid. Please contact support.'
        })
    }
    dispatch(userActions.initLicense(null))
  }

  /**
   * Handles valid license scenarios
   * @param verification - License verification result
   */
  const handleValidLicense = (verification: any) => {
    if (verification.warning) {
      setLicenseStatus({
        type: 'warning',
        message: 'License Warning',
        description: verification.warning
      })
    } else {
      setLicenseStatus({
        type: 'success',
        message: 'License Valid',
        description: `Valid until ${new Date(verification.license.expiryDate).toLocaleDateString()}`
      })
    }

    dispatch(userActions.initLicense(verification.license))
    navigate(App_Routes.DASHBOARD)
  }

  /**
   * Handles license key submission
   * @param values - Form values containing the license key
   */
  const handleSubmit = async (values: { licenseKey: string }) => {
    setLoading(true)
    try {
      const response = await licenseApi.verifyLicense(values.licenseKey)

      if (response.error?.error) {
        setLicenseStatus({
          type: 'error',
          message: 'Verification Failed',
          description: response.error.message || 'Server error occurred'
        })
        return
      }

      const verification = response.data?.data
      if (!verification) {
        setLicenseStatus({
          type: 'error',
          message: 'Invalid Response',
          description: 'Invalid response from server'
        })
        return
      }

      if (!verification.isValid) {
        handleInvalidLicense(verification)
        return
      }

      handleValidLicense(verification)
    } catch (error) {
      console.error('License verification failed:', error)
      setLicenseStatus({
        type: 'error',
        message: 'Verification Failed',
        description: 'An unexpected error occurred. Please try again.'
      })
    } finally {
      setLoading(false)
    }
  }

  // Show loading spinner while checking license
  if (licenseChecking) {
    return (
      <div
        className={`relative flex h-screen items-center justify-center p-4 ${
          isDarkMode ? 'bg-neutral-950' : 'bg-white'
        }`}
      >
        <Card
          className={`w-[400px] bg-[length:200%_200%] bg-[10%_10%] shadow-lg transition-[background-position] duration-500 ease-in-out ${
            isDarkMode
              ? 'bg-gradient-to-br from-blue-950 via-slate-950 to-blue-900 shadow-blue-950'
              : 'bg-gradient-to-br from-sky-200 via-white to-blue-200 shadow-blue-200'
          }`}
        >
          <div className="flex flex-col items-center justify-center py-8">
            <Spin size="large" />
            <Text className="mt-4 text-center">Checking license status...</Text>
            <Text type="secondary" className="mt-2 text-center text-sm">
              Please wait while we verify your license
            </Text>
          </div>
        </Card>
      </div>
    )
  }

  return (
    <div
      className={`relative flex h-screen items-center justify-center p-4 ${
        isDarkMode ? 'bg-neutral-950' : 'bg-white'
      }`}
    >
      {/* License Status Alert */}
      {licenseStatus && (
        <div className="absolute left-1/2 top-8 z-50 w-96 -translate-x-1/2 transform">
          <Alert
            message={licenseStatus.message}
            description={licenseStatus.description}
            type={licenseStatus.type}
            showIcon
            className="shadow-lg"
          />
        </div>
      )}

      <Card
        className={`w-[400px] bg-[length:200%_200%] bg-[10%_10%] shadow-lg transition-[background-position] duration-500 ease-in-out hover:bg-[90%_90%] ${
          isDarkMode
            ? 'bg-gradient-to-br from-blue-950 via-slate-950 to-blue-900 shadow-blue-950'
            : 'bg-gradient-to-br from-sky-200 via-white to-blue-200 shadow-blue-200'
        }`}
      >
        <div className="mb-6 text-center">
          <h1 className="text-2xl font-bold">License Verification</h1>
          <Text type="secondary" className="mt-2 block">
            Please enter your license key to continue
          </Text>
        </div>

        <Form form={form} layout="vertical" onFinish={handleSubmit}>
          <Form.Item
            name="licenseKey"
            rules={[{ required: true, message: 'Please enter your license key' }]}
          >
            <Input.TextArea
              rows={4}
              placeholder="Enter your license key"
              className="font-mono"
              disabled={loading}
            />
          </Form.Item>

          <Button type="primary" htmlType="submit" loading={loading} className="w-full">
            {loading ? 'Verifying License...' : 'Verify License'}
          </Button>
        </Form>
      </Card>
    </div>
  )
}

export default License
