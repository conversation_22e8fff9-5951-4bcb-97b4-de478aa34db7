import { Button, Form, InputNumber, Select, Space, Descriptions, Switch, Checkbox } from 'antd'
import type { SaleItemData } from '@/common/types'
import { useTheme } from '@/renderer/contexts'
import { useBankContext } from '@/renderer/contexts'
import { useState, useEffect } from 'react'

interface Props {
  items: SaleItemData[]
  formData: {
    discountAmount: number
    paidAmount: number
    paymentMethod?: 'CASH' | 'BANK_TRANSFER'
    bankId?: string
    source?: 'SMALL_COUNTER' | 'CASH_VAULT' | 'BANK'
  }
  onChange: (data: Partial<Props['formData']>) => void
  onSubmit: () => void
  loading: boolean
  type: 'REGISTERED' | 'WALK_IN'
}

export const Summary = ({ items, formData, onChange, onSubmit, loading, type }: Props) => {
  const [discountType, setDiscountType] = useState<'AMOUNT' | 'PERCENTAGE'>('AMOUNT')
  const [discountPercentage, setDiscountPercentage] = useState(0)
  const [paymentEnabled, setPaymentEnabled] = useState(false)

  const totalAmount = items.reduce((sum, item) => sum + item.totalQuantity * item.salePrice, 0)
  const netAmount = totalAmount - formData.discountAmount

  useEffect(() => {
    if (type === 'WALK_IN') {
      onChange({
        paidAmount: netAmount,
        paymentMethod: 'CASH',
        source: 'SMALL_COUNTER'
      })
    }
  }, [netAmount])

  const { isDarkMode } = useTheme()
  const { banks } = useBankContext()

  // Detect component unmount
  useEffect(() => {
    return () => {
      console.log('Summary component unmounted')
      // Reset states when component unmounts
      setDiscountType('AMOUNT')
      setDiscountPercentage(0)
      setPaymentEnabled(false)
    }
  }, [])

  // For walk-in sales, ensure paid amount equals net amount
  const handlePaidAmountChange = (value: number | null) => {
    if (type === 'WALK_IN') {
      onChange({
        paidAmount: netAmount,
        paymentMethod: 'CASH',
        source: 'SMALL_COUNTER'
      })
    } else {
      onChange({ paidAmount: value || 0 })
    }
  }

  // Handle discount type change
  const handleDiscountTypeChange = (checked: boolean) => {
    const newType = checked ? 'PERCENTAGE' : 'AMOUNT'
    setDiscountType(newType)
    // Reset values
    setDiscountPercentage(0)
    onChange({ discountAmount: 0 })
  }

  // Handle discount percentage change
  const handleDiscountPercentageChange = (value: number | null) => {
    const percentage = value || 0
    const amount = (totalAmount * percentage) / 100
    setDiscountPercentage(percentage)
    onChange({ discountAmount: amount })
  }

  // Handle discount amount change
  const handleDiscountAmountChange = (value: number | null) => {
    const newDiscountAmount = value === null ? 0 : value
    onChange({
      discountAmount: newDiscountAmount,
      ...(type === 'WALK_IN' && {
        paidAmount: totalAmount - newDiscountAmount,
        paymentMethod: 'CASH',
        source: 'SMALL_COUNTER'
      })
    })
  }

  // Handle payment enabled change
  const handlePaymentEnabledChange = (checked: boolean) => {
    setPaymentEnabled(checked)
    if (!checked) {
      // Reset payment related fields when payment is disabled
      onChange({
        paidAmount: 0,
        paymentMethod: undefined,
        bankId: undefined,
        source: undefined
      })
    }
  }

  return (
    <div className="space-y-6">
      <Descriptions column={2} size="small">
        <Descriptions.Item label="Total Amount">
          {totalAmount.toLocaleString('en-US', { minimumFractionDigits: 2 })}
        </Descriptions.Item>
        <Descriptions.Item label="Net Amount">
          {netAmount.toLocaleString('en-US', { minimumFractionDigits: 2 })}
        </Descriptions.Item>
      </Descriptions>

      <Form layout="vertical" className="flex items-center justify-between">
        <Space size="large" wrap>
          <Space align="baseline">
            <Form.Item
              label={
                <Space>
                  Discount {discountType === 'AMOUNT' ? 'Amount' : 'Percentage'}
                  <Switch
                    checkedChildren="%"
                    unCheckedChildren="$"
                    checked={discountType === 'PERCENTAGE'}
                    onChange={handleDiscountTypeChange}
                  />
                </Space>
              }
              validateStatus={formData.discountAmount <= totalAmount ? 'success' : 'error'}
              help={
                formData.discountAmount > totalAmount &&
                'Discount cannot be greater than total amount'
              }
            >
              {discountType === 'AMOUNT' ? (
                <InputNumber
                  min={0}
                  max={totalAmount}
                  value={formData.discountAmount}
                  onChange={handleDiscountAmountChange}
                  className="w-32"
                  formatter={(value) => value?.toLocaleString('en-US') || ''}
                  parser={(value) => parseFloat(value?.replace(/,/g, '') || '0')}
                />
              ) : (
                <InputNumber
                  min={0}
                  max={100}
                  value={discountPercentage}
                  onChange={handleDiscountPercentageChange}
                  className="w-32"
                  formatter={(value) => `${value}%`}
                  parser={(value) => parseFloat(value?.replace('%', '') || '0')}
                />
              )}
            </Form.Item>
          </Space>

          <>
            {type === 'REGISTERED' && (
              <Form.Item label="Enable Payment">
                <Checkbox
                  checked={paymentEnabled}
                  onChange={(e) => handlePaymentEnabledChange(e.target.checked)}
                />
              </Form.Item>
            )}

            {(paymentEnabled || type === 'WALK_IN') && (
              <>
                <Form.Item label="Paid Amount">
                  <InputNumber
                    min={0}
                    disabled={type === 'WALK_IN'}
                    value={type === 'WALK_IN' ? netAmount : formData.paidAmount}
                    onChange={handlePaidAmountChange}
                    className="w-32"
                    formatter={(value) => (value ? value.toLocaleString('en-US') : '')}
                    parser={(value) => parseFloat(value?.replace(/,/g, '') || '0')}
                  />
                </Form.Item>

                {formData.paidAmount > 0 && (
                  <>
                    <Form.Item label="Payment Method" required className="w-32">
                      <Select
                        value={formData.paymentMethod}
                        onChange={(value) => onChange({ paymentMethod: value })}
                        options={[
                          { label: 'Cash', value: 'CASH' },
                          { label: 'Cheque', value: 'CHEQUE' },
                          { label: 'Bank Transfer', value: 'BANK_TRANSFER' }
                        ]}
                        className="w-32"
                      />
                    </Form.Item>

                    <Form.Item label="Source" required className="w-32">
                      <Select
                        value={formData.source}
                        onChange={(value) => onChange({ source: value })}
                        options={[
                          { label: 'Small Counter', value: 'SMALL_COUNTER' },
                          { label: 'Cash Vault', value: 'CASH_VAULT' },
                          { label: 'Bank', value: 'BANK' }
                        ]}
                        className="w-32"
                      />
                    </Form.Item>

                    {formData.source === 'BANK' && (
                      <Form.Item label="Bank" required className="w-44">
                        <Select
                          value={formData.bankId}
                          onChange={(value) => onChange({ bankId: value })}
                          options={banks}
                          className="w-44"
                        />
                      </Form.Item>
                    )}
                  </>
                )}
              </>
            )}
          </>
        </Space>

        <Space>
          <Button
            type="primary"
            onClick={onSubmit}
            loading={loading}
            disabled={
              items.length === 0 ||
              formData.discountAmount > totalAmount ||
              (type === 'REGISTERED' &&
                paymentEnabled &&
                formData.paidAmount > 0 &&
                (!formData.paymentMethod ||
                  (formData.paymentMethod === 'CASH' && !formData.source) ||
                  (formData.paymentMethod === 'BANK_TRANSFER' && !formData.bankId))) ||
              (type === 'WALK_IN' && formData.paidAmount !== netAmount)
            }
            // disabled={
            //   items.length === 0 ||
            //   formData.discountAmount > totalAmount ||
            //   (type === 'REGISTERED' &&
            //     paymentEnabled &&
            //     (formData.paidAmount > netAmount ||
            //       (formData.paidAmount > 0 &&
            //         (!formData.paymentMethod ||
            //           (formData.paymentMethod === 'CASH' && !formData.source) ||
            //           (formData.paymentMethod === 'BANK_TRANSFER' && !formData.bankId))))) ||
            //   (type === 'WALK_IN' && formData.paidAmount !== netAmount)
            // }
            className={`!border-green-500 !bg-green-500 hover:!border-green-600 hover:!bg-green-600 ${
              isDarkMode
                ? 'disabled:!border-gray-900 disabled:!bg-gray-600 disabled:!text-gray-400'
                : 'disabled:!border-gray-500 disabled:!bg-gray-300 disabled:!text-gray-50'
            }`}
          >
            Create Sale Invoice
          </Button>
        </Space>
      </Form>
    </div>
  )
}
