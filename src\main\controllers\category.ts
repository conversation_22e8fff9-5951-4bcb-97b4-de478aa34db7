import { IRequest } from '../../common';
import { categoryService } from '../services/category';

interface CreateCategoryData {
    name: string;
    description?: string;
}

class CategoryController {

    async createCategory(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const data = req.body as CreateCategoryData;

        if (!data?.name) {
            throw new Error('Category name is required');
        }
        return await categoryService.createCategory(data);
    }

    async getAllCategories(_event: Electron.IpcMainInvokeEvent) {
        return await categoryService.getAllCategories();
    }

    async getCategoriesForSelect(_event: Electron.IpcMainInvokeEvent) {
        return await categoryService.getCategoriesForSelect();
    }

    async generateProductId(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { categoryId } = req.params ?? {};

        if (!categoryId) {
            throw new Error('Category ID is required');
        }
        return await categoryService.generateProductId(categoryId);
    }

    async deleteCategory(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { id } = req.params ?? {};

        if (!id) {
            throw new Error('Category ID is required');
        }
        return await categoryService.deleteCategory(id);
    }

}

export const categoryController = new CategoryController();