import { ReportFormat } from '@/common/types'

export type ViewMode = 'grid' | 'list'

export interface ReportCategory {
    id: string
    name: string
    description: string
    icon: string
    accentColor: string
}

export interface Report {
    id: string
    title: string
    description: string
    icon: string
    category: string
    channel: string
    defaultFormat: ReportFormat
}

export interface ReportCardProps {
    report: Report
    onGenerate: (report: Report) => void
}

export interface ReportGridProps {
    searchQuery: string
}

export interface ReportListProps {
    searchQuery: string
}

export interface ReportModalProps {
    report: Report | null
    open: boolean
    onClose: () => void
} 