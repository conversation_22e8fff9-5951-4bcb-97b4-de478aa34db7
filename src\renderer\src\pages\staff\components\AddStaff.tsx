import { Form, Input, Select, Button, message } from 'antd'
import { useState } from 'react'
import { userApi } from '@/renderer/services'
import type { CreateUserData } from '@/common/types'

interface AddStaffProps {
  onSuccess: () => void
}

export const AddStaff = ({ onSuccess }: AddStaffProps) => {
  const [form] = Form.useForm()
  const [loading, setLoading] = useState(false)

  const handleSubmit = async (values: CreateUserData) => {
    setLoading(true)
    const response = await userApi.createUser(values)
    setLoading(false)

    if (response.error.error || response.data.error) {
      message.error(response.error.message || response.data.error.message)
      return
    }

    message.success('Staff member created successfully')
    form.resetFields()
    onSuccess()
  }

  return (
    <div className="max-w-lg">
      <Form form={form} layout="vertical" onFinish={handleSubmit} requiredMark={false}>
        <Form.Item
          label="Name"
          name="name"
          rules={[{ required: true, message: 'Please enter name' }]}
        >
          <Input placeholder="Enter name" />
        </Form.Item>

        <Form.Item
          label="Username"
          name="username"
          rules={[{ required: true, message: 'Please enter username' }]}
        >
          <Input placeholder="Enter username" />
        </Form.Item>

        <Form.Item
          label="Password"
          name="password"
          rules={[
            { required: true, message: 'Please enter password' },
            { min: 8, message: 'Password must be at least 8 characters' }
          ]}
        >
          <Input.Password placeholder="Enter password" />
        </Form.Item>

        <Form.Item
          label="Role"
          name="role"
          rules={[{ required: true, message: 'Please select role' }]}
        >
          <Select
            placeholder="Select role"
            options={[
              { label: 'Super Admin', value: 'SUPER_ADMIN' },
              { label: 'Admin Staff', value: 'ADMIN_STAFF' },
              { label: 'Staff', value: 'STAFF' }
            ]}
          />
        </Form.Item>

        <Form.Item>
          <Button type="primary" htmlType="submit" loading={loading}>
            Create Staff Member
          </Button>
        </Form.Item>
      </Form>
    </div>
  )
}
