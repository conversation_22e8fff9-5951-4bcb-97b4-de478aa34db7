import { prisma } from './db';
import { hash } from 'bcryptjs';
import { dialog, BrowserWindow } from 'electron';

// Schema initialization status flag to track whether a backup needs to be restored
export let isSchemaInitialized = true;

export async function initializeApp() {
    try {
        // Check if any users exist - this will fail if the schema isn't applied
        const userCount = await prisma.admin.count();

        if (userCount === 0) {
            // Create default admin user
            const defaultPassword = 'dev123'; // This should be changed on first login
            const hashedPassword = await hash(defaultPassword, 10);

            await prisma.admin.create({
                data: {
                    username: 'dev',
                    password: hashedPassword,
                    role: 'DEVE<PERSON>OP<PERSON>',
                    name: 'System Administrator'
                }
            });

            console.log('Default admin user created');
            console.log('Username: admin');
            console.log('Password: admin123');
            console.log('Please change this password after first login');
        }
    } catch (error) {
        console.error('Error initializing schema:', error);
        isSchemaInitialized = false;

        // We don't show the dialog here, but set a flag that the main process can use
        // to determine if we should show a dialog or message in the renderer
    }
}

export function showSchemaErrorDialog(parentWindow?: BrowserWindow) {
    const options = {
        type: 'error' as const,
        title: 'Database Schema Missing',
        message: 'The application database has not been initialized correctly.',
        detail: 'Please use the "Restore from Backup" button on the login screen to restore a database backup with the proper schema.',
        buttons: ['OK']
    };

    if (parentWindow) {
        dialog.showMessageBox(parentWindow, options);
    } else {
        dialog.showMessageBox(options);
    }
}