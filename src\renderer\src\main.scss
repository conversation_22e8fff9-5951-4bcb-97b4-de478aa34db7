@tailwind base;
@tailwind components;
@tailwind utilities;

* {
    padding: 0px;
    margin: 0px;
    box-sizing: border-box;
    --header-height: 70px;

    transition: color 0.1s ease-in-out, background-color 0.3s ease-in-out;
    
    font-family: 'Times New Roman', Times, serif;

    // Transition all in the root, will make all select components buggy thier drpdown will not appear or appear out of bounds of the screen


    // transition: all 0.2s ease-in-out;
    // scroll-behavior: smooth;
    
}

.test {
    background-color: black;
    color: white;
}



// Add this after your existing styles
.ant-table-wrapper {
    .ant-table-tbody {
      > tr {
        transition: transform 0.2s ease-in-out;

        // &:hover {
        //   transform: scale(1.001);
          
        // }
      }
    }
  }
  



// In your global styles file (e.g., App.scss or index.scss)
// .ant-select-dropdown {
//     position: absolute !important;
//   }
  
//   .ant-select-dropdown,
//   .ant-picker-dropdown,
//   .ant-table-filter-dropdown {
//     animation: none !important;
//     transform-origin: center top !important;
//   }
  
//   // Fix for table pagination dropdown specifically
//   .ant-pagination-options {
//     .ant-select-dropdown {
//       position: absolute !important;
//       top: 100% !important;
//       left: 0 !important;
//     }
//   }