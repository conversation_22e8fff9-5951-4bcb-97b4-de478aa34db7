/*
  Warnings:

  - You are about to drop the column `openingBalanceRef` on the `Ledger` table. All the data in the column will be lost.
  - You are about to drop the `OpeningBalance` table. If the table is not empty, all the data it contains will be lost.

*/
-- DropForeignKey
ALTER TABLE "Ledger" DROP CONSTRAINT "Ledger_openingBalanceRef_fkey";

-- DropForeignKey
ALTER TABLE "Ledger" DROP CONSTRAINT "Ledger_partyId_fkey";

-- DropForeignKey
ALTER TABLE "OpeningBalance" DROP CONSTRAINT "OpeningBalance_partyId_fkey";

-- DropIndex
DROP INDEX "Ledger_openingBalanceRef_idx";

-- DropIndex
DROP INDEX "Ledger_openingBalanceRef_key";

-- AlterTable
ALTER TABLE "Ledger" DROP COLUMN "openingBalanceRef",
ALTER COLUMN "partyId" DROP NOT NULL;

-- DropTable
DROP TABLE "OpeningBalance";

-- AddForeignKey
ALTER TABLE "Ledger" ADD CONSTRAINT "Ledger_partyId_fkey" FOREIGN KEY ("partyId") REFERENCES "Party"("id") ON DELETE SET NULL ON UPDATE CASCADE;
