import { useEffect, useState } from 'react'
import { Table, Popconfirm, Tooltip, App } from 'antd'
import { DeleteOutlined, EditOutlined } from '@ant-design/icons'
import { productApi } from '@/renderer/services'
import type { ColumnsType } from 'antd/es/table'
import { formatCurrency } from '@/renderer/utils'
import { FaTrashAlt } from 'react-icons/fa'
import { FaTrash } from 'react-icons/fa'

interface ProductListProps {
  onProductClick: (id: string) => void
  searchQuery: string
  selectedProductId: string | null
  refreshTrigger: number
}

interface ProductData {
  id: string
  name: string
  category: string
  productId: string
  nature?: string
  tag?: string
  minStockLevel?: number
  salePrice: number
}

export const ProductList = ({
  onProductClick,
  searchQuery,
  selectedProductId,
  refreshTrigger
}: ProductListProps) => {
  const [products, setProducts] = useState<ProductData[]>([])
  const [loading, setLoading] = useState(false)

  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0
  })

  const { message } = App.useApp()

  const fetchProducts = async () => {
    setLoading(true)
    if (selectedProductId) {
      // Fetch single product
      const response = await productApi.getProduct(selectedProductId)
      setProducts([response.data.data])
      setPagination({
        ...pagination,
        total: 1
      })
      setLoading(false)
    } else {
      // Fetch filtered products
      const response = await productApi.getProducts({
        page: pagination.current,
        limit: pagination.pageSize,
        search: searchQuery
      })
      setLoading(false)
      console.log(response)
      setProducts(response.data.data.products)
      setPagination({
        ...pagination,
        total: response.data.data.total
      })
    }
  }

  useEffect(() => {
    fetchProducts()
  }, [pagination.current, pagination.pageSize, searchQuery, selectedProductId, refreshTrigger])

  const handleDelete = async (id: string) => {
    // try {
    const response = await productApi.deleteProduct(id)
    // console.log(response)

    if (response.error.error || response.data.error) {
      message.error(response.error.message || response.data.error.message)
      return
    }

    message.success('Product deleted successfully')
    fetchProducts()
    // } catch (error) {
    //   message.error('Failed to delete product')
    // }
  }

  const columns: ColumnsType<ProductData> = [
    {
      title: 'No',
      dataIndex: 'index',
      render: (_, __, index) => index + 1 + (pagination.current - 1) * pagination.pageSize
    },
    {
      title: 'Product ID',
      dataIndex: 'productId',
      key: 'productId'
    },
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name'
    },
    {
      title: 'Category',
      dataIndex: ['category'], // Change this to match the relation name
      key: 'category',
      render: (category) => {
        if (!category) return '-' // Handle null case

        return (
          <div className="flex items-center gap-2">
            <Tooltip title={category.description || 'No description available'}>
              <span className="cursor-help">{category.name}</span>
            </Tooltip>
          </div>
        )
      }
    },
    {
      title: 'Nature',
      dataIndex: 'nature',
      key: 'nature'
    },
    {
      title: 'Quantity',
      dataIndex: 'quantityInStock',
      key: 'quantityInStock',
      render: (quantity) => quantity || '0'
    },

    {
      title: 'Sale Price',
      dataIndex: 'salePrice',
      key: 'salePrice',
      render: (price) => price || '-'
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record) => (
        <div className="action-buttons">
          <EditOutlined
            className="transition-all duration-150 hover:scale-125"
            onClick={() => onProductClick(record.id)}
          />
          <DeleteConfirmPopover handleDelete={handleDelete} record={record} />
        </div>
      )
    }
  ]

  return (
    <Table
      columns={columns}
      dataSource={products}
      rowKey="id"
      loading={loading}
      sticky
      virtual
      pagination={{
        ...pagination,
        showQuickJumper: true,
        position: ['topRight'],
        showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`
      }}
      onChange={(newPagination) => setPagination({ ...pagination, ...newPagination })}
    />
  )
}

function DeleteConfirmPopover({ handleDelete, record }) {
  const [isDeleteIconHovered, setIsDeleteIconHovered] = useState(false)

  return (
    <Popconfirm
      title="Delete Product"
      description="Are you sure you want to delete this product?"
      onConfirm={() => handleDelete(record.id)}
      okText="Yes"
      cancelText="No"
    >
      <div
        onMouseEnter={() => setIsDeleteIconHovered(true)}
        onMouseLeave={() => setIsDeleteIconHovered(false)}
        style={{
          cursor: 'pointer',
          justifyContent: 'center',
          display: 'flex',
          position: 'relative',
          alignItems: 'center',
          marginLeft: 'auto',
          marginRight: 'auto'
        }}
      >
        <FaTrash
          color="#ff4d4f"
          className="absolute transition-all duration-300"
          style={{
            opacity: isDeleteIconHovered ? 0 : 1,
            transform: isDeleteIconHovered ? 'scale(0)' : 'scale(1)'
          }}
        />
        <FaTrashAlt
          color="#ff4d4f"
          className="absolute transition-all duration-300"
          style={{
            opacity: isDeleteIconHovered ? 1 : 0,
            transform: isDeleteIconHovered ? 'scale(1.4)' : 'scale(0)'
          }}
        />
      </div>
    </Popconfirm>
  )
}
