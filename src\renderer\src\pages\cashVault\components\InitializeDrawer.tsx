import { But<PERSON>, Drawer, Form, InputNumber, Space, message } from 'antd'
import { useState } from 'react'
import { FaMoneyBillTransfer } from 'react-icons/fa6'
import { cashVaultApi } from '@/renderer/services'

interface InitializeDrawerProps {
  open: boolean
  onClose: () => void
  onBalanceUpdate: () => void
  userId: string
}

export const InitializeDrawer = ({
  open,
  onClose,
  onBalanceUpdate,
  userId
}: InitializeDrawerProps) => {
  const [form] = Form.useForm()
  const [loading, setLoading] = useState(false)

  const handleInitialize = async (values: { amount: number }) => {
    setLoading(true)
    const response = await cashVaultApi.initializeVault({
      amount: values.amount,
      adminId: userId
    })
    setLoading(false)

    if (response.error.error || response.data.error) {
      message.error(response.error.message || response.data.error.message)
      return
    }

    message.success('Cash vault initialized successfully')
    form.resetFields()
    onBalanceUpdate()
    onClose()
  }

  return (
    <Drawer
      title="Initialize Cash Vault"
      placement="right"
      onClose={onClose}
      open={open}
      width={400}
      extra={
        <Space>
          <Button onClick={onClose}>Cancel</Button>
          <Button
            type="primary"
            icon={<FaMoneyBillTransfer />}
            loading={loading}
            onClick={() => form.submit()}
          >
            Initialize
          </Button>
        </Space>
      }
    >
      <Form form={form} layout="vertical" onFinish={handleInitialize}>
        <Form.Item
          label="Opening Balance"
          name="amount"
          rules={[
            { required: true, message: 'Please enter opening balance' },
            {
              type: 'number',
              min: 0,
              message: 'Opening balance cannot be negative'
            }
          ]}
        >
          <InputNumber
            style={{ width: '100%' }}
            prefix="Rs. "
            precision={2}
            placeholder="Enter opening balance"
          />
        </Form.Item>
      </Form>
    </Drawer>
  )
}
