import { useState, useEffect } from 'react'
import {
  Select,
  Card,
  Divider,
  DatePicker,
  Input,
  Button,
  Table,
  Tooltip,
  message,
  Popconfirm,
  Form,
  InputNumber,
  Checkbox,
  Space,
  Radio,
  Statistic,
  Typography,
  Tag
} from 'antd'
import { ArrowLeftOutlined, PlusOutlined } from '@ant-design/icons'
import { usePartyContext, useTheme } from '@/renderer/contexts'
import { partyApi, purchaseInvoiceApi } from '@/renderer/services'
import { AddStockModal } from './components/AddStockModal'
import dayjs from 'dayjs'
import './AddPurchaseInvoice.scss'
import { FaTrash, FaTrashAlt } from 'react-icons/fa'
import { VendorDetailsCard } from './components/VendorsDetailsCard'
import { PaymentDetailsCard } from './components/PaymentDetailCard'
import { useBankContext } from '@/renderer/contexts/BankContext'
import { useSelector } from 'react-redux'
import { IRootState } from '@/renderer/redux'
import { handleDatePickerValue } from '@/renderer/utils'

type AddPurchaseInvoiceProps = {
  onBack: () => void
  onPurchaseCreated: () => void
}

interface PurchaseInvoiceItem {
  // id: string
  displayName: string
  productId: string
  quantity: number
  purchasePrice: number
  total: number
}

const AddPurchaseInvoice = ({ onBack, onPurchaseCreated }: AddPurchaseInvoiceProps) => {
  const [form] = Form.useForm()
  const { vendors } = usePartyContext()
  const [selectedVendor, setSelectedVendor] = useState<string | null>(null)
  const [items, setItems] = useState<PurchaseInvoiceItem[]>([])
  const [isAddStockModalOpen, setIsAddStockModalOpen] = useState(false)
  const [vendorDetails, setVendorDetails] = useState<any>(null)

  const { banks } = useBankContext()
  const { isDarkMode } = useTheme()

  const user = useSelector((state: IRootState) => state.user.data)

  // Add these state variables after other state declarations
  const [showPayment, setShowPayment] = useState(false)
  const [paymentMethod, setPaymentMethod] = useState<'cash' | 'bank' | 'vault' | null>(null)
  const [selectedBank, setSelectedBank] = useState<string | null>(null)

  // Calculate totals
  const totalPurchaseAmount = items.reduce((sum, item) => sum + item.total, 0)
  const previousDues = vendorDetails?.currentBalance || 0
  const totalWithDues = previousDues + totalPurchaseAmount

  const [balance, setBalance] = useState<number | null>(null)

  // useEffect to set the totalAmount, previousBalance, totalWithDues, and newBalance fields values
  useEffect(() => {
    form.setFieldsValue({
      totalAmount: totalPurchaseAmount,
      previousBalance: previousDues,
      totalWithDues: totalWithDues,
      newBalance: totalWithDues - (form.getFieldValue('paidAmount') || 0) // Changed from subtraction to addition
    })
  }, [totalPurchaseAmount, previousDues, form])

  // Watch paid amount changes
  const handlePaidAmountChange = (value: number | null) => {
    form.setFieldsValue({
      newBalance: totalWithDues - (value || 0)
    })
  }

  const handleAddItem = (item: PurchaseInvoiceItem) => {
    const existingItem = items.find((i) => i.productId === item.productId)
    if (existingItem) {
      message.error('Product already exists in the invoice.')
      return
    }
    setItems((prevItems) => [...prevItems, item])
  }

  const handleRemoveItemFromInvoice = (productId: string) => {
    setItems((prevItems) => prevItems.filter((item) => item.productId !== productId))
  }

  const handleCreateInvoice = async (values: any) => {
    if (!selectedVendor) {
      message.error('Please select a vendor')
      return
    }
    if (items.length === 0) {
      message.error('Please add at least one item to the invoice')
      return
    }

    if (values.enablePayment) {
      if (!values.paidAmount) {
        message.error('Please enter a payment amount')
        return
      }

      if (!values.paymentMethod) {
        message.error('Please select a payment method')
        return
      }

      if (values.paymentMethod === 'bank' && !values.bankId) {
        message.error('Please select a bank')
        return
      }

      if (balance === null || values.paidAmount > balance) {
        message.error('Insufficient balance for payment')
        return
      }
    }

    // remove displayName from items, it will cause error in database as it is not a valid column name
    const apiItems = items.map((item) => {
      const { displayName, ...rest } = item
      return rest
    })

    const response = await purchaseInvoiceApi.createPurchaseInvoice({
      invoiceNumber: values.invoiceNumber,
      vendorId: selectedVendor,
      totalAmount: totalPurchaseAmount,
      paidAmount: values.paidAmount || 0,
      previousBalance: previousDues,
      newBalance: values.newBalance,
      date: handleDatePickerValue(values.date.toDate()),
      paymentMethod: values.enablePayment ? values.paymentMethod : null,
      bankId: values.paymentMethod === 'bank' ? values.bankId : null,
      createdById: user?.id || '',
      items: apiItems
    })

    if (response.error.error || response.data.error) {
      message.error(response.error.message || response.data.error.message)
      return
    }

    console.log('add purchase invoice response', response)
    message.success('Purchase invoice created successfully')
    form.resetFields()
    setItems([])
    setSelectedVendor(null)
    setVendorDetails(null)
    setShowPayment(false)
    setPaymentMethod(null)
    setSelectedBank(null)
    onPurchaseCreated()
  }

  return (
    <Card
      className={`create-purchase-invoice m-6 shadow-lg ${
        isDarkMode
          ? 'bg-gradient-to-bl from-indigo-950 via-black to-indigo-900 shadow-indigo-950'
          : 'bg-gradient-to-bl from-indigo-200 via-white to-indigo-100 shadow-indigo-200'
      }`}
    >
      <Form form={form} onFinish={handleCreateInvoice} layout="vertical">
        <div className="flex flex-row justify-between">
          <Button
            icon={<ArrowLeftOutlined />}
            onClick={() => {
              form.resetFields()
              setItems([])
              setSelectedVendor(null)
              setVendorDetails(null)
              setShowPayment(false)
              setPaymentMethod(null)
              setSelectedBank(null)
              onPurchaseCreated()
              onBack()
            }}
          >
            Back
          </Button>
          <Select
            showSearch
            allowClear
            onClear={() => setVendorDetails(null)}
            placeholder="Select a vendor"
            optionFilterProp="children"
            onChange={setSelectedVendor}
            style={{ width: '50%' }}
            options={vendors}
            filterOption={(input, option) =>
              (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
            }
          />
        </div>
        <Divider />

        <VendorDetailsCard
          selectedVendor={selectedVendor}
          vendorDetails={vendorDetails}
          setVendorDetails={setVendorDetails}
        />

        <Divider />
        <h3>Invoice Data Entry</h3>
        <div className="invoice-data-entry">
          <Form.Item
            name="invoiceNumber"
            rules={[{ required: true, message: 'Please enter invoice number' }]}
            style={{ width: '30%', marginRight: '1%' }}
          >
            <Input placeholder="Invoice Number" />
          </Form.Item>

          <Form.Item
            name="date"
            rules={[{ required: true, message: 'Please select date' }]}
            initialValue={dayjs()}
            style={{ width: '30%', marginRight: '1%' }}
          >
            <DatePicker style={{ width: '100%' }} />
          </Form.Item>

          <Form.Item style={{ marginLeft: 'auto' }}>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => setIsAddStockModalOpen(true)}
              className="!border-green-400 !bg-green-400 hover:!border-green-600 hover:!bg-green-600"
            >
              Add Stock
            </Button>
          </Form.Item>
        </div>
        <Divider />
        <Table
          dataSource={items}
          columns={[
            {
              title: 'Sr. No',
              dataIndex: 'srNo',
              key: 'srNo',
              render: (_, __, index) => index + 1
            },
            { title: 'Product', dataIndex: 'displayName', key: 'displayName' },
            { title: 'Quantity', dataIndex: 'quantity', key: 'quantity' },
            { title: 'Purchase Rate', dataIndex: 'purchasePrice', key: 'purchasePrice' },
            { title: 'Total Amount', dataIndex: 'total', key: 'total' },
            {
              title: 'Actions',
              key: 'actions',
              align: 'center',
              render: (_, record) => (
                <Popconfirm
                  title="Remove Item"
                  description="Are you sure you want to remove this item?"
                  onConfirm={() => handleRemoveItemFromInvoice(record.productId)}
                  okText="Yes"
                  cancelText="No"
                  okButtonProps={{ danger: true }}
                >
                  <span className="delete-icon-wrapper">
                    <FaTrash className="delete-icon" />
                    <FaTrashAlt className="delete-icon-hover" />
                  </span>
                </Popconfirm>
              )
            }
          ]}
          rowKey="productId"
          pagination={false}
        />
        <Divider />

        {/* // Replace your existing invoice-totals div with this: */}
        <div className="flex flex-row space-x-8">
          <PaymentDetailsCard
            paymentMethod={paymentMethod}
            selectedBank={selectedBank}
            paidAmount={form.getFieldValue('paidAmount') || 0}
            balance={balance}
            setBalance={setBalance}
          />

          <div className="flex-1">
            <div className="invoice-totals space-y-4">
              <Form.Item name="totalAmount" label="Total Price of Purchase">
                <InputNumber disabled style={{ width: '100%' }} />
              </Form.Item>

              <Form.Item name="previousBalance" label="Previous Dues">
                <InputNumber disabled style={{ width: '100%' }} />
              </Form.Item>

              <Form.Item name="totalWithDues" label="Total with Dues">
                <InputNumber disabled style={{ width: '100%' }} />
              </Form.Item>

              <Form.Item name="enablePayment" valuePropName="checked">
                <Checkbox onChange={(e) => setShowPayment(e.target.checked)}>
                  Enable Payment
                </Checkbox>
              </Form.Item>

              {showPayment && (
                <div
                  className={`space-y-4 transition-all duration-300 ${showPayment ? 'translate-y-0 opacity-100' : 'translate-y-10 opacity-0'}`}
                >
                  <Form.Item name="paymentMethod">
                    <Radio.Group onChange={(e) => setPaymentMethod(e.target.value)}>
                      <Space direction="vertical">
                        <Radio value="cash">Cash</Radio>
                        <Radio value="vault">Vault</Radio>
                        <Radio value="bank">Bank</Radio>
                      </Space>
                    </Radio.Group>
                  </Form.Item>

                  {paymentMethod === 'bank' && (
                    <Form.Item name="bankId" className="transition-all duration-300">
                      <Select
                        placeholder="Select Bank"
                        options={banks}
                        onChange={setSelectedBank}
                      />
                    </Form.Item>
                  )}

                  {paymentMethod && (
                    <Form.Item
                      name="paidAmount"
                      label="Payment"
                      className="transition-all duration-300"
                    >
                      <InputNumber
                        style={{ width: '100%' }}
                        onChange={handlePaidAmountChange}
                        min={0}
                      />
                    </Form.Item>
                  )}
                </div>
              )}

              <Form.Item name="newBalance" label="New Balance">
                <InputNumber disabled style={{ width: '100%' }} />
              </Form.Item>
            </div>
          </div>
        </div>
        <Form.Item className="flex justify-end">
          <Button
            type="primary"
            htmlType="submit"
            style={{ backgroundColor: '#52c41a', borderColor: '#52c41a' }}
          >
            Confirm Purchase
          </Button>
        </Form.Item>
      </Form>

      <AddStockModal
        open={isAddStockModalOpen}
        onClose={() => setIsAddStockModalOpen(false)}
        onAddItem={handleAddItem}
        invoiceItems={items}
      />
    </Card>
  )
}

export default AddPurchaseInvoice
