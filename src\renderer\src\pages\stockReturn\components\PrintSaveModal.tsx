import React from 'react'
import { Mo<PERSON>, Button, Space } from 'antd'
import { PrinterOutlined, SaveOutlined } from '@ant-design/icons'

interface PrintSaveModalProps {
  open: boolean
  loading: boolean
  onPrint: () => void
  onSave: () => void
  onCancel: () => void
}

const PrintSaveModal: React.FC<PrintSaveModalProps> = ({
  open,
  loading,
  onPrint,
  onSave,
  onCancel
}) => {
  return (
    <Modal
      title="Print or Save Stock Return"
      open={open}
      onCancel={onCancel}
      footer={null}
      centered
    >
      <div className="mb-4">
        <p>Would you like to print or save this stock return document?</p>
      </div>
      <Space className="flex justify-end">
        <Button onClick={onCancel} disabled={loading}>
          Cancel
        </Button>
        <Button icon={<SaveOutlined />} onClick={onSave} type="default" loading={loading}>
          Save
        </Button>
        <Button icon={<PrinterOutlined />} onClick={onPrint} type="primary" loading={loading}>
          Print
        </Button>
      </Space>
    </Modal>
  )
}

export default PrintSaveModal
