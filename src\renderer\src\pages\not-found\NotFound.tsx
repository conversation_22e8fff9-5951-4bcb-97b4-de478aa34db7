import { App_Routes } from '@/common'
// import { IRootState } from "@/redux";
import { Button } from 'antd'
// import { useSelector } from 'react-redux'
import { useNavigate } from 'react-router'

const NotFound = () => {
  const navigate = useNavigate()
  // const user = useSelector((state: IRootState) => state.user.data);
  // const handleGoHome = () => {
  //   if (user) {
  //     navigate(App_Routes.DASHBOARD);
  //     return;
  //   }
  //   navigate(App_Routes.LOGIN);
  // };

  return (
    <div className="flex justify-center items-center h-full w-full">
      <div className="flex flex-col gap-[10px] justify-center items-center">
        <h3> Not Found </h3>
        <Button type="primary" onClick={() => navigate(App_Routes.LOGIN)}>
          Go to Home Page
        </Button>
      </div>
    </div>
  )
}

export default NotFound
