import { useTheme } from '@/renderer/contexts'
import { partyApi } from '@/renderer/services'
import { App, Card, Descriptions, Space, Tag, Typography } from 'antd'
import { useEffect, useState } from 'react'
import { FaUser, FaPhone, FaEnvelope, FaBalanceScale } from 'react-icons/fa'
import { HiCurrencyRupee } from 'react-icons/hi'

const { Text, Title } = Typography

interface CustomerDetailsCardProps {
  formData: {
    customerId?: string
  }
}

export const CustomerDetailsCard = ({ formData }: CustomerDetailsCardProps) => {
  const [customerDetails, setCustomerDetails] = useState<any>(null)
  const [loading, setLoading] = useState(false)
  const [isVisible, setIsVisible] = useState(false)

  const { isDarkMode } = useTheme()

  const { message } = App.useApp()

  useEffect(() => {
    if (formData.customerId) {
      setIsVisible(false)

      setTimeout(() => {
        fetchCustomer()
        setIsVisible(true)
      }, 500)
    } else {
      setIsVisible(false)
    }
  }, [formData.customerId])

  const fetchCustomer = async () => {
    if (!formData.customerId) return

    setLoading(true)
    const response = await partyApi.getParty(formData.customerId)
    setLoading(false)

    if (response.error.error || response.data.error) {
      message.error(response.error.message || response.data.error.message)
      return
    }

    setCustomerDetails(response.data.data)
  }

  return (
    <div
      className={`overflow-hidden transition-[opacity,transform,max-height] duration-500 ${
        isVisible ? 'max-h-60 translate-y-0 opacity-100' : 'max-h-0 -translate-y-4 opacity-0'
      } `}
    >
      <Card
        loading={loading}
        className={`${
          isDarkMode
            ? 'bg-gradient-to-br from-indigo-950 via-black to-indigo-900 shadow-lg shadow-indigo-900'
            : 'bg-gradient-to-br from-indigo-200 via-white to-indigo-100 shadow-lg shadow-indigo-100'
        }`}
        // style={{
        //   borderTop: '4px solid #1890ff'
        // }}
      >
        {customerDetails && (
          <Space direction="vertical" size="middle" className="w-full">
            <div className="flex items-center justify-between">
              <Space>
                <FaUser className="text-2xl text-blue-500" />
                <Title level={4} className="!mb-0">
                  {customerDetails.name}
                </Title>
                <Tag color="blue">{customerDetails.type}</Tag>
              </Space>
              <div className="text-right">
                <Space direction="vertical" size="small">
                  <div className="flex items-center justify-end gap-2">
                    <FaBalanceScale className="text-gray-500" />
                    <Text>Current Balance:</Text>
                    <Text
                      strong
                      className={
                        customerDetails.currentBalance >= 0 ? 'text-green-500' : 'text-red-500'
                      }
                    >
                      <HiCurrencyRupee className="inline" />
                      {customerDetails.currentBalance.toLocaleString('en-US', {
                        minimumFractionDigits: 2
                      })}
                    </Text>
                  </div>
                </Space>
              </div>
            </div>

            <Descriptions className="mb-3.5" bordered column={2} size="small">
              <Descriptions.Item
                label={
                  <Space>
                    <FaPhone className="text-gray-500" />
                    <Text>Phone</Text>
                  </Space>
                }
              >
                {customerDetails.phoneNumber || 'N/A'}
              </Descriptions.Item>
              <Descriptions.Item
                label={
                  <Space>
                    <FaEnvelope className="text-gray-500" />
                    <Text>Contact</Text>
                  </Space>
                }
              >
                {customerDetails.contact || 'N/A'}
              </Descriptions.Item>
            </Descriptions>
          </Space>
        )}
      </Card>
    </div>
  )
}
