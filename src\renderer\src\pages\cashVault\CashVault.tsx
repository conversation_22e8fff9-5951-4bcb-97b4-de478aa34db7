import { useEffect, useState } from 'react'
import { message, Tabs } from 'antd'
import { cashVaultApi } from '@/renderer/services'
import { useSelector } from 'react-redux'
import { IRootState } from '@/renderer/redux'
import { CashVaultHeader, TransactionList, CashVaultStatement } from './components'

const CashVault = () => {
  // Only keep essential state in parent
  const [balance, setBalance] = useState(0)
  const [isInitialized, setIsInitialized] = useState(true)
  const user = useSelector((state: IRootState) => state.user.data)

  // Load initial data
  useEffect(() => {
    checkInitialization()
  }, [])

  const checkInitialization = async () => {
    const response = await cashVaultApi.isInitialized()
    if (response.error.error || response.data.error) {
      message.error(response.error.message || response.data.error.message)
      return
    }
    setIsInitialized(response.data.data)
    if (response.data.data) {
      loadBalance()
    }
  }

  const loadBalance = async () => {
    const response = await cashVaultApi.getCurrentBalance()
    if (response.error.error || response.data.error) {
      message.error(response.error.message || response.data.error.message)
      return
    }
    setBalance(response.data.data)
  }

  return (
    <div className="p-6">
      <CashVaultHeader
        balance={balance}
        isInitialized={isInitialized}
        onBalanceUpdate={loadBalance}
        userId={user?.id || ''}
      />

      {isInitialized && (
        <Tabs defaultActiveKey="transactions">
          <Tabs.TabPane tab="Transactions" key="transactions">
            <TransactionList />
          </Tabs.TabPane>
          <Tabs.TabPane tab="Statement" key="statement">
            <CashVaultStatement />
          </Tabs.TabPane>
        </Tabs>
      )}
    </div>
  )
}

export default CashVault
