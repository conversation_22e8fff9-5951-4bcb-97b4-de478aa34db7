import { Statistic, Card, message } from 'antd'
import { useEffect, useState } from 'react'
import { bankApi, smallCounterApi, cashVaultApi } from '@/renderer/services'
import { formatCurrency } from '@/renderer/utils'
import { useTheme } from '@/renderer/contexts'

interface PaymentDetailsCardProps {
  paymentMethod: 'cash' | 'vault' | 'bank' | null
  selectedBank: string | null
  paidAmount: number
  balance: number | null
  setBalance: (balance: number | null) => void
}

export const PaymentDetailsCard = ({
  paymentMethod,
  selectedBank,
  paidAmount,
  balance,
  setBalance
}: PaymentDetailsCardProps) => {
  const [isPaymentDetailsVisible, setIsPaymentDetailsVisible] = useState(false)
  const [bankName, setBankName] = useState<string>('')
  const [isVaultInitialized, setIsVaultInitialized] = useState(true)
  const [currentPaymentMethod, setCurrentPaymentMethod] = useState<
    'cash' | 'vault' | 'bank' | null
  >(null)

  const { isDarkMode } = useTheme()

  const fetchBalance = async (method: 'cash' | 'vault' | 'bank' | null) => {
    // TODO: try catch not required cash account balance not correctly set

    try {
      let newBalance = null
      switch (method) {
        case 'cash':
          const response = await smallCounterApi.getCurrentBalance()

          console.log('cash account balance response', response)

          newBalance = response.data.data
          break
        case 'vault':
          try {
            const response = await cashVaultApi.getCurrentBalance()
            if (response.error.error || response.data.error) {
              setIsVaultInitialized(false)
              break
            }
            newBalance = response.data.data
          } catch (error) {
            setIsVaultInitialized(false)
          }
          break
        case 'bank':
          if (selectedBank) {
            const response = await bankApi.getBankById(selectedBank)
            setBankName(response.data.data.name)
            newBalance = response.data.data.balance
          }
          break
      }
      setBalance(newBalance)
    } catch (error) {
      message.error('Failed to fetch balance')
    }
  }

  useEffect(() => {
    if (paymentMethod !== currentPaymentMethod) {
      setIsPaymentDetailsVisible(false)
      setBalance(null)
      setBankName('')

      setTimeout(() => {
        setCurrentPaymentMethod(paymentMethod)
        if (paymentMethod) {
          fetchBalance(paymentMethod)
          setIsPaymentDetailsVisible(true)
        }
      }, 500)
    } else if (paymentMethod === 'bank' && selectedBank) {
      setIsPaymentDetailsVisible(false)
      setTimeout(() => {
        fetchBalance(paymentMethod)
        setIsPaymentDetailsVisible(true)
      }, 500)
    }
  }, [paymentMethod, selectedBank])

  const isBalanceInsufficient = balance !== null && paidAmount > balance
  const shouldShowCard = paymentMethod && (paymentMethod !== 'bank' || selectedBank)

  const getTitle = () => {
    switch (currentPaymentMethod) {
      case 'cash':
        return 'Cash Account Balance'
      case 'vault':
        return isVaultInitialized ? 'Cash Vault Balance' : 'Cash Vault Status'
      case 'bank':
        return `${bankName} Balance`
      default:
        return ''
    }
  }

  return (
    <div className="flex-1">
      {/* {shouldShowCard && ( */}
      <Card
        className={`overflow-hidden shadow-sm transition-all duration-500 ${
          isDarkMode
            ? 'bg-gradient-to-br from-blue-950 via-slate-950 to-blue-900 shadow-lg shadow-blue-900'
            : 'bg-gradient-to-br from-sky-300 via-white to-blue-200 shadow-lg shadow-blue-100'
        } ${
          isPaymentDetailsVisible && shouldShowCard ? 'max-h-32 opacity-100' : 'max-h-0 opacity-0'
        } ${isBalanceInsufficient ? 'border-red-500' : ''}`}
      >
        <Statistic
          title={getTitle()}
          value={
            currentPaymentMethod === 'vault' && !isVaultInitialized
              ? 'Not Initialized'
              : formatCurrency(balance || 0)
          }
          valueStyle={{ color: isBalanceInsufficient ? '#ff4d4f' : undefined }}
        />
      </Card>
      {/* )} */}
    </div>
  )
}
