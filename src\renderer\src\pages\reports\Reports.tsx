import { useState } from 'react'
import { Card, Input, Radio, Space } from 'antd'
import { SearchOutlined } from '@ant-design/icons'
import { useTheme } from '@/renderer/contexts'
import ReportGrid from './components/ReportGrid'
import ReportList from './components/ReportList'
import { ViewMode } from './types'

const Reports = () => {
  const { isDarkMode } = useTheme()
  const [viewMode, setViewMode] = useState<ViewMode>('grid')
  const [searchQuery, setSearchQuery] = useState('')

  return (
    <Card
      className={`m-6 bg-[length:200%_200%] bg-[center] shadow-lg ${
        isDarkMode
          ? 'bg-gradient-to-bl from-indigo-950 via-black to-indigo-900 shadow-indigo-950'
          : 'bg-gradient-to-bl from-indigo-200 via-white to-indigo-100 shadow-indigo-200'
      }`}
    >
      <div className="flex flex-col gap-6">
        {/* Header Section */}
        <div className="flex flex-col items-start justify-between gap-4 sm:flex-row sm:items-center">
          <Input
            placeholder="Search reports..."
            prefix={<SearchOutlined />}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="max-w-md"
            allowClear
          />
          <Radio.Group value={viewMode} onChange={(e) => setViewMode(e.target.value)}>
            <Radio.Button value="grid">Grid View</Radio.Button>
            <Radio.Button value="list">List View</Radio.Button>
          </Radio.Group>
        </div>

        {/* Reports Section */}
        <div className="mt-4">
          {viewMode === 'grid' ? (
            <ReportGrid searchQuery={searchQuery} />
          ) : (
            <ReportList searchQuery={searchQuery} />
          )}
        </div>
      </div>
    </Card>
  )
}

export default Reports
