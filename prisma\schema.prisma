// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider      = "prisma-client-js"
  binaryTargets = ["native", "windows"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

enum Status {
  ACTIVE
  VOID
}

model Party {
  id             String    @id @default(cuid())
  name           String
  type           PartyType // vendor, customer, creditor
  contact        String?
  address        String?
  phoneNumber    String?
  currentBalance Float     @default(0)
  createdAt      DateTime  @default(now())
  updatedAt      DateTime  @updatedAt

  // Relationships
  ledgers         Ledger[]
  Stock           Stock[]
  SaleInvoice     SaleInvoice[]
  PurchaseInvoice PurchaseInvoice[]
  Payments        Payments[]
  ManualEntry     ManualEntry[]
  TransfersFrom   AccountTransfer[] @relation("TransfersFrom")
  TransfersTo     AccountTransfer[] @relation("TransfersTo")

  @@index([type])
  @@index([name])
  @@index([currentBalance])
}

enum PartyType {
  VENDOR
  CUSTOMER
  CREDITOR
}

model Category {
  id           String   @id @default(cuid())
  name         String   @unique
  prefix       String   @unique // New field for storing the auto-generated prefix
  sequence     Int      @default(0)
  productCount Int      @default(0)
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  products Product[]
}

model Product {
  id              String   @id @default(cuid())
  name            String
  productId       String   @unique
  nature          String?
  tag             String?
  quantityInStock Float    @default(0)
  minStockLevel   Float?
  salePrice       Float
  barcode         String?
  imagePath       String?
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  // Make the relation required
  category   Category @relation(fields: [categoryId], references: [id])
  categoryId String

  stocks         Stock[]
  SaleItem       SaleItem[]
  PurchaseItem   PurchaseItem[]
  WalkInSaleItem WalkInSaleItem[]

  @@index([categoryId])
  @@index([quantityInStock])
  @@index([barcode])
  @@index([name])
  @@index([productId])
}

enum StockStatus {
  IN_STOCK
  SOLD_OUT
}

model Stock {
  id            String      @id @default(cuid())
  purchasePrice Float
  quantity      Float
  status        StockStatus @default(IN_STOCK)
  createdAt     DateTime    @default(now())
  updatedAt     DateTime    @updatedAt

  product           Product         @relation(fields: [productId], references: [id])
  productId         String
  vendor            Party           @relation(fields: [vendorId], references: [id])
  vendorId          String
  purchaseInvoice   PurchaseInvoice @relation(fields: [purchaseInvoiceId], references: [id])
  purchaseInvoiceId String

  StockEntry StockEntry[]

  @@index([productId, status])
  @@index([vendorId])
  @@index([purchaseInvoiceId])
}

model Ledger {
  id            String      @id @default(cuid())
  date          DateTime    @default(now())
  amount        Float
  creditOrDebit CreditDebit
  description   String
  status        Status      @default(ACTIVE)
  referenceType LedgerType

  // Cash flow tracking
  cashSource      CashLocation?
  cashDestination CashLocation?

  // Party reference (when needed)
  partyId String?
  party   Party?  @relation(fields: [partyId], references: [id])

  // Bank reference (since there are multiple banks)
  bankId String?
  bank   Banks?  @relation(fields: [bankId], references: [id])

  // Reference relationships
  paymentRef  String?
  payment     Payments?        @relation(fields: [paymentRef], references: [id])
  purchaseRef String?          @unique
  purchase    PurchaseInvoice? @relation(fields: [purchaseRef], references: [id])
  saleRef     String?          @unique
  sale        SaleInvoice?     @relation(fields: [saleRef], references: [id])
  expenseRef  String?          @unique
  expense     Expense?         @relation(fields: [expenseRef], references: [id])

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  createdById String
  createdBy   Admin        @relation("CreatedLedger", fields: [createdById], references: [id])
  voidedById  String?
  voidedBy    Admin?       @relation("VoidedLedger", fields: [voidedById], references: [id])
  ManualEntry ManualEntry?

  @@index([cashSource])
  @@index([cashDestination])
  @@index([bankId])
  @@index([partyId])
  @@index([status])
  @@index([date, status])
  @@index([referenceType])
  @@index([paymentRef])
  @@index([date])
}

enum CashLocation {
  BANK
  CASH_VAULT
  SMALL_COUNTER
  ACCOUNT
  OTHER
}

enum LedgerType {
  PurchaseInvoice
  SaleInvoice
  Payment
  OpeningBalance
  Expense
  Bank
  Vault
  SmallCounter
  Transfer
  Account_Transfer
  ManualEntry
}

enum CreditDebit {
  CREDIT
  DEBIT
}

enum CustomerType {
  REGISTERED
  WALK_IN
}

// Invoice number tracking
enum InvoiceStatus {
  RESERVED // When initially generated
  CONFIRMED // When invoice is created
  CANCELED // When invoice creation is canceled
  AVAILABLE // When reserved number is expired or canceled and can be reused
}

model InvoiceNumber {
  id            String        @id @default(cuid())
  number        Int
  invoiceNumber String        @unique
  type          CustomerType
  year          Int
  status        InvoiceStatus @default(RESERVED)
  issuedAt      DateTime      @default(now())
  confirmedAt   DateTime?
  canceledAt    DateTime?
  expiresAt     DateTime

  // Track who issued the number
  issuedById String
  issuedBy   Admin  @relation("IssuedInvoices", fields: [issuedById], references: [id])

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([type, number, year])
  @@index([status, expiresAt])
  @@index([type, year])
  @@index([invoiceNumber])
}

// Sale Invoice for registered customers
model SaleInvoice {
  id              String       @id @default(cuid())
  invoiceNumber   String       @unique
  customerType    CustomerType @default(REGISTERED)
  discountAmount  Float
  totalAmount     Float
  totalProfit     Float
  paidAmount      Float
  previousBalance Float
  newBalance      Float
  status          Status       @default(ACTIVE)
  date            DateTime
  createdAt       DateTime     @default(now())
  updatedAt       DateTime     @updatedAt

  // Relations
  customerId String
  customer   Party  @relation(fields: [customerId], references: [id])

  // Can have payment
  paymentId String?   @unique
  payment   Payments? @relation(fields: [paymentId], references: [id])

  // Tracking who created/voided
  createdById   String
  createdBy     Admin     @relation("CreatedSales", fields: [createdById], references: [id])
  voidedById    String?
  voidedBy      Admin?    @relation("VoidedSales", fields: [voidedById], references: [id])
  voidedAt      DateTime?
  voidingReason String?

  items  SaleItem[]
  Ledger Ledger?

  @@index([customerType, customerId])
  @@index([status])
  @@index([date])
  @@index([invoiceNumber])
  @@index([totalAmount])
}

// Sale Invoice for walk-in customers
model WalkInSaleInvoice {
  id             String       @id @default(cuid())
  invoiceNumber  String       @unique
  customerType   CustomerType @default(WALK_IN)
  customerName   String?
  discountAmount Float
  totalAmount    Float
  totalProfit    Float
  paidAmount     Float
  status         Status       @default(ACTIVE)
  date           DateTime
  createdAt      DateTime     @default(now())
  updatedAt      DateTime     @updatedAt

  // Must have payment
  paymentId String   @unique
  payment   Payments @relation(fields: [paymentId], references: [id])

  // Tracking who created/voided
  createdById   String
  createdBy     Admin     @relation("CreatedWalkInSales", fields: [createdById], references: [id])
  voidedById    String?
  voidedBy      Admin?    @relation("VoidedWalkInSales", fields: [voidedById], references: [id])
  voidedAt      DateTime?
  voidingReason String?

  items WalkInSaleItem[]

  @@index([customerType])
  @@index([status])
  @@index([date])
  @@index([invoiceNumber])
  @@index([totalAmount])
}

// Sale Item for registered customers
model SaleItem {
  id            String @id @default(cuid())
  totalQuantity Float
  salePrice     Float
  totalProfit   Float
  total         Float

  saleInvoiceId String
  saleInvoice   SaleInvoice @relation(fields: [saleInvoiceId], references: [id])
  productId     String
  product       Product     @relation(fields: [productId], references: [id])

  stockEntries StockEntry[]

  @@index([saleInvoiceId])
  @@index([productId])
}

// Sale Item for walk-in customers
model WalkInSaleItem {
  id            String @id @default(cuid())
  totalQuantity Float
  salePrice     Float
  totalProfit   Float
  total         Float

  saleInvoiceId String
  saleInvoice   WalkInSaleInvoice @relation(fields: [saleInvoiceId], references: [id])
  productId     String
  product       Product           @relation(fields: [productId], references: [id])

  stockEntries StockEntry[]

  @@index([saleInvoiceId])
  @@index([productId])
}

model StockEntry {
  id            String @id @default(cuid())
  quantityUsed  Float
  purchasePrice Float
  salePrice     Float
  profit        Float

  stockId          String
  stock            Stock           @relation(fields: [stockId], references: [id])
  saleItemId       String?
  saleItem         SaleItem?       @relation(fields: [saleItemId], references: [id])
  WalkInSaleItem   WalkInSaleItem? @relation(fields: [walkInSaleItemId], references: [id])
  walkInSaleItemId String?

  @@index([stockId])
  @@index([saleItemId])
  @@index([walkInSaleItemId])
}

enum PurchaseInvoiceType {
  PURCHASE
  VENDOR_RETURN
  STOCK_RETURN
  LEGACY_STOCK_RETURN
  OPENING_STOCK
}

model PurchaseInvoice {
  id              String   @id @default(cuid())
  invoiceNumber   String   @unique
  totalAmount     Float
  paidAmount      Float
  previousBalance Float
  newBalance      Float
  status          Status   @default(ACTIVE)
  date            DateTime
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  type PurchaseInvoiceType @default(PURCHASE)

  payment   Payments? @relation(fields: [paymentId], references: [id])
  paymentId String?   @unique // @unique ensures one-to-one relationship

  // Tracking who created/voided
  createdById   String
  createdBy     Admin     @relation("CreatedPurchases", fields: [createdById], references: [id])
  voidedById    String?
  voidedBy      Admin?    @relation("VoidedPurchases", fields: [voidedById], references: [id])
  voidedAt      DateTime?
  voidingReason String?

  vendorId String
  vendor   Party  @relation(fields: [vendorId], references: [id])

  items  PurchaseItem[]
  Stock  Stock[]
  Ledger Ledger?

  @@index([vendorId])
  @@index([status])
  @@index([date])
  @@index([type])
  @@index([invoiceNumber])
  @@index([totalAmount])
}

model PurchaseItem {
  id            String @id @default(cuid())
  quantity      Float
  purchasePrice Float
  total         Float

  purchaseInvoiceId String
  purchaseInvoice   PurchaseInvoice @relation(fields: [purchaseInvoiceId], references: [id])
  productId         String
  product           Product         @relation(fields: [productId], references: [id])

  @@index([purchaseInvoiceId])
  @@index([productId])
}

// Modify Payments model to track cash locations
model Payments {
  id              String        @id @default(cuid())
  date            DateTime
  type            PaymentType
  amount          Float
  paymentMethod   PaymentMethod
  referenceNumber String?
  description     String?
  status          Status        @default(ACTIVE)
  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt

  // For PAID: source is required, destination is the partyId
  // For RECEIVED: destination is required, source is the partyId
  sourceLocation      CashLocation? // Required when type is PAID
  destinationLocation CashLocation? // Required when type is RECEIVED
  locationId          String? // Required for bank transactions

  partyId String?
  party   Party?  @relation(fields: [partyId], references: [id])

  // Tracking who created/voided
  createdById   String
  createdBy     Admin     @relation("CreatedPayments", fields: [createdById], references: [id])
  voidedById    String?
  voidedBy      Admin?    @relation("VoidedPayments", fields: [voidedById], references: [id])
  voidedAt      DateTime?
  voidingReason String?

  Ledger            Ledger[]
  WalkInSaleInvoice WalkInSaleInvoice?
  PurchaseInvoice   PurchaseInvoice?
  SaleInvoice       SaleInvoice?

  @@index([partyId])
  @@index([type])
  @@index([paymentMethod])
  @@index([status])
  @@index([date])
  @@index([sourceLocation])
  @@index([destinationLocation])
}

enum PaymentSource {
  SMALL_COUNTER
  CASH_VAULT
  BANK
}

enum PaymentType {
  PAID
  RECEIVED
}

enum PaymentMethod {
  CASH
  BANK_TRANSFER
  CHEQUE
}

model Admin {
  id        String    @id @default(cuid())
  username  String    @unique
  password  String
  name      String
  role      AdminRole
  isActive  Boolean   @default(true)
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt

  PurchaseInvoice         PurchaseInvoice[]   @relation("CreatedPurchases")
  VoidedPurchases         PurchaseInvoice[]   @relation("VoidedPurchases")
  SaleInvoice             SaleInvoice[]       @relation("CreatedSales")
  VoidedSales             SaleInvoice[]       @relation("VoidedSales")
  Expenses                Expense[]           @relation("CreatedExpenses")
  VoidedExpenses          Expense[]           @relation("VoidedExpenses")
  Payments                Payments[]          @relation("CreatedPayments")
  VoidedPayments          Payments[]          @relation("VoidedPayments")
  WalkInSaleInvoice       WalkInSaleInvoice[] @relation("CreatedWalkInSales")
  VoidedWalkInSales       WalkInSaleInvoice[] @relation("VoidedWalkInSales")
  InvoiceNumber           InvoiceNumber[]     @relation("IssuedInvoices")
  Banks                   Banks[]             @relation("CreatedBanks")
  Ledger                  Ledger[]            @relation("CreatedLedger")
  VoidedLedger            Ledger[]            @relation("VoidedLedger")
  CreatedManualEntries    ManualEntry[]       @relation("CreatedManualEntries")
  DeletedManualEntries    ManualEntry[]       @relation("DeletedManualEntries")
  CreatedAccountTransfers AccountTransfer[]   @relation("CreatedAccountTransfers")
  DeletedAccountTransfers AccountTransfer[]   @relation("DeletedAccountTransfers")

  @@index([role])
  @@index([isActive])
  @@index([username])
}

enum AdminRole {
  DEVELOPER
  SUPER_ADMIN
  ADMIN_STAFF
  STAFF
}

model Expense {
  id          String   @id @default(cuid())
  category    String // e.g., "Salary", "Rent", "Marketing", "Transportation", "Office Supplies", "Repairs", "Utilities", "Other"
  amount      Float
  description String?
  date        DateTime @default(now())
  status      Status   @default(ACTIVE)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Tracking who created/voided
  createdById   String
  createdBy     Admin     @relation("CreatedExpenses", fields: [createdById], references: [id])
  voidedById    String?
  voidedBy      Admin?    @relation("VoidedExpenses", fields: [voidedById], references: [id])
  voidedAt      DateTime?
  voidingReason String?
  Ledger        Ledger?

  @@index([category])
  @@index([date])
  @@index([status])
}

// Modified Banks model (removed BankDailyBalance relation)
model Banks {
  id        String   @id @default(cuid())
  name      String
  accountNo String   @unique
  balance   Float
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  isActive  Boolean  @default(true)

  // Tracking who created/voided
  createdById String
  createdBy   Admin  @relation("CreatedBanks", fields: [createdById], references: [id])

  // Add relations
  Ledger      Ledger[]
  ManualEntry ManualEntry[]

  @@index([isActive])
  @@index([name])
  @@index([balance])
}

model SmallCounter {
  id         String @id @default(cuid())
  cashInShop Float

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([cashInShop])
}

// Modify existing CashAccount model to CashVault
model CashVault {
  id        String   @id @default(cuid())
  balance   Float    @default(0)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([balance])
}

enum ManualEntryTarget {
  CASH_VAULT
  SMALL_COUNTER
}

model ManualEntry {
  id              String      @id @default(cuid())
  amount          Float
  description     String?
  transactionDate DateTime
  entryType       CreditDebit // CREDIT or DEBIT

  // Target entity (only one should be set)
  partyId String? // For parties (vendors, customers, creditors)
  party   Party?  @relation(fields: [partyId], references: [id])

  bankId String? // For specific banks
  bank   Banks?  @relation(fields: [bankId], references: [id])

  // For cash vault and small counter, we use targetType
  targetType ManualEntryTarget? // CASH_VAULT or SMALL_COUNTER

  // Link to the created ledger entry
  ledgerId String @unique
  ledger   Ledger @relation(fields: [ledgerId], references: [id])

  // Audit trail
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  createdById String
  createdBy   Admin    @relation("CreatedManualEntries", fields: [createdById], references: [id])

  // Deletion tracking
  isDeleted      Boolean   @default(false)
  deletedAt      DateTime?
  deletedBy      Admin?    @relation("DeletedManualEntries", fields: [deletedById], references: [id])
  deletedById    String?
  deletionReason String?

  @@index([partyId])
  @@index([bankId])
  @@index([targetType])
  @@index([transactionDate])
  @@index([entryType])
  @@index([isDeleted])
}

model AccountTransfer {
  id           String   @id @default(cuid())
  description  String?
  amount       Float
  transferDate DateTime

  // From Account (Debited Party)
  fromPartyId String
  fromParty   Party  @relation("TransfersFrom", fields: [fromPartyId], references: [id])

  // To Account (Credited Party)
  toPartyId String
  toParty   Party  @relation("TransfersTo", fields: [toPartyId], references: [id])

  // Track both ledger entries for double-entry bookkeeping
  fromLedgerId String // FROM party ledger entry ID
  toLedgerId   String // TO party ledger entry ID

  // Audit trail
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  createdById String
  createdBy   Admin    @relation("CreatedAccountTransfers", fields: [createdById], references: [id])

  // Deletion tracking
  isDeleted      Boolean   @default(false)
  deletedAt      DateTime?
  deletedBy      Admin?    @relation("DeletedAccountTransfers", fields: [deletedById], references: [id])
  deletedById    String?
  deletionReason String?

  @@index([fromPartyId])
  @@index([toPartyId])
  @@index([transferDate])
  @@index([isDeleted])
}
