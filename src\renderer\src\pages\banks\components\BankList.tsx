// src/renderer/src/pages/banks/components/BankList.tsx
import { useEffect, useState } from 'react'
import { Table, Space, Button, Switch, App, Select, Divider } from 'antd'
import type { ColumnsType } from 'antd/es/table'
import { useApi } from '@/renderer/hooks'
import { bankApi } from '@/renderer/services'
import { GetAllBanksFilter, BankResponse } from '@/common/types'

interface BankListProps {
  onBankSelect: (bankId: string) => void
  refreshTrigger: number
  setRefreshTrigger: (value: any) => void
}

export const BankList = ({ onBankSelect, refreshTrigger, setRefreshTrigger }: BankListProps) => {
  const { message, modal } = App.useApp()
  const [filters, setFilters] = useState<GetAllBanksFilter>({
    isActive: true,
    page: 1,
    limit: 10
  })

  const {
    request: fetchAllBanks,
    data,
    isLoading
  } = useApi<BankResponse, [GetAllBanksFilter]>(bankApi.getAllBanks)

  useEffect(() => {
    loadBanks()
  }, [refreshTrigger, filters])

  const loadBanks = async () => {
    await fetchAllBanks(filters)
  }

  const handleStatusChange = async (bankId: string, isActive: boolean) => {
    let response

    if (isActive) {
      response = await bankApi.reactivateBank(bankId)
    } else {
      response = await bankApi.deactivateBank(bankId)
    }

    if (response.error.error || response.data.error) {
      message.error(response.error.message || response.data.error.message)
      setRefreshTrigger((prev) => prev + 1)
      return
    }

    setRefreshTrigger((prev) => prev + 1)
    message.success(`Bank ${isActive ? 'activated' : 'deactivated'} successfully`)
  }

  const handleDelete = (bankId: string) => {
    modal.confirm({
      title: 'Delete Bank',
      content: 'Are you sure you want to delete this bank?',
      okText: 'Yes',
      okType: 'danger',
      cancelText: 'No',
      onOk: async () => {
        const response = await bankApi.deleteBank(bankId)

        if (response.error.error || response.data.error) {
          message.error(response.error.message || response.data.error.message)
          return
        }

        setRefreshTrigger((prev) => prev + 1)
        message.success('Bank deleted successfully')
      }
    })
  }

  const columns: ColumnsType<any> = [
    {
      title: 'Sr. No.',
      dataIndex: 'index',
      key: 'index',
      render: (_, __, index) => index + 1
    },
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name'
    },
    {
      title: 'Account No',
      dataIndex: 'accountNo',
      key: 'accountNo'
    },
    {
      title: 'Balance',
      dataIndex: 'balance',
      key: 'balance',
      render: (balance) => `Rs. ${balance.toLocaleString()}`
    },
    {
      title: 'Status',
      key: 'isActive',
      render: (_, record) => (
        <Switch
          checked={record.isActive}
          onChange={(checked) => handleStatusChange(record.id, checked)}
        />
      )
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record) => (
        <Space>
          <Button type="link" onClick={() => onBankSelect(record.id)}>
            Details
          </Button>
          <Button type="link" danger onClick={() => handleDelete(record.id)}>
            Delete
          </Button>
        </Space>
      )
    }
  ]

  return (
    <div>
      <Space className="mb-4">
        <Select
          value={filters.isActive}
          onChange={(value) => setFilters((prev) => ({ ...prev, isActive: value }))}
          style={{ width: 120 }}
        >
          <Select.Option value={undefined}>All Status</Select.Option>
          <Select.Option value={true}>Active</Select.Option>
          <Select.Option value={false}>Inactive</Select.Option>
        </Select>
      </Space>

      <Table
        columns={columns}
        dataSource={data?.banks}
        loading={isLoading}
        rowKey="id"
        pagination={{
          position: ['topRight'],
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`,
          showPrevNextJumpers: true,
          current: filters.page,
          pageSize: filters.limit,
          total: data?.total,
          onChange: (page) => setFilters((prev) => ({ ...prev, page }))
        }}
      />
    </div>
  )
}
