// src/renderer/src/pages/banks/components/BankDetailsModal/BankLedger.tsx
import { useEffect, useState } from 'react'
import { Table, DatePicker, Select, Space } from 'antd'
import type { ColumnsType } from 'antd/es/table'
import dayjs from 'dayjs'
import { useApi } from '@/renderer/hooks'
import { bankLedgerApi } from '@/renderer/services'
import { BankLedgerEntry, BankLedgerResponse, GetBankLedgerParams } from '@/common/types/bankLedger'
// import { CreditDebit, Status } from '@/common/types'

enum CreditDebit {
  CREDIT = 'CREDIT',
  DEBIT = 'DEBIT'
}

enum Status {
  ACTIVE = 'ACTIVE',
  VOID = 'VOID'
}

enum LedgerType {
  SALE = 'SALE',
  PURCHASE = 'PURCHASE',
  PAYMENT = 'PAYMENT',
  RECEIPT = 'RECEIPT',
  TRANSFER = 'TRANSFER',
  EXPENSE = 'EXPENSE',
  STOCK_RETURN = 'STOCK_RETURN'
}

interface BankLedgerProps {
  bankId: string
}

export const BankLedger = ({ bankId }: BankLedgerProps) => {
  const [filters, setFilters] = useState<GetBankLedgerParams>({
    bankId,
    page: 1,
    limit: 10
  })

  const { request, data, isLoading } = useApi<BankLedgerResponse, [GetBankLedgerParams]>(
    bankLedgerApi.getBankLedgerEntries
  )

  useEffect(() => {
    setFilters((prev) => ({ ...prev, bankId }))
  }, [bankId])

  useEffect(() => {
    loadEntries()
  }, [filters, bankId])

  const loadEntries = async () => {
    await request(filters)
  }

  const columns: ColumnsType<BankLedgerEntry> = [
    {
      title: 'Date',
      dataIndex: 'date',
      key: 'date',
      render: (date) => dayjs(date).format('YYYY-MM-DD')
    },
    {
      title: 'Description',
      dataIndex: 'description',
      key: 'description'
    },
    {
      title: 'Type',
      dataIndex: 'referenceType',
      key: 'referenceType'
    },
    {
      title: 'Credit/Debit',
      dataIndex: 'creditOrDebit',
      key: 'creditOrDebit'
    },
    {
      title: 'Amount',
      dataIndex: 'amount',
      key: 'amount',
      render: (amount) => `Rs. ${amount.toLocaleString()}`
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status'
    }
  ]

  return (
    <div>
      <Space className="mb-4">
        <DatePicker.RangePicker
          onChange={(dates) => {
            setFilters((prev) => ({
              ...prev,
              startDate: dates?.[0]?.toDate(),
              endDate: dates?.[1]?.toDate()
            }))
          }}
        />
        <Select
          placeholder="Transaction Type"
          allowClear
          style={{ width: 150 }}
          onChange={(value) => setFilters((prev) => ({ ...prev, creditOrDebit: value }))}
        >
          <Select.Option value={CreditDebit.CREDIT}>Credit</Select.Option>
          <Select.Option value={CreditDebit.DEBIT}>Debit</Select.Option>
        </Select>
        <Select
          placeholder="Reference Type"
          allowClear
          style={{ width: 150 }}
          onChange={(value) => setFilters((prev) => ({ ...prev, referenceType: value }))}
        >
          {Object.values(LedgerType).map((type) => (
            <Select.Option key={type} value={type}>
              {type}
            </Select.Option>
          ))}
        </Select>
        <Select
          placeholder="Status"
          allowClear
          style={{ width: 150 }}
          onChange={(value) => setFilters((prev) => ({ ...prev, status: value }))}
        >
          {Object.values(Status).map((status) => (
            <Select.Option key={status} value={status}>
              {status}
            </Select.Option>
          ))}
        </Select>
      </Space>

      <Table
        size="small"
        columns={columns}
        dataSource={data?.data}
        loading={isLoading}
        rowKey="id"
        pagination={{
          current: filters.page,
          pageSize: filters.limit,
          total: data?.total,
          onChange: (page) => setFilters((prev) => ({ ...prev, page }))
        }}
        summary={() => (
          <Table.Summary>
            <Table.Summary.Row>
              <Table.Summary.Cell index={0} colSpan={3}>
                Totals
              </Table.Summary.Cell>
              <Table.Summary.Cell index={1}>
                Credits: Rs. {data?.totals.credits.toLocaleString()}
              </Table.Summary.Cell>
              <Table.Summary.Cell index={2}>
                Debits: Rs. {data?.totals.debits.toLocaleString()}
              </Table.Summary.Cell>
              <Table.Summary.Cell index={3}>
                Net: Rs. {data?.totals.net.toLocaleString()}
              </Table.Summary.Cell>
            </Table.Summary.Row>
          </Table.Summary>
        )}
      />
    </div>
  )
}
