import { Channels } from "@/common/constants";
import { http } from './http'
import {
    ProcessVendorReturnParams,
    VoidVendorReturnParams,
    GetVendorReturnsParams
} from '@/common/types/vendorReturn'

export const processVendorReturn = async (data: ProcessVendorReturnParams) => {
    return await http.post(Channels.PROCESS_VENDOR_RETURN, { body: data })
}

export const voidVendorReturn = async (data: VoidVendorReturnParams) => {
    return await http.post(Channels.VOID_VENDOR_RETURN, { body: data })
}

export const getVendorReturns = async (data: GetVendorReturnsParams) => {
    return await http.get(Channels.GET_VENDOR_RETURNS, {
        query: {
            ...data,
            startDate: data.startDate?.toISOString(),
            endDate: data.endDate?.toISOString()
        }
    })
} 