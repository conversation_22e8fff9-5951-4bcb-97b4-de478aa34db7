import { http } from './http'
import { Channels } from '@/common/constants'

// Ledger
export async function clearLedger() {
    return await http.post(Channels.CLEAR_LEDGER)
}

// Sales
export async function clearStockEntries() {
    return await http.post(Channels.CLEAR_STOCK_ENTRIES)
}

export async function clearWalkInSaleItems() {
    return await http.post(Channels.CLEAR_WALK_IN_SALE_ITEMS)
}

export async function clearSaleItems() {
    return await http.post(Channels.CLEAR_SALE_ITEMS)
}

export async function clearWalkInSaleInvoices() {
    return await http.post(Channels.CLEAR_WALK_IN_SALE_INVOICES)
}

export async function clearSaleInvoices() {
    return await http.post(Channels.CLEAR_SALE_INVOICES)
}

// Purchases
export async function clearPurchaseItems() {
    return await http.post(Channels.CLEAR_PURCHASE_ITEMS)
}

export async function clearStock() {
    return await http.post(Channels.CLEAR_STOCK)
}

export async function clearPurchaseInvoices() {
    return await http.post(Channels.CLEAR_PURCHASE_INVOICES)
}

// Products & Categories
export async function clearProducts() {
    return await http.post(Channels.CLEAR_PRODUCTS)
}

export async function clearCategories() {
    return await http.post(Channels.CLEAR_CATEGORIES)
}

// Payments & Banks
export async function clearPayments() {
    return await http.post(Channels.CLEAR_PAYMENTS)
}


export async function clearBanks() {
    return await http.post(Channels.CLEAR_BANKS)
}

// Cash Management
export async function clearSmallCounter() {
    return await http.post(Channels.CLEAR_SMALL_COUNTER)
}

export async function clearCashVault() {
    return await http.post(Channels.CLEAR_CASH_VAULT)
}

// Expenses
export async function clearExpenses() {
    return await http.post(Channels.CLEAR_EXPENSES)
}

// Invoice Numbers
export async function clearInvoiceNumbers() {
    return await http.post(Channels.CLEAR_INVOICE_NUMBERS)
}

// Parties
export async function clearParties() {
    return await http.post(Channels.CLEAR_PARTIES)
}

// Admin
export async function clearAdmins() {
    return await http.post(Channels.CLEAR_ADMINS)
}

// Clear All Data
export async function clearAllData() {
    return await http.post(Channels.CLEAR_ALL_DATA)
}
