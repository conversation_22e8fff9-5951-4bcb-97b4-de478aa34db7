import { CashLocation as CashLocationEnum, Status as StatusEnum } from '@prisma/client'


export enum CashLocation {
    SMALL_COUNTER = 'SMALL_COUNTER',
    CASH_VAULT = 'CASH_VAULT',
    BANK = 'BANK'
}

// Types for API parameters
export interface VendorReturnItem {
    productId: string
    quantity: number
    purchasePrice: number
    // originalStockId?: string
}

export interface ProcessVendorReturnParams {
    vendorId: string
    items: VendorReturnItem[]
    adminId: string
    paymentDetails?: {
        source: CashLocationEnum
        bankId?: string
    }
    description?: string
}

export interface VoidVendorReturnParams {
    returnInvoiceId: string
    adminId: string
    reason: string
}

export interface GetVendorReturnsParams {
    startDate?: Date
    endDate?: Date
    vendorId?: string
    productId?: string
    status?: StatusEnum
    page?: number
    limit?: number
}

// Types for frontend components
export interface VendorReturnItemDisplay {
    id: string
    quantity: number
    purchasePrice: number
    product: {
        name: string
        productId: string
    }
}

export interface VendorReturnDisplay {
    id: string
    invoiceNumber: string
    date: string
    vendor: {
        id: string
        name: string
    }
    totalAmount: number
    items: VendorReturnItemDisplay[]
    status: StatusEnum
    createdBy: {
        name: string
    }
    voidingReason?: string
}

export interface VendorReturnsResponse {
    returns: VendorReturnDisplay[]
    total: number
    page: number
    totalPages: number
}

export interface VendorReturnListProps {
    refreshTrigger: number
}

export interface AddVendorReturnProps {
    setRefreshTrigger: (trigger: number) => void
}

export interface VendorReturnFormItem extends VendorReturnItem {
    key: string
}

export interface ProductOption {
    value: string
    label: string
    purchasePrice: number
} 