import { useState } from 'react'
import { Button, Input, Form, Card, Typography, Space, Divider } from 'antd'
import { VendorList } from './components/VendorList'
import { CreateVendorDrawer } from './components/CreateVendorDrawer'
import { VendorDetailsModal } from './components/VendorDetailsModal'
import { SearchOutlined, PlusOutlined, UserOutlined } from '@ant-design/icons'
import { PartyListPdfButton } from '@/renderer/components/partyListPdfButton'
import { PartyType } from '@/common/types/party'
import './Vendors.scss'
import { useTheme } from '@/renderer/contexts'

const { Title } = Typography

const Vendors = () => {
  const [isCreateDrawerOpen, setIsCreateDrawerOpen] = useState(false)
  const [selectedVendor, setSelectedVendor] = useState<string | null>(null)
  const [searchQuery, setSearchQuery] = useState('')
  const [refreshTrigger, setRefreshTrigger] = useState(0)
  const { isDarkMode } = useTheme()

  const handleCreateVendor = () => {
    setIsCreateDrawerOpen(true)
  }

  const handleVendorClick = (vendorId: string) => {
    setSelectedVendor(vendorId)
  }

  const handleVendorCreated = () => {
    setRefreshTrigger((prev) => prev + 1)
    setIsCreateDrawerOpen(false)
  }

  return (
    <div className="vendors-container">
      <Card
        className={`bg-[length:200%_200%] bg-[center] shadow-lg ${
          isDarkMode
            ? 'bg-gradient-to-bl from-indigo-950 via-black to-indigo-900 shadow-indigo-950'
            : 'bg-gradient-to-bl from-indigo-200 via-white to-indigo-100 shadow-indigo-200'
        }`}
      >
        <Space wrap className="flex justify-between gap-4">
          <Title level={4}>
            <UserOutlined /> Vendors Management
          </Title>

          <Space wrap className="gap-4">
            <Form.Item className="search-input" style={{ marginBottom: 0 }}>
              <Input
                placeholder="Search vendors..."
                prefix={<SearchOutlined />}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </Form.Item>

            <PartyListPdfButton partyType={PartyType.VENDOR} search={searchQuery} />

            <Button type="primary" icon={<PlusOutlined />} onClick={handleCreateVendor}>
              Add Vendor
            </Button>
          </Space>
        </Space>

        <Divider />

        <VendorList
          onVendorClick={handleVendorClick}
          searchQuery={searchQuery}
          refreshTrigger={refreshTrigger}
        />
      </Card>

      <CreateVendorDrawer
        open={isCreateDrawerOpen}
        onClose={() => setIsCreateDrawerOpen(false)}
        onVendorCreated={handleVendorCreated}
      />

      <VendorDetailsModal
        vendorId={selectedVendor}
        open={!!selectedVendor}
        onClose={() => setSelectedVendor(null)}
      />
    </div>
  )
}

export default Vendors
