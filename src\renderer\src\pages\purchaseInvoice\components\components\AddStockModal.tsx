import { useState, useEffect, useRef } from 'react'
import {
  Modal,
  Select,
  InputNumber,
  Card,
  Button,
  message,
  Form,
  Statistic,
  Tooltip,
  Tag,
  Divider,
  Space,
  Typography,
  theme
} from 'antd'

import { useProductContext } from '@/renderer/contexts/ProductContext'
import { productApi } from '@/renderer/services'
import './AddStockModal.scss'
import AddNewProductModal from './AddNewProductModal'
import {
  BarcodeOutlined,
  BoxPlotOutlined,
  DollarOutlined,
  TagsOutlined,
  InfoCircleOutlined,
  WarningOutlined,
  PlusOutlined
} from '@ant-design/icons'
import { formatCurrency } from '@/renderer/utils'
import { useTheme } from '@/renderer/contexts/ThemeContext'
import type { RefSelectProps } from 'antd/es/select'

const { Title, Text } = Typography

interface AddStockModalProps {
  open: boolean
  onClose: () => void
  onAddItem: (item: PurchaseInvoiceItem) => void
  invoiceItems: PurchaseInvoiceItem[]
}

interface PurchaseInvoiceItem {
  // id: string
  displayName: string
  productId: string
  quantity: number
  purchasePrice: number
  total: number
}

export const AddStockModal = ({ open, onClose, onAddItem, invoiceItems }: AddStockModalProps) => {
  const [form] = Form.useForm()
  const { products, refreshProducts } = useProductContext()
  const [productDetails, setProductDetails] = useState<any>(null)
  const [isVisible, setIsVisible] = useState(false)
  const [isProductAlreadyInList, setIsProductAlreadyInList] = useState(false)

  const [isAddNewProductModalOpen, setIsAddNewProductModalOpen] = useState(false)

  const pproductSelectRef = useRef<RefSelectProps>(null)

  // Watch form values for quantity and purchasePrice
  const quantity = Form.useWatch('quantity', form)
  const purchasePrice = Form.useWatch('purchasePrice', form)

  // Calculate total whenever quantity or purchasePrice changes
  const total = (quantity || 0) * (purchasePrice || 0)

  useEffect(() => {
    if (!open) {
      form.resetFields()
      setProductDetails(null)
    }
  }, [open, form])

  const handleProductSelect = async (productId: string) => {
    setIsVisible(false)

    // Wait for fade out animation
    await new Promise((resolve) => setTimeout(resolve, 500))

    const product = invoiceItems.find((item) => item.productId === productId)
    if (product) {
      setIsProductAlreadyInList(true)
    } else {
      setIsProductAlreadyInList(false)
    }

    if (productId) {
      // does not need try catch
      const response = await productApi.getProduct(productId)

      console.log(response)

      if (response.error.error || response.data.error) {
        message.error(response.error.message || response.data.error.message)
        return
      }
      setProductDetails(response.data.data)
      // Fade in animation will be triggered by productDetails change
      setTimeout(() => {
        setIsVisible(true)
      }, 50) // Small delay to ensure DOM update
    } else {
      setProductDetails(null)
    }
  }

  const handleCancel = () => {
    form.resetFields()
    onClose()
  }

  const handleSubmit = async (values: any) => {
    const total = values.quantity * values.purchasePrice
    onAddItem({
      // id: productDetails.productId,
      displayName: `${productDetails.name} - (${productDetails.productId}) [${productDetails.tag}]`,
      productId: values.productId,
      quantity: values.quantity,
      purchasePrice: values.purchasePrice,
      total
    })
    setIsVisible(false)
    setProductDetails(null)
    form.resetFields()
    pproductSelectRef.current?.focus()
  }

  const handleAddNewProduct = () => {
    setIsAddNewProductModalOpen(true)
  }

  const handleAddNewProductCancel = () => {
    setIsAddNewProductModalOpen(false)
  }

  return (
    <Modal title="Add Stock" open={open} onCancel={handleCancel} footer={null} width={600}>
      <Space
        direction="vertical"
        style={{
          width: '100%',
          alignItems: 'flex-end'
        }}
      >
        <Button
          className="!border-green-500 hover:!border-green-600 hover:!text-green-600"
          onClick={handleAddNewProduct}
        >
          <PlusOutlined />
          Add New Product
        </Button>
      </Space>
      <Form form={form} layout="vertical" onFinish={handleSubmit}>
        <Form.Item
          name="productId"
          label="Select Product"
          rules={[{ required: true, message: 'Please select a product' }]}
        >
          <Select
            showSearch
            ref={pproductSelectRef}
            placeholder="Select a product"
            optionFilterProp="children"
            onChange={handleProductSelect}
            style={{ width: '100%' }}
            options={products}
            allowClear
            filterOption={(input, option) =>
              (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
            }
          />
        </Form.Item>

        <ProductDetailCard
          productDetails={productDetails}
          isProductAlreadyInList={isProductAlreadyInList}
          isVisible={isVisible}
        />

        <Form.Item
          className="!mt-10"
          name="quantity"
          label="Quantity"
          rules={[
            { required: true, message: 'Please enter quantity' },
            { type: 'number', min: 1, message: 'Quantity must be greater than 0' }
          ]}
        >
          <InputNumber placeholder="Enter quantity" style={{ width: '100%' }} min={1} />
        </Form.Item>

        <Form.Item
          name="purchasePrice"
          label="Purchase Price"
          rules={[
            { required: true, message: 'Please enter purchase price' },
            { type: 'number', min: 0.01, message: 'Price must be greater than 0' }
          ]}
        >
          <InputNumber
            placeholder="Enter purchase price"
            style={{ width: '100%' }}
            min={0.01}
            precision={2}
          />
        </Form.Item>

        <Typography.Text strong>Total: {total.toFixed(2)}</Typography.Text>

        <Form.Item>
          <div className="modal-footer">
            <Button
              type="primary"
              htmlType="submit"
              style={{ backgroundColor: '#52c41a', borderColor: '#52c41a' }}
            >
              Add to Invoice
            </Button>
          </div>
        </Form.Item>
      </Form>

      <AddNewProductModal
        open={isAddNewProductModalOpen}
        onClose={handleAddNewProductCancel}
        setRefreshTrigger={refreshProducts}
      />
    </Modal>
  )
}

interface ProductDetailCardProps {
  productDetails: any
  isProductAlreadyInList: boolean
  isVisible: boolean
}

const ProductDetailCard = ({
  productDetails,
  isProductAlreadyInList,
  isVisible
}: ProductDetailCardProps) => {
  const { token } = theme.useToken()

  const { isDarkMode } = useTheme()

  return (
    <Card
      title={
        <Space className="w-full justify-between">
          <Title level={4} className="!mb-0">
            {productDetails?.name || 'N/A'}
          </Title>
          {isProductAlreadyInList && (
            <Tag color="error" icon={<WarningOutlined />}>
              Already in invoice
            </Tag>
          )}
        </Space>
      }
      className={`animated-section shadow-md ${
        isDarkMode
          ? 'bg-gradient-to-br from-blue-950 via-slate-950 to-blue-900'
          : 'bg-gradient-to-br from-sky-200 via-white to-blue-200'
      } ${
        isVisible ? 'visible translate-y-4 opacity-100' : 'translate-y-0 opacity-0'
      } ${isProductAlreadyInList ? 'border-b-2 border-l-2 border-red-500' : 'border-b-2 border-l-2 border-green-500'}`}
      bordered
      style={{ backgroundSize: '200% 200%', backgroundPosition: 'center' }}
    >
      <div className="grid grid-cols-2 gap-4">
        <div>
          <Space direction="vertical" className="w-full">
            <Space className="w-full justify-center gap-4">
              <Text type="secondary">
                <BarcodeOutlined className="mr-2" />
                Product ID
              </Text>
              <Text strong>{productDetails?.productId || 'N/A'}</Text>
            </Space>

            <Space className="w-full justify-center gap-4">
              <Text type="secondary">
                <TagsOutlined className="mr-2" />
                Category
              </Text>
              <Tag color="blue">{productDetails?.category.name || 'N/A'}</Tag>
            </Space>
          </Space>
        </div>

        <div>
          <Space direction="vertical" className="w-full items-center">
            <Statistic
              title={
                <Space>
                  <BoxPlotOutlined />
                  <Text type="secondary">In Stock</Text>
                </Space>
              }
              value={productDetails?.quantityInStock || 0}
              valueStyle={{
                color:
                  (productDetails?.quantityInStock || 0) <= (productDetails?.minStockLevel || 0)
                    ? '#ff4d4f'
                    : '#52c41a'
              }}
              suffix="units"
            />
          </Space>
        </div>
      </div>

      <Divider className="my-2" />

      <div className="grid grid-cols-2 gap-4">
        <Statistic
          title={
            <Space>
              <DollarOutlined />
              <Text type="secondary">Sale Price</Text>
            </Space>
          }
          value={productDetails?.salePrice || 0}
          precision={2}
          prefix="$"
        />

        <div className="flex items-end">
          <Tooltip
            title={
              <div className="space-y-2">
                <div className="flex justify-between gap-2">
                  <Text className="text-white">Nature:</Text>
                  <Tag color="purple">{productDetails?.nature || 'Not Specified'}</Tag>
                </div>
                <div className="flex justify-between gap-2">
                  <Text className="text-white">Tag:</Text>
                  <Tag color="cyan">{productDetails?.tag || 'Not Specified'}</Tag>
                </div>
                <div className="flex justify-between gap-2">
                  <Text className="text-white">Min Stock Level:</Text>
                  <Tag color="orange">{productDetails?.minStockLevel || 'Not Specified'}</Tag>
                </div>
              </div>
            }
            placement="bottomRight"
          >
            <Tag icon={<InfoCircleOutlined />} color="default" className="cursor-help">
              More Details
            </Tag>
          </Tooltip>
        </div>
      </div>
    </Card>
  )
}
