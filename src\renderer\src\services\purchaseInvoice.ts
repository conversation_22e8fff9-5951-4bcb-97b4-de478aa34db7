import { http } from "./http";
import { Channels } from "@/common/constants";
import {
    CreatePurchaseInvoiceData,
    GetPurchaseInvoicesParams,
    VoidPurchaseInvoiceParams
} from "@/common/types";

export const createPurchaseInvoice = async (data: CreatePurchaseInvoiceData) => {
    return await http.post(Channels.CREATE_PURCHASE_INVOICE, {
        body: data
    });
}

export const voidPurchaseInvoice = async ({ id, adminId, reason }: VoidPurchaseInvoiceParams) => {
    return await http.post(Channels.VOID_PURCHASE_INVOICE, {
        body: { id, adminId, reason }
    });
}

export const getPurchaseInvoiceById = async (id: string) => {
    return await http.get(Channels.GET_PURCHASE_INVOICE, {
        params: { id }
    });
}

export const getAllPurchaseInvoices = async (params: GetPurchaseInvoicesParams) => {
    return await http.get(Channels.GET_ALL_PURCHASE_INVOICES, {
        query: params
    });
}

export const getPurchaseInvoicesByVendor = async (vendorId: string, { page = 1, limit = 10 } = {}) => {
    return await http.get(Channels.GET_PURCHASE_INVOICES_BY_VENDOR, {
        params: { vendorId },
        query: { page, limit }
    });
}

