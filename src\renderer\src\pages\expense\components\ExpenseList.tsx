import { useEffect, useState } from 'react'
import {
  Table,
  Tag,
  Typography,
  Button,
  Space,
  Form,
  Input,
  DatePicker,
  Select,
  Divider
} from 'antd'
import { EyeOutlined, DeleteOutlined, SearchOutlined } from '@ant-design/icons'
import { expenseApi } from '@/renderer/services'
import type { ColumnsType } from 'antd/es/table'
import { formatCurrency } from '@/renderer/utils'
import dayjs from 'dayjs'
import { useApi } from '@/renderer/hooks'
import { ExpenseItem, ExpenseResponse, GetExpenseParams } from '@/common/types/expense'
import { Status } from '@/common/types'
import type { RangePickerProps } from 'antd/es/date-picker'

// This interface is only used as props between components, so it stays here
interface ExpenseListProps {
  onExpenseClick: (id: string) => void
  onVoidExpense: (id: string) => void
  refreshTrigger: number
}

export const ExpenseList = ({
  onExpenseClick,
  onVoidExpense,
  refreshTrigger
}: ExpenseListProps) => {
  // State for filters
  const [searchQuery, setSearchQuery] = useState('')
  const [dateRange, setDateRange] = useState<[dayjs.Dayjs | null, dayjs.Dayjs | null]>([null, null])
  const [status, setStatus] = useState<string>(Status.ACTIVE)
  const [category, setCategory] = useState<string>('')

  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10
  })

  const {
    data,
    isLoading,
    error,
    request: fetchExpenses
  } = useApi<ExpenseResponse, [GetExpenseParams]>(expenseApi.getExpenses)

  useEffect(() => {
    loadExpenses()
  }, [
    pagination.current,
    pagination.pageSize,
    searchQuery,
    dateRange,
    status,
    category,
    refreshTrigger
  ])

  const loadExpenses = async () => {
    console.log({
      page: pagination.current,
      limit: pagination.pageSize,
      search: searchQuery,
      startDate: dateRange[0]?.toDate(),
      endDate: dateRange[1]?.toDate(),
      status,
      category: category
    })

    await fetchExpenses({
      page: pagination.current,
      limit: pagination.pageSize,
      search: searchQuery,
      startDate: dateRange[0]?.toDate(),
      endDate: dateRange[1]?.toDate(),
      status,
      category: category
    })
  }

  const handleTableChange = (pagination: any) => {
    setPagination({
      current: pagination.current,
      pageSize: pagination.pageSize
    })
  }

  const handleDateRangeChange: RangePickerProps['onChange'] = (dates) => {
    setDateRange(dates || [null, null])
  }

  const statusOptions = [
    { value: Status.ACTIVE, label: 'Active' },
    { value: Status.VOID, label: 'Void' },
    { value: 'ALL', label: 'All' }
  ]

  const columns: ColumnsType<ExpenseItem> = [
    {
      title: 'No',
      dataIndex: 'index',
      width: 60,
      render: (_, __, index) => index + 1 + (pagination.current - 1) * pagination.pageSize
    },
    {
      title: 'Date',
      dataIndex: 'date',
      key: 'date',
      width: 120,
      render: (date) => dayjs(date).format('YYYY-MM-DD')
    },
    {
      title: 'Category',
      dataIndex: 'category',
      key: 'category',
      width: 150
    },
    {
      title: 'Description',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true
    },
    {
      title: 'Amount',
      dataIndex: 'amount',
      key: 'amount',
      width: 120,
      render: (amount) => formatCurrency(amount)
    },
    {
      title: 'Payment Source',
      dataIndex: ['Ledger', 'cashSource'],
      key: 'paymentSource',
      width: 150,
      render: (source, record) => {
        if (!record.Ledger) return '-'

        if (record.Ledger.cashSource === 'BANK') {
          return `Bank: ${record.Ledger.bank?.name || 'Unknown'}`
        }

        return source === 'SMALL_COUNTER' ? 'Cash Counter' : 'Cash Vault'
      }
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status) => <Tag color={status === Status.ACTIVE ? 'green' : 'red'}>{status}</Tag>
    },
    {
      title: 'Created By',
      dataIndex: ['createdBy', 'name'],
      key: 'createdBy',
      width: 150
    },
    {
      title: 'Actions',
      key: 'actions',
      width: 100,
      render: (_, record) => (
        <Space>
          <Button type="text" icon={<EyeOutlined />} onClick={() => onExpenseClick(record.id)} />
          {record.status === Status.ACTIVE && (
            <Button
              type="text"
              danger
              icon={<DeleteOutlined />}
              onClick={() => onVoidExpense(record.id)}
            />
          )}
        </Space>
      )
    }
  ]

  // Render table title with expense totals
  const renderTableTitle = () => (
    <Space size="large" className="w-full justify-between px-2">
      <Typography.Text strong>
        Current Page Total:{' '}
        <Typography.Text type="success">
          {formatCurrency(data?.currentPageTotal || 0)}
        </Typography.Text>
      </Typography.Text>

      {data?.dateRangeTotal !== null && (
        <Typography.Text strong>
          {dateRange[0] && dateRange[1]
            ? `${dayjs(dateRange[0]).format('MMM D')} - ${dayjs(dateRange[1]).format('MMM D, YYYY')} Total:`
            : 'Date Range Total:'}
          <Typography.Text type="success">
            {formatCurrency(data?.dateRangeTotal || 0)}
          </Typography.Text>
        </Typography.Text>
      )}
    </Space>
  )

  return (
    <div className="space-y-4">
      {/* Filters */}
      <Form layout="vertical" className="mb-4">
        <Space wrap className="w-full">
          <Form.Item label="Search" className="mb-0">
            <Input
              placeholder="Search expenses..."
              prefix={<SearchOutlined />}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              allowClear
            />
          </Form.Item>

          <Form.Item label="Date Range" className="mb-0">
            <DatePicker.RangePicker
              value={dateRange}
              onChange={handleDateRangeChange}
              placeholder={['Start Date', 'End Date']}
            />
          </Form.Item>

          <Form.Item label="Status" className="mb-0">
            <Select
              value={status}
              onChange={setStatus}
              style={{ width: 120 }}
              options={statusOptions}
            />
          </Form.Item>

          <Form.Item label="Category" className="mb-0">
            <Input
              placeholder="Filter by category"
              value={category}
              onChange={(e) => setCategory(e.target.value)}
              allowClear
              style={{ width: 200 }}
            />
          </Form.Item>
        </Space>
      </Form>

      <Divider />

      {/* Table */}
      <Table
        title={renderTableTitle}
        columns={columns}
        dataSource={data?.expenses || []}
        rowKey="id"
        loading={isLoading}
        virtual
        sticky
        size="small"
        pagination={{
          position: ['topRight'],
          current: pagination.current,
          pageSize: pagination.pageSize,
          total: data?.total || 0,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total) => `Total ${total} items`,
          showPrevNextJumpers: true
        }}
        onChange={handleTableChange}
        // scroll={{ x: 1200 }}
      />
    </div>
  )
}
