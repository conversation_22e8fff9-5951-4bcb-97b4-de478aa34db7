import { CustomerType } from "@prisma/client";
import { prisma } from "../db";

class InvoiceNumberService {
    // POTENTIAL ISSUE: EXPIRY_MINUTES is hardcoded and not configurable
    private EXPIRY_MINUTES = 120;

    async generateInvoiceNumber(type: CustomerType, adminId: string) {
        return await prisma.$transaction(async (tx) => {
            const currentYear = new Date().getFullYear();

            // First, find any available (canceled or expired) numbers
            const availableNumber = await tx.invoiceNumber.findFirst({
                where: {
                    type,
                    year: currentYear,
                    status: 'AVAILABLE'
                },
                orderBy: {
                    number: 'asc'
                }
            });

            if (availableNumber) {
                // Reuse the available number
                return await tx.invoiceNumber.update({
                    where: { id: availableNumber.id },
                    data: {
                        status: 'RESERVED',
                        issuedAt: new Date(),
                        expiresAt: new Date(Date.now() + this.EXPIRY_MINUTES * 60000),
                        issuedById: adminId,
                        canceledAt: null
                    }
                });
            }

            // Get the last number for this type and year
            const lastNumber = await tx.invoiceNumber.findFirst({
                where: {
                    type,
                    year: currentYear,
                },
                orderBy: {
                    number: 'desc'
                }
            });

            const nextNumber = (lastNumber?.number ?? 0) + 1;

            // POTENTIAL ISSUE: Invoice number format validation
            // The format is consistent (prefix-year-padded number), but there's no validation
            // to ensure that manually entered invoice numbers follow this format.
            // Consider adding a validation function for invoice numbers.

            // POTENTIAL ISSUE: Year transitions
            // When the year changes, the numbering resets to 1, which is correct.
            // However, there's no explicit handling for year transitions.
            // Consider adding a scheduled job that runs at the beginning of each year
            // to ensure the first invoice of the year gets number 1.

            const prefix = type === 'WALK_IN' ? 'WI' : 'REG';
            const formattedNumber = `${prefix}-${currentYear}-${nextNumber.toString().padStart(5, '0')}`;

            return await tx.invoiceNumber.create({
                data: {
                    number: nextNumber,
                    invoiceNumber: formattedNumber,
                    type,
                    year: currentYear,
                    expiresAt: new Date(Date.now() + this.EXPIRY_MINUTES * 60000),
                    issuedById: adminId
                }
            });
        });
    }

    async confirmInvoiceNumber(invoiceNumber: string) {
        return await prisma.invoiceNumber.update({
            where: { invoiceNumber },
            data: {
                status: 'CONFIRMED',
                confirmedAt: new Date()
            }
        });
    }

    async cancelInvoiceNumber(invoiceNumber: string) {
        return await prisma.invoiceNumber.update({
            where: { invoiceNumber },
            data: {
                status: 'AVAILABLE',
                canceledAt: new Date()
            }
        });
    }

    async cleanupExpiredNumbers() {
        return await prisma.invoiceNumber.updateMany({
            where: {
                status: 'RESERVED',
                expiresAt: {
                    lt: new Date()
                }
            },
            data: {
                status: 'AVAILABLE'
            }
        });
    }
}

export const invoiceNumberService = new InvoiceNumberService();
