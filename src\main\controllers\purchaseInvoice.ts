import { Prisma } from '@prisma/client';
import { IRequest } from '../../common';
import { purchaseInvoiceService } from '../services';
import {
    CreatePurchaseInvoiceData,
    GetPurchaseInvoicesParams,
    VoidPurchaseInvoiceParams
} from '@/common/types/purchaseInvoice';

class PurchaseInvoiceController {
    async createPurchaseInvoice(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const data = req.body as CreatePurchaseInvoiceData;

        if (!data.vendorId || !data.invoiceNumber || !data.items?.length) {
            throw new Error("Missing required fields for purchase invoice creation.");
        }

        return await purchaseInvoiceService.create(data);
    }

    async voidPurchaseInvoice(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { id, adminId, reason } = req.body as VoidPurchaseInvoiceParams;

        if (!id || !adminId || !reason) {
            throw new Error("Missing required fields for voiding purchase invoice.");
        }

        return await purchaseInvoiceService.void(id, adminId, reason);
    }

    async getPurchaseInvoiceById(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { id } = req.params ?? {};
        if (!id) throw new Error("Purchase Invoice ID is required");

        return await purchaseInvoiceService.getById(id);
    }

    async getAllPurchaseInvoices(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { page = 1, limit = 10, search, vendorId, startDate, endDate, status } = req.query ?? {};
        return await purchaseInvoiceService.getAll({
            page,
            limit,
            search,
            vendorId,
            startDate: startDate ? new Date(startDate) : undefined,
            endDate: endDate ? new Date(endDate) : undefined,
            status
        });
    }

    async getPurchaseInvoicesByVendor(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { vendorId } = req.params ?? {};
        const { page = 1, limit = 10 } = req.query ?? {};

        if (!vendorId) throw new Error("Vendor ID is required");

        return await purchaseInvoiceService.getByVendor(vendorId, { page, limit });
    }
}

export const purchaseInvoiceController = new PurchaseInvoiceController();