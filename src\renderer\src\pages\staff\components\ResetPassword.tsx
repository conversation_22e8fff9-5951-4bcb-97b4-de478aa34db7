import { Form, Input, Select, Button, message } from 'antd'
import { useState, useEffect } from 'react'
import { userApi } from '@/renderer/services'
import type { GetUsersResponse } from '@/common/types'
import { useApi } from '@/renderer/hooks'
import { useSelector } from 'react-redux'
import { IRootState } from '@/renderer/redux'

interface ResetPasswordProps {
  onSuccess: () => void
}

export const ResetPassword = ({ onSuccess }: ResetPasswordProps) => {
  const [form] = Form.useForm()
  const [loading, setLoading] = useState(false)

  const user = useSelector((state: IRootState) => state.user.data)

  const {
    data,
    isLoading,
    request: fetchUsers
  } = useApi<GetUsersResponse, [{ page: number; limit: number; includeInactive: boolean }]>(
    userApi.getUsers
  )

  useEffect(() => {
    fetchUsers({ page: 1, limit: 100, includeInactive: false })
  }, [])

  const handleSubmit = async (values: { userId: string; newPassword: string }) => {
    setLoading(true)
    const response = await userApi.resetPassword({
      userId: values.userId,
      newPassword: values.newPassword,
      adminId: user?.id || ''
    })
    setLoading(false)

    if (response.error.error || response.data.error) {
      message.error(response.error.message || response.data.error.message)
      return
    }

    message.success('Password reset successfully')
    form.resetFields()
    onSuccess()
  }

  const userOptions =
    data?.users.map((user) => ({
      label: `${user.name} (${user.username})`,
      value: user.id
    })) || []

  return (
    <div className="max-w-lg">
      <Form form={form} layout="vertical" onFinish={handleSubmit} requiredMark={false}>
        <Form.Item
          label="Select User"
          name="userId"
          rules={[{ required: true, message: 'Please select a user' }]}
        >
          <Select
            showSearch
            placeholder="Select user"
            loading={isLoading}
            options={userOptions}
            filterOption={(input, option) =>
              (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
            }
          />
        </Form.Item>

        <Form.Item
          label="New Password"
          name="newPassword"
          rules={[
            { required: true, message: 'Please enter new password' },
            { min: 6, message: 'Password must be at least 6 characters' }
          ]}
        >
          <Input.Password placeholder="Enter new password" />
        </Form.Item>

        <Form.Item
          name="confirmPassword"
          label="Confirm Password"
          dependencies={['newPassword']}
          rules={[
            { required: true, message: 'Please confirm password' },
            ({ getFieldValue }) => ({
              validator(_, value) {
                if (!value || getFieldValue('newPassword') === value) {
                  return Promise.resolve()
                }
                return Promise.reject(new Error('Passwords do not match'))
              }
            })
          ]}
        >
          <Input.Password placeholder="Confirm new password" />
        </Form.Item>

        <Form.Item>
          <Button type="primary" htmlType="submit" loading={loading}>
            Reset Password
          </Button>
        </Form.Item>
      </Form>
    </div>
  )
}
