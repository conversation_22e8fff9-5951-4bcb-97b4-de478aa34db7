import { prisma } from '../db'
import { CustomerType, Status, CreditDebit, LedgerType, PurchaseInvoiceType } from '@prisma/client'
import {
    StockReturnItem,
    SaleInvoiceDetails,
    ProcessStockReturnParams,
    VoidReturnStockParams,
    GetStockReturnParams,
    GetSaleInvoiceByNumberParams,
    ProcessLegacyStockReturnParams,
    StockReturnType,
    StockReturnStatus,
    StockReturnSortOrder
} from '@/common/types/stockReturn'

class StockReturnService {
    /**
     * Gets or creates a special vendor for handling stock returns.
     * 
     * Note: This creates a special vendor that should never be deleted.
     * The application should prevent deletion of this vendor through the UI.
     * If this vendor is deleted, stock returns will fail until it's recreated.
     */
    private async getOrCreateReturnStockVendor() {
        const returnStockVendor = await prisma.party.findFirst({
            where: {
                name: 'RETURN_STOCK',
                type: 'VENDOR'
            }
        })

        if (returnStockVendor) {
            return returnStockVendor
        }

        return await prisma.party.create({
            data: {
                name: 'RETURN_STOCK',
                type: 'VENDOR',
                currentBalance: 0 // This vendor's balance will always remain 0
            }
        })
    }

    /**
     * Generates a return invoice number based on the original invoice number.
     * 
     * Note: This method creates invoice numbers that include the original invoice number
     * plus a counter in parentheses for multiple returns of the same invoice.
     * This approach ensures traceability but could lead to very long invoice numbers
     * if the original invoice number is already long.
     * A more robust approach might be to use a separate sequence for returns
     * and maintain the relationship in the database.
     */
    private async generateReturnInvoiceNumber(originalInvoiceNumber: string): Promise<string> {
        // Count existing returns for this invoice
        const existingReturns = await prisma.purchaseInvoice.count({
            where: {
                invoiceNumber: {
                    startsWith: originalInvoiceNumber
                },
                vendor: {
                    name: 'RETURN_STOCK'
                },
                // status: Status.ACTIVE
            }
        })

        // If there are existing returns, append the count + 1 in parentheses
        return existingReturns > 0
            ? `${originalInvoiceNumber}(${existingReturns + 1})`
            : originalInvoiceNumber
    }

    private async getReturnedQuantities(invoiceNumber: string) {
        const returnedItems = await prisma.purchaseInvoice.findMany({
            where: {
                vendor: {
                    name: 'RETURN_STOCK'
                },
                invoiceNumber: {
                    startsWith: invoiceNumber
                },
                status: Status.ACTIVE // Only count active (non-voided) returns
            },
            include: {
                items: {
                    include: {
                        product: true
                    }
                }
            }
        })

        // Sum up returned quantities by product
        const returnedQuantities: { [productId: string]: number } = {}
        for (const invoice of returnedItems) {
            for (const item of invoice.items) {
                returnedQuantities[item.productId] = (returnedQuantities[item.productId] || 0) + item.quantity
            }
        }

        return returnedQuantities
    }

    async getSaleInvoiceByNumber({ invoiceNumber, customerType }: GetSaleInvoiceByNumberParams): Promise<SaleInvoiceDetails | null> {
        try {
            let invoice: any = null

            if (customerType === CustomerType.REGISTERED) {
                invoice = await prisma.saleInvoice.findFirst({
                    where: {
                        invoiceNumber,
                        customerType: CustomerType.REGISTERED,
                        status: Status.ACTIVE
                    },
                    select: {
                        id: true,
                        invoiceNumber: true,
                        date: true,
                        customerType: true,
                        customer: {
                            select: {
                                name: true
                            }
                        },
                        discountAmount: true,
                        totalAmount: true,
                        items: {
                            select: {
                                id: true,
                                productId: true,
                                product: {
                                    select: {
                                        name: true,
                                        productId: true,
                                        tag: true,
                                        nature: true,
                                        category: {
                                            select: {
                                                name: true
                                            }
                                        }
                                    }
                                },
                                totalQuantity: true,
                                salePrice: true,
                                total: true
                            }
                        }
                    }
                })
            } else {
                invoice = await prisma.walkInSaleInvoice.findFirst({
                    where: {
                        invoiceNumber,
                        customerType: CustomerType.WALK_IN,
                        status: Status.ACTIVE
                    },
                    select: {
                        id: true,
                        invoiceNumber: true,
                        date: true,
                        customerType: true,
                        customerName: true,
                        discountAmount: true,
                        totalAmount: true,
                        items: {
                            select: {
                                id: true,
                                productId: true,
                                product: {
                                    select: {
                                        name: true,
                                        productId: true,
                                        tag: true,
                                        nature: true,
                                        category: {
                                            select: {
                                                name: true
                                            }
                                        }
                                    }
                                },
                                totalQuantity: true,
                                salePrice: true,
                                total: true
                            }
                        }
                    }
                })
            }

            if (!invoice) return null

            // Get already returned quantities
            const returnedQuantities = await this.getReturnedQuantities(invoiceNumber)

            // Add returned quantities to items
            const itemsWithReturns = invoice.items.map((item: any) => ({
                ...item,
                returnedQuantity: returnedQuantities[item.productId] || 0
            }))

            return {
                ...invoice,
                customerName: customerType === CustomerType.REGISTERED ? invoice.customer.name : invoice.customerName,
                items: itemsWithReturns
            }
        } catch (error) {
            throw error
        }
    }

    async processStockReturn({ items, originalInvoiceNumber, adminId, paymentDetails }: ProcessStockReturnParams) {
        try {
            // Validate items
            if (!items || items.length === 0) {
                throw new Error('No items provided for stock return')
            }

            // Validate quantities
            for (const item of items) {
                if (item.quantity <= 0) {
                    throw new Error(`Return quantity must be positive for product ${item.productId}`)
                }
                if (item.purchasePrice < 0) {
                    throw new Error(`Purchase price cannot be negative for product ${item.productId}`)
                }
            }

            return await prisma.$transaction(async (tx) => {
                // Get already returned quantities
                const returnedQuantities = await this.getReturnedQuantities(originalInvoiceNumber)

                // Get original invoice to determine customer type
                const originalInvoice = await tx.saleInvoice.findFirst({
                    where: { invoiceNumber: originalInvoiceNumber },
                    include: {
                        customer: true
                    }
                }) || await tx.walkInSaleInvoice.findFirst({
                    where: { invoiceNumber: originalInvoiceNumber }
                })

                if (!originalInvoice) {
                    throw new Error('Original invoice not found')
                }

                const isWalkInSale = 'customerName' in originalInvoice
                const customerId = !isWalkInSale && 'customer' in originalInvoice ? originalInvoice.customer?.id : undefined

                // For walk-in sales, payment details are required
                if (isWalkInSale && !paymentDetails) {
                    throw new Error('Payment details are required for walk-in sale returns')
                }

                // Validate return quantities
                for (const item of items) {
                    const alreadyReturned = returnedQuantities[item.productId] || 0
                    const originalItem = await tx.saleItem.findFirst({
                        where: {
                            saleInvoiceId: item.originalInvoiceId,
                            productId: item.productId
                        }
                    }) || await tx.walkInSaleItem.findFirst({
                        where: {
                            saleInvoiceId: item.originalInvoiceId,
                            productId: item.productId
                        }
                    })

                    if (!originalItem) {
                        throw new Error(`Item ${item.productId} not found in original invoice`)
                    }

                    if (item.quantity + alreadyReturned > originalItem.totalQuantity) {
                        throw new Error(`Cannot return more than sold quantity for product ${item.productId}`)
                    }
                }

                // Get or create the special return stock vendor
                const returnStockVendor = await this.getOrCreateReturnStockVendor()

                // Generate return invoice number based on original invoice
                const returnInvoiceNumber = await this.generateReturnInvoiceNumber(originalInvoiceNumber)

                // Calculate total return amount
                const totalReturnAmount = items.reduce((sum, item) => sum + (item.quantity * item.purchasePrice), 0)

                // update the customer balance
                if (customerId) {
                    await tx.party.update({
                        where: { id: customerId },
                        data: { currentBalance: { increment: totalReturnAmount } }
                    })
                }

                // Get customer name for description (used in ledger description)
                let customerName = 'Walk-in Customer';
                if (customerId) {
                    const customer = await tx.party.findUnique({
                        where: { id: customerId },
                        select: { name: true }
                    });
                    customerName = customer?.name || 'Unknown Customer';
                } else if (isWalkInSale && 'customerName' in originalInvoice && originalInvoice.customerName) {
                    customerName = originalInvoice.customerName;
                }

                // Create a special purchase invoice for returned stock
                const purchaseInvoice = await tx.purchaseInvoice.create({
                    data: {
                        invoiceNumber: returnInvoiceNumber,
                        type: PurchaseInvoiceType.STOCK_RETURN,
                        totalAmount: totalReturnAmount,
                        paidAmount: 0,
                        previousBalance: 0,
                        newBalance: 0,
                        date: new Date(),
                        vendorId: returnStockVendor.id,
                        createdById: adminId,
                        status: Status.ACTIVE
                    }
                })

                // Create ledger entry for stock return (CREDIT)
                await tx.ledger.create({
                    data: {
                        date: new Date(),
                        amount: totalReturnAmount,
                        creditOrDebit: CreditDebit.CREDIT,
                        description: `Stock Return for Invoice #${originalInvoiceNumber} - Customer: ${customerName} - Type: ${isWalkInSale ? 'WALK_IN' : 'REGISTERED'}`,
                        referenceType: LedgerType.PurchaseInvoice,
                        purchaseRef: purchaseInvoice.id,
                        status: Status.ACTIVE,
                        createdById: adminId,
                        ...(customerId && {
                            partyId: customerId
                        })
                    }
                })

                // If payment details are provided (required for walk-in, optional for registered)
                if (paymentDetails) {
                    // Create payment record
                    const payment = await tx.payments.create({
                        data: {
                            date: new Date(),
                            type: 'PAID',
                            amount: totalReturnAmount,
                            paymentMethod: 'CASH',
                            sourceLocation: paymentDetails.source,
                            locationId: paymentDetails.bankId,
                            status: Status.ACTIVE,
                            createdById: adminId,
                            ...(customerId && {
                                partyId: customerId
                            })
                        }
                    })

                    // Create ledger entry for payment (DEBIT)
                    await tx.ledger.create({
                        data: {
                            date: new Date(),
                            amount: totalReturnAmount,
                            creditOrDebit: CreditDebit.DEBIT,
                            description: `Payment for Stock Return #${returnInvoiceNumber}`,
                            referenceType: LedgerType.Payment,
                            paymentRef: payment.id,
                            status: Status.ACTIVE,
                            bankId: paymentDetails.bankId,
                            cashSource: paymentDetails.source,
                            createdById: adminId,
                            ...(customerId && {
                                partyId: customerId
                            })
                        }
                    })

                    // update the customer balance
                    if (customerId) {
                        await tx.party.update({
                            where: { id: customerId },
                            data: { currentBalance: { decrement: totalReturnAmount } }
                        })
                    }

                    // Update cash location balance
                    switch (paymentDetails.source) {
                        case 'SMALL_COUNTER':
                            const smallCounter = await tx.smallCounter.findFirst({
                            })
                            if (!smallCounter) throw new Error('Small counter not found')
                            await tx.smallCounter.update({
                                where: { id: smallCounter.id },
                                data: { cashInShop: { decrement: totalReturnAmount } }
                            })
                            break
                        case 'CASH_VAULT':
                            const cashVault = await tx.cashVault.findFirst({
                            })
                            if (!cashVault) throw new Error('Cash vault not found')
                            await tx.cashVault.update({
                                where: { id: cashVault.id },
                                data: { balance: { decrement: totalReturnAmount } }
                            })
                            break
                        case 'BANK':
                            if (!paymentDetails.bankId) throw new Error('Bank ID is required for bank payment')
                            const bank = await tx.banks.findFirst({
                                where: { id: paymentDetails.bankId }
                            })
                            if (!bank) throw new Error('Bank not found')
                            await tx.banks.update({
                                where: { id: bank.id },
                                data: { balance: { decrement: totalReturnAmount } }
                            })
                            break
                    }
                }

                // Process each returned item
                for (const item of items) {
                    // Create purchase item record
                    await tx.purchaseItem.create({
                        data: {
                            quantity: item.quantity,
                            purchasePrice: item.purchasePrice,
                            total: item.quantity * item.purchasePrice,
                            purchaseInvoiceId: purchaseInvoice.id,
                            productId: item.productId
                        }
                    })

                    // Create stock entry for returned item
                    await tx.stock.create({
                        data: {
                            purchasePrice: item.purchasePrice,
                            quantity: item.quantity,
                            status: 'IN_STOCK',
                            productId: item.productId,
                            vendorId: returnStockVendor.id,
                            purchaseInvoiceId: purchaseInvoice.id
                        }
                    })

                    // Update product's quantity in stock
                    await tx.product.update({
                        where: { id: item.productId },
                        data: {
                            quantityInStock: {
                                increment: item.quantity
                            }
                        }
                    })
                }

                return purchaseInvoice
            })
        } catch (error) {
            throw error
        }
    }

    /**
     * Process a legacy stock return (for items purchased before system implementation)
     */
    async processLegacyStockReturn({
        items,
        customerId,
        customerName,
        customerType,
        adminId,
        approverNote,
        paymentDetails,
        returnDate
    }: ProcessLegacyStockReturnParams) {
        try {
            // Validate items
            if (!items || items.length === 0) {
                throw new Error('No items provided for legacy stock return')
            }

            // Validate quantities and prices
            for (const item of items) {
                if (item.quantity <= 0) {
                    throw new Error(`Return quantity must be positive for product ${item.productId}`)
                }
                if (item.purchasePrice < 0) {
                    throw new Error(`Purchase price cannot be negative for product ${item.productId}`)
                }
            }

            // Validate payment details for walk-in customers
            if (customerType === CustomerType.WALK_IN && !paymentDetails) {
                throw new Error('Payment details are required for walk-in customers')
            }

            return await prisma.$transaction(async (tx) => {
                // Get or create the special return stock vendor
                const returnStockVendor = await this.getOrCreateReturnStockVendor()

                // Generate a unique invoice number for the legacy return
                // Format: LEGACY-RETURN-YYYYMMDD-XXXX where XXXX is a sequential number
                const today = new Date()
                const dateString = today.toISOString().slice(0, 10).replace(/-/g, '')

                // Count existing legacy returns for today to generate sequential number
                const existingReturnsCount = await tx.purchaseInvoice.count({
                    where: {
                        invoiceNumber: {
                            startsWith: `LEGACY-RETURN-${dateString}`
                        },
                        type: PurchaseInvoiceType.LEGACY_STOCK_RETURN
                    }
                })

                const sequentialNumber = (existingReturnsCount + 1).toString().padStart(4, '0')
                const legacyReturnInvoiceNumber = `LEGACY-RETURN-${dateString}-${sequentialNumber}`

                // Calculate total return amount
                const totalReturnAmount = items.reduce((sum, item) => sum + (item.quantity * item.purchasePrice), 0)

                // Get customer name for description and ledger entries
                let customerNameForDescription = customerName || 'Unknown Customer'

                // If registered customer, update their balance and get their name
                if (customerId) {
                    const customer = await tx.party.findUnique({
                        where: { id: customerId },
                        select: { name: true }
                    })

                    if (customer) {
                        customerNameForDescription = customer.name

                        // Update customer balance (add the return amount)
                        await tx.party.update({
                            where: { id: customerId },
                            data: { currentBalance: { increment: totalReturnAmount } }
                        })
                    }
                }

                // Create a special purchase invoice for the legacy returned stock
                const purchaseInvoice = await tx.purchaseInvoice.create({
                    data: {
                        invoiceNumber: legacyReturnInvoiceNumber,
                        type: PurchaseInvoiceType.LEGACY_STOCK_RETURN,
                        totalAmount: totalReturnAmount,
                        paidAmount: 0,
                        previousBalance: 0,
                        newBalance: 0,
                        date: returnDate || new Date(),
                        vendorId: returnStockVendor.id,
                        createdById: adminId,
                        status: Status.ACTIVE,
                        voidingReason: approverNote // Store the approver note in this field for reference
                    }
                })

                // Create ledger entry for the legacy stock return (CREDIT)
                await tx.ledger.create({
                    data: {
                        date: new Date(),
                        amount: totalReturnAmount,
                        creditOrDebit: CreditDebit.CREDIT,
                        description: `Legacy Stock Return: ${legacyReturnInvoiceNumber} - Customer: ${customerNameForDescription} - Type: ${customerType}`,
                        referenceType: LedgerType.PurchaseInvoice,
                        purchaseRef: purchaseInvoice.id,
                        status: Status.ACTIVE,
                        createdById: adminId,
                        ...(customerId && { partyId: customerId })
                    }
                })

                // If payment details are provided (optional for registered customers, required for walk-in)
                if (paymentDetails) {
                    // Create payment record
                    const payment = await tx.payments.create({
                        data: {
                            date: new Date(),
                            type: 'PAID',
                            amount: totalReturnAmount,
                            paymentMethod: 'CASH',
                            sourceLocation: paymentDetails.source,
                            locationId: paymentDetails.bankId,
                            status: Status.ACTIVE,
                            createdById: adminId,
                            ...(customerId && { partyId: customerId })
                        }
                    })

                    // Create ledger entry for payment (DEBIT)
                    await tx.ledger.create({
                        data: {
                            date: new Date(),
                            amount: totalReturnAmount,
                            creditOrDebit: CreditDebit.DEBIT,
                            description: `Payment for Legacy Stock Return: ${legacyReturnInvoiceNumber}`,
                            referenceType: LedgerType.Payment,
                            paymentRef: payment.id,
                            status: Status.ACTIVE,
                            bankId: paymentDetails.bankId,
                            cashSource: paymentDetails.source,
                            createdById: adminId,
                            ...(customerId && {
                                partyId: customerId
                            })
                        }
                    })

                    // If registered customer, update their balance (deduct payment amount)
                    if (customerId) {
                        await tx.party.update({
                            where: { id: customerId },
                            data: { currentBalance: { decrement: totalReturnAmount } }
                        })
                    }

                    // Update cash location balance
                    switch (paymentDetails.source) {
                        case 'SMALL_COUNTER':
                            const smallCounter = await tx.smallCounter.findFirst({})
                            if (!smallCounter) throw new Error('Small counter not found')
                            await tx.smallCounter.update({
                                where: { id: smallCounter.id },
                                data: { cashInShop: { decrement: totalReturnAmount } }
                            })
                            break
                        case 'CASH_VAULT':
                            const cashVault = await tx.cashVault.findFirst({})
                            if (!cashVault) throw new Error('Cash vault not found')
                            await tx.cashVault.update({
                                where: { id: cashVault.id },
                                data: { balance: { decrement: totalReturnAmount } }
                            })
                            break
                        case 'BANK':
                            if (!paymentDetails.bankId) throw new Error('Bank ID is required for bank payment')
                            const bank = await tx.banks.findFirst({
                                where: { id: paymentDetails.bankId }
                            })
                            if (!bank) throw new Error('Bank not found')
                            await tx.banks.update({
                                where: { id: bank.id },
                                data: { balance: { decrement: totalReturnAmount } }
                            })
                            break
                    }
                }

                // Process each returned item
                for (const item of items) {
                    // Create purchase item record
                    await tx.purchaseItem.create({
                        data: {
                            quantity: item.quantity,
                            purchasePrice: item.purchasePrice,
                            total: item.quantity * item.purchasePrice,
                            purchaseInvoiceId: purchaseInvoice.id,
                            productId: item.productId
                        }
                    })

                    // Create stock entry for returned item
                    await tx.stock.create({
                        data: {
                            purchasePrice: item.purchasePrice,
                            quantity: item.quantity,
                            status: 'IN_STOCK',
                            productId: item.productId,
                            vendorId: returnStockVendor.id,
                            purchaseInvoiceId: purchaseInvoice.id
                        }
                    })

                    // Update product's quantity in stock
                    await tx.product.update({
                        where: { id: item.productId },
                        data: {
                            quantityInStock: {
                                increment: item.quantity
                            }
                        }
                    })
                }

                return purchaseInvoice
            })
        } catch (error) {
            throw error
        }
    }

    // Enhanced getReturnedStock with comprehensive filtering and optimization
    async getReturnedStock(params: GetStockReturnParams) {
        const returnStockVendor = await prisma.party.findFirst({
            where: {
                name: 'RETURN_STOCK',
                type: 'VENDOR'
            }
        })

        if (!returnStockVendor) {
            return {
                data: [],
                total: 0,
                page: params.page || 1,
                totalPages: 0
            }
        }

        // Set default values
        const page = params.page || 1
        const limit = params.limit || 10
        const skip = (page - 1) * limit
        const returnType = params.returnType || StockReturnType.ALL
        const status = params.status || StockReturnStatus.ALL
        const sortOrder = params.sortOrder || StockReturnSortOrder.NEWEST_FIRST

        // Build where clause
        let whereClause: any = {
            vendorId: returnStockVendor.id,
            type: {
                in: [PurchaseInvoiceType.STOCK_RETURN, PurchaseInvoiceType.LEGACY_STOCK_RETURN]
            }
        }

        // Date filtering
        if (params.startDate && params.endDate) {
            if (params.startDate.toDateString() === params.endDate.toDateString()) {
                // Same day - filter for entire day
                const start = new Date(params.startDate)
                start.setHours(0, 0, 0, 0)
                const end = new Date(params.startDate)
                end.setHours(23, 59, 59, 999)
                whereClause.date = {
                    gte: start,
                    lte: end
                }
            } else {
                // Date range
                whereClause.date = {
                    gte: params.startDate,
                    lte: params.endDate
                }
            }
        } else if (params.startDate) {
            whereClause.date = { gte: params.startDate }
        } else if (params.endDate) {
            whereClause.date = { lte: params.endDate }
        }

        // Status filtering
        if (status === StockReturnStatus.ACTIVE) {
            whereClause.status = Status.ACTIVE
        } else if (status === StockReturnStatus.VOIDED) {
            whereClause.status = Status.VOID
        }

        // Return type filtering - only filter by legacy vs non-legacy at database level
        if (returnType === StockReturnType.LEGACY_RETURN) {
            whereClause.type = PurchaseInvoiceType.LEGACY_STOCK_RETURN
        } else if (returnType !== StockReturnType.ALL) {
            // For walk-in and registered, we need to filter at application level after getting data
            whereClause.type = PurchaseInvoiceType.STOCK_RETURN
        }

        // Product filtering - if productId is specified, only get returns that include this product
        if (params.productId) {
            whereClause.items = {
                some: {
                    product: {
                        productId: params.productId
                    }
                }
            }
        }

        // Search functionality - search in invoice number only
        if (params.search) {
            whereClause.invoiceNumber = {
                contains: params.search,
                mode: 'insensitive'
            }
        }

        // Note: Customer filtering removed due to Prisma relationship complexity
        // This can be implemented as post-processing if needed

        // Determine sort order
        let orderBy: any = { date: 'desc' } // Default newest first

        if (sortOrder === StockReturnSortOrder.OLDEST_FIRST) {
            orderBy = { date: 'asc' }
        } else if (sortOrder === StockReturnSortOrder.AMOUNT_HIGH_TO_LOW) {
            orderBy = { totalAmount: 'desc' }
        } else if (sortOrder === StockReturnSortOrder.AMOUNT_LOW_TO_HIGH) {
            orderBy = { totalAmount: 'asc' }
        }

        // Get total count for pagination
        const totalCount = await prisma.purchaseInvoice.count({
            where: whereClause
        })

        // Get paginated data with optimized includes
        const returnedStock = await prisma.purchaseInvoice.findMany({
            where: whereClause,
            include: {
                items: {
                    include: {
                        product: {
                            select: {
                                name: true,
                                productId: true,
                                tag: true,
                                nature: true,
                                category: {
                                    select: {
                                        name: true
                                    }
                                }
                            }
                        }
                    }
                },
                createdBy: {
                    select: {
                        name: true
                    }
                },
                // Get ledger information to identify customer
                Ledger: {
                    include: {
                        party: {
                            select: {
                                id: true,
                                name: true,
                                phoneNumber: true,
                                type: true
                            }
                        }
                    }
                }
            },
            orderBy,
            skip,
            take: limit
        })

        // Process the returned data to include customer information in a more accessible format
        const processedData = returnedStock.map((stock: any) => {
            // Extract customer info from the ledger if available (one-to-one relationship)
            const customer = stock.Ledger?.party

            // Get original invoice number by extracting it from the invoice number
            // For legacy returns, it will be the generated legacy number
            const originalInvoiceNumber = stock.type === PurchaseInvoiceType.STOCK_RETURN
                ? stock.invoiceNumber.split('(')[0]
                : null

            // Determine customer type and return type based on ledger party info
            let customerType = 'WALK_IN'
            let returnTypeForFilter = 'WALK_IN_RETURN'

            if (stock.type === PurchaseInvoiceType.LEGACY_STOCK_RETURN) {
                customerType = 'LEGACY'
                returnTypeForFilter = 'LEGACY_RETURN'
            } else if (customer && customer.type === 'CUSTOMER') {
                // If there's a customer party in the ledger, it's a registered return
                customerType = 'REGISTERED'
                returnTypeForFilter = 'REGISTERED_RETURN'
            }
            // If no customer in ledger and not legacy, it's a walk-in return (default)

            return {
                ...stock,
                // Remove the ledger from the response to clean it up
                Ledger: undefined,
                // Add the customer info if available
                customer: customer ? {
                    id: customer.id,
                    name: customer.name,
                    phoneNumber: customer.phoneNumber || null
                } : null,
                customerType,
                type: stock.type,
                returnType: returnTypeForFilter,
                originalInvoiceNumber
            }
        })

        // Apply additional filtering for return type and customer (post-processing)
        let filteredData = processedData

        // Filter by return type if needed
        if (returnType === StockReturnType.WALK_IN_RETURN) {
            filteredData = filteredData.filter(item => item.returnType === 'WALK_IN_RETURN')
        } else if (returnType === StockReturnType.REGISTERED_RETURN) {
            filteredData = filteredData.filter(item => item.returnType === 'REGISTERED_RETURN')
        }

        // Note: Customer filtering removed due to complexity and performance concerns

        return {
            data: filteredData,
            total: totalCount, // Note: total count may not match filtered data due to post-processing
            page,
            totalPages: Math.ceil(totalCount / limit)
        }
    }

    async voidReturnStock({ returnInvoiceId, adminId, reason }: VoidReturnStockParams) {
        return await prisma.$transaction(async (tx) => {
            const returnInvoice = await tx.purchaseInvoice.findUnique({
                where: { id: returnInvoiceId, type: PurchaseInvoiceType.STOCK_RETURN },
                include: {
                    items: true,
                    Stock: {
                        include: {
                            StockEntry: {
                                include: {
                                    saleItem: true,
                                    WalkInSaleItem: true
                                }
                            }
                        }
                    },
                    Ledger: {
                        where: {
                            referenceType: LedgerType.Payment
                        },
                        include: {
                            payment: true
                        }
                    }
                }
            })

            if (!returnInvoice) {
                throw new Error('Return stock invoice not found')
            }

            // Check if already voided
            if (returnInvoice.status === Status.VOID) {
                throw new Error('Return stock invoice is already voided')
            }

            // Check if any of the returned stock has been resold
            for (const item of returnInvoice.items) {
                // Find associated stock records for this return
                const stockRecords = returnInvoice.Stock.filter(s => s.productId === item.productId)

                // Check if any of the stock has been used in sales
                for (const stock of stockRecords) {
                    const usedInSales = stock.StockEntry.some(entry =>
                        entry.saleItem || entry.WalkInSaleItem
                    )

                    if (usedInSales) {
                        // If stock has been resold, check if we have enough other stock to cover the void
                        const currentStock = await tx.product.findUnique({
                            where: { id: item.productId },
                            select: { quantityInStock: true, name: true }
                        })

                        if (!currentStock || currentStock.quantityInStock < item.quantity) {
                            throw new Error(
                                `Cannot void return: Some of the returned ${currentStock?.name || 'product'} has been resold and current stock (${currentStock?.quantityInStock}) is insufficient to cover the void operation (${item.quantity} needed)`
                            )
                        }
                    }
                }
            }

            // Update the invoice status
            await tx.purchaseInvoice.update({
                where: { id: returnInvoiceId },
                data: {
                    status: Status.VOID,
                    voidedById: adminId,
                    voidedAt: new Date(),
                    voidingReason: reason
                }
            })

            // Void associated ledger entries
            await tx.ledger.updateMany({
                where: { purchaseRef: returnInvoiceId },
                data: {
                    status: Status.VOID,
                    voidedById: adminId
                }
            })

            // If there was a payment, void it and restore cash balance
            const paymentLedger = returnInvoice.Ledger?.[0]
            if (paymentLedger?.payment) {
                const payment = paymentLedger.payment

                // Restore cash balance
                switch (payment.sourceLocation) {
                    case 'SMALL_COUNTER':
                        await tx.smallCounter.updateMany({
                            data: { cashInShop: { increment: payment.amount } }
                        })
                        break
                    case 'CASH_VAULT':
                        await tx.cashVault.updateMany({
                            data: { balance: { increment: payment.amount } }
                        })
                        break
                    case 'BANK':
                        if (!payment.locationId) throw new Error('Bank ID not found')
                        await tx.banks.update({
                            where: { id: payment.locationId },
                            data: { balance: { increment: payment.amount } }
                        })
                        break
                }

                // Void the payment
                await tx.payments.update({
                    where: { id: payment.id },
                    data: {
                        status: Status.VOID,
                        voidedById: adminId,
                        voidedAt: new Date(),
                        voidingReason: reason
                    }
                })
            }

            // Mark associated stock records as SOLD_OUT
            await tx.stock.updateMany({
                where: { purchaseInvoiceId: returnInvoiceId },
                data: { status: 'SOLD_OUT' }
            })

            // Revert the product quantities
            for (const item of returnInvoice.items) {
                await tx.product.update({
                    where: { id: item.productId },
                    data: {
                        quantityInStock: {
                            decrement: item.quantity
                        }
                    }
                })
            }

            return returnInvoice
        })
    }
}

export const stockReturnService = new StockReturnService()
