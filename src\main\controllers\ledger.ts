import { IRequest } from '../../common';
import { ledgerService } from "../services";




class LedgerController {

    async getLedgerEntriesByParty(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { partyId } = req.params ?? {};
        const { page, limit, startDate, endDate } = req.query ?? {};

        if (!partyId) throw new Error("Party ID is required");

        return await ledgerService.getPaginatedLedgerEntries(partyId, {
            page: page ? Number(page) : undefined,
            limit: limit ? Number(limit) : undefined,
            startDate: startDate ? new Date(startDate) : undefined,
            endDate: endDate ? new Date(endDate) : undefined
        });
    }

    async getDailyLedgerEntries(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { date } = req.query ?? {};

        if (!date) throw new Error("Date is required");

        return await ledgerService.getDailyLedger({
            date: new Date(date)
        });
    }

    async getLedgerEntriesForPDF(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { partyId } = req.params ?? {};
        if (!partyId) throw new Error("Party ID is required");
        return await ledgerService.getLedgerEntriesForPDF(partyId);
    }

}


export const ledgerController = new LedgerController();


