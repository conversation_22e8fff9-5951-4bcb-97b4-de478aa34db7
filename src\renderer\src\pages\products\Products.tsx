import { useState } from 'react'
import { Button, Input, Form, Select, Card, Space, Modal } from 'antd'
import { ProductList } from './components/ProductList'
import { CreateProductDrawer } from './components/CreateProductDrawer'
import { ProductDetailsModal } from './components/ProductDetailsModal'
import { SearchOutlined, PlusOutlined, FileTextOutlined } from '@ant-design/icons'
import './Products.scss'
import { useProductContext, useTheme } from '@/renderer/contexts'
import ProductQuantityReport from './components/ProductQuantityReport'

const Products = () => {
  const [isCreateDrawerOpen, setIsCreateDrawerOpen] = useState(false)
  const [selectedProduct, setSelectedProduct] = useState<string | null>(null)
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedProductId, setSelectedProductId] = useState<string | null>(null)
  const { products, loading } = useProductContext()

  const [refreshTrigger, setRefreshTrigger] = useState(0)

  const { isDarkMode } = useTheme()

  const [reportVisible, setReportVisible] = useState(false)

  const handleCreateProduct = () => {
    setIsCreateDrawerOpen(true)
  }

  const handleProductClick = (productId: string) => {
    setSelectedProduct(productId)
  }

  const handleSearchChange = (value: string) => {
    setSearchQuery(value)
    setSelectedProductId(null) // Clear selected product when searching
  }

  const handleProductSelect = (value: string | null) => {
    setSelectedProductId(value)
    setSearchQuery('') // Clear search when selecting a product
  }

  const handleReportClick = () => {
    setReportVisible(true)
  }

  return (
    <Card
      className={`products-container bg-[length:200%_200%] bg-[center] shadow-lg ${
        isDarkMode
          ? 'bg-gradient-to-bl from-indigo-950 via-black to-indigo-900 shadow-indigo-950'
          : 'bg-gradient-to-bl from-indigo-200 via-white to-indigo-100 shadow-indigo-200'
      }`}
    >
      <Space wrap className="products-header">
        <Space wrap>
          <Form.Item className="search-input">
            <Input
              placeholder="Search products..."
              prefix={<SearchOutlined />}
              onChange={(e) => handleSearchChange(e.target.value)}
              value={searchQuery}
              // size="large"
            />
          </Form.Item>
          <Select
            // size="large"
            style={{ width: '300px' }}
            loading={loading}
            showSearch
            placeholder="Select a product"
            optionFilterProp="children"
            allowClear
            value={selectedProductId}
            onChange={handleProductSelect}
            filterOption={(input, option) =>
              (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
            }
            options={products}
          />
        </Space>
        <Space>
          <Button type="primary" icon={<FileTextOutlined />} onClick={handleReportClick}>
            Inventory Report
          </Button>
          <Button
            className="bg-green-500 hover:!bg-green-600"
            type="primary"
            icon={<PlusOutlined />}
            onClick={handleCreateProduct}
            // size="large"
          >
            Create Product
          </Button>
        </Space>
      </Space>

      <ProductList
        onProductClick={handleProductClick}
        searchQuery={searchQuery}
        selectedProductId={selectedProductId}
        refreshTrigger={refreshTrigger}
      />

      <CreateProductDrawer
        open={isCreateDrawerOpen}
        onClose={() => setIsCreateDrawerOpen(false)}
        setRefreshTrigger={setRefreshTrigger}
      />

      <ProductDetailsModal
        productId={selectedProduct}
        open={!!selectedProduct}
        onClose={() => setSelectedProduct(null)}
        setRefreshTrigger={setRefreshTrigger}
      />

      <Modal
        title="Product Inventory Report"
        centered
        open={reportVisible}
        onCancel={() => setReportVisible(false)}
        footer={null}
        // width="80%"
        destroyOnClose
      >
        <ProductQuantityReport onClose={() => setReportVisible(false)} />
      </Modal>
    </Card>
  )
}

export default Products
