import jsPDF from 'jspdf'
import 'jspdf-autotable'
import dayjs from 'dayjs'
import { PartyListItem, PartySummary, PartyType } from '@/common/types/party'
import { formatCurrency } from '.'

interface GeneratePartyListPDFParams {
    parties: PartyListItem[]
    summary: PartySummary
    partyType: PartyType
    shouldSave?: boolean
}

export const generatePartyListPDF = async ({
    parties,
    summary,
    partyType,
    shouldSave = false
}: GeneratePartyListPDFParams): Promise<jsPDF | void> => {
    try {
        // Create a new PDF document
        const doc = new jsPDF({
            orientation: 'portrait',
            unit: 'mm',
            format: 'a4'
        })

        // Set up the document
        const pageWidth = doc.internal.pageSize.width
        const pageHeight = doc.internal.pageSize.height
        const margin = 10

        // Get title based on party type
        const getTitle = () => {
            switch (partyType) {
                case PartyType.VENDOR:
                    return 'Vendor List'
                case PartyType.CUSTOMER:
                    return 'Customer List'
                case PartyType.CREDITOR:
                    return 'Creditor List'
                default:
                    return 'Party List'
            }
        }

        // Function to add header to the first page
        const addFirstPageHeader = () => {
            // Add company name
            doc.setFont('helvetica', 'bold')
            doc.setFontSize(16)
            doc.text('SJ LACE', pageWidth / 2, 10, { align: 'center' })

            // Add report title
            doc.setFontSize(12)
            doc.text(getTitle(), pageWidth / 2, 22, { align: 'center' })

            // Add date
            doc.setFontSize(10)
            doc.text(`Date: ${dayjs().format('DD/MM/YYYY')}`, pageWidth - margin, 15, { align: 'right' })

            // Add summary information
            doc.setFontSize(9)
            doc.text(`Total ${getTitle().replace(' List', '')}: ${summary.totalCount}`, margin, 30)
            doc.text(`Total Payables: ${formatCurrency(summary.totalPositiveBalance)}`, margin, 35)
            doc.text(`Total Receivables: ${formatCurrency(summary.totalNegativeBalance)}`, pageWidth - margin - 70, 30, { align: 'left' })
            doc.text(`Net Balance: ${formatCurrency(summary.netBalance)}`, pageWidth - margin - 70, 35, { align: 'left' })

            return 45 // Return starting Y position for table
        }

        // Function to add header to subsequent pages (only company name)
        const addSubsequentPageHeader = () => {
            // Add only company name
            doc.setFont('helvetica', 'bold')
            doc.setFontSize(16)
            doc.text('SJ LACE', pageWidth / 2, 10, { align: 'center' })

            return 30 // Return starting Y position for table on subsequent pages
        }

        // Add first page header
        let startY = addFirstPageHeader()

        // Configure the table
        const tableColumns = [
            { header: 'Sr.', dataKey: 'serialNumber' },
            { header: 'Name', dataKey: 'name' },
            { header: 'Phone Number', dataKey: 'phoneNumber' },
            { header: 'Balance', dataKey: 'balance' },
            { header: 'Status', dataKey: 'status' }
        ]

        const tableRows = parties.map((party, index) => ({
            serialNumber: index + 1,
            name: party.name,
            phoneNumber: party.phoneNumber || '-',
            balance: formatCurrency(party.balance),
            status: party.balance > 0
                ? 'Payable'
                : party.balance < 0
                    ? 'Receivable'
                    : 'Settled'
        }))

        // @ts-ignore (jspdf-autotable types are not properly recognized)
        doc.autoTable({
            columns: tableColumns,
            body: tableRows,
            startY,
            margin: { left: margin, right: margin },
            theme: 'grid',
            styles: {
                fontSize: 8,
                cellPadding: 1,
                lineColor: [0, 0, 0],
                lineWidth: 0.1,
                fontStyle: 'bold'
            },
            headStyles: {
                fillColor: [0, 0, 0],
                textColor: [255, 255, 255],
                fontStyle: 'bold',
                lineColor: [0, 0, 0],
                lineWidth: 0.1
            },
            columnStyles: {
                serialNumber: { cellWidth: 15, halign: 'center', textColor: [0, 0, 0] },
                name: { cellWidth: 70, textColor: [0, 0, 0] },
                phoneNumber: { cellWidth: 30, textColor: [0, 0, 0] },
                balance: { cellWidth: 45, halign: 'right', textColor: [0, 0, 0] },
                status: { cellWidth: 30, halign: 'center', textColor: [0, 0, 0] }
            },
            didDrawPage: (data: any) => {
                // Add simplified header to subsequent pages
                if (data.pageCount > 1) {
                    addSubsequentPageHeader()
                }

                // Add footer
                const str = `Page ${data.pageCount}`
                doc.setFontSize(8)
                doc.text(str, data.settings.margin.left, pageHeight - 10)

                // Add computer-generated notice
                doc.setFont('helvetica', 'bold')
                doc.setFontSize(8)
                doc.text(
                    'This is a computer-generated document. No signature is required.',
                    pageWidth / 2,
                    pageHeight - 10,
                    { align: 'center' }
                )
            }
        })

        if (shouldSave) {
            // Save the PDF with a more readable filename format
            const date = dayjs().format('YYYYMMDD')
            const time = dayjs().format('HHmmss')
            doc.save(`${getTitle().replace(' ', '_')}_Date_${date}_Time_${time}.pdf`)
            return
        }

        return doc
    } catch (error) {
        console.error('Error generating party list PDF:', error)
        throw error
    }
}