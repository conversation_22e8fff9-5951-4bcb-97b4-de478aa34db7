import { createContext, useContext, useState, useEffect } from 'react'
import { partyApi } from '../services'
import { useApi } from '../hooks'

export interface Party {
  value: string
  label: string
}

interface PartyContextType {
  vendors: Party[]
  customers: Party[]
  creditors: Party[]
  vendorsLoading: boolean
  customersLoading: boolean
  creditorsLoading: boolean
  vendorsError: any
  customersError: any
  creditorsError: any
  vendorsErrorMessage: string | null
  customersErrorMessage: string | null
  creditorsErrorMessage: string | null
  refreshVendors: () => Promise<void>
  refreshCustomers: () => Promise<void>
  refreshCreditors: () => Promise<void>
  refreshAllParties: () => Promise<void>
}

const PartyContext = createContext<PartyContextType>({
  vendors: [],
  customers: [],
  creditors: [],
  vendorsLoading: false,
  customersLoading: false,
  creditorsLoading: false,
  vendorsError: null,
  customersError: null,
  creditorsError: null,
  vendorsErrorMessage: null,
  customersErrorMessage: null,
  creditorsErrorMessage: null,
  refreshVendors: async () => {},
  refreshCustomers: async () => {},
  refreshCreditors: async () => {},
  refreshAllParties: async () => {}
})

export const usePartyContext = () => useContext(PartyContext)

export const PartyProvider = ({ children }: { children: React.ReactNode }) => {
  const [vendors, setVendors] = useState<Party[]>([])
  const [customers, setCustomers] = useState<Party[]>([])
  const [creditors, setCreditors] = useState<Party[]>([])

  const {
    data: vendorsData,
    isLoading: vendorsLoading,
    request: getVendors,
    error: vendorsError,
    errorMessage: vendorsErrorMessage
  } = useApi<Party[], []>(partyApi.getVendorsForSelect)

  const {
    data: customersData,
    isLoading: customersLoading,
    request: getCustomers,
    error: customersError,
    errorMessage: customersErrorMessage
  } = useApi<Party[], []>(partyApi.getCustomersForSelect)

  const {
    data: creditorsData,
    isLoading: creditorsLoading,
    request: getCreditors,
    error: creditorsError,
    errorMessage: creditorsErrorMessage
  } = useApi<Party[], []>(partyApi.getCreditorsForSelect)

  const fetchVendors = async () => {
    if (!vendors.length) {
      await getVendors()
    }
  }

  const fetchCustomers = async () => {
    if (!customers.length) {
      await getCustomers()
    }
  }

  const fetchCreditors = async () => {
    if (!creditors.length) {
      await getCreditors()
    }
  }

  const refreshVendors = async () => {
    setVendors([])
    await getVendors()
  }

  const refreshCustomers = async () => {
    setCustomers([])
    await getCustomers()
  }

  const refreshCreditors = async () => {
    setCreditors([])
    await getCreditors()
  }

  const refreshAllParties = async () => {
    setVendors([])
    setCustomers([])
    setCreditors([])
    await Promise.all([getVendors(), getCustomers(), getCreditors()])
  }

  useEffect(() => {
    Promise.all([fetchVendors(), fetchCustomers(), fetchCreditors()])
  }, [])

  useEffect(() => {
    if (vendorsData) {
      setVendors(vendorsData)
      //   console.log('vendorsData', vendorsData)
    }
  }, [vendorsData])

  useEffect(() => {
    if (customersData) {
      setCustomers(customersData)
      //   console.log('customersData', customersData)
    }
  }, [customersData])

  useEffect(() => {
    if (creditorsData) {
      setCreditors(creditorsData)
      //   console.log('creditorsData', creditorsData)
    }
  }, [creditorsData])

  return (
    <PartyContext.Provider
      value={{
        vendors,
        customers,
        creditors,
        vendorsLoading,
        customersLoading,
        creditorsLoading,
        vendorsError,
        customersError,
        creditorsError,
        vendorsErrorMessage,
        customersErrorMessage,
        creditorsErrorMessage,
        refreshVendors,
        refreshCustomers,
        refreshCreditors,
        refreshAllParties
      }}
    >
      {children}
    </PartyContext.Provider>
  )
}
