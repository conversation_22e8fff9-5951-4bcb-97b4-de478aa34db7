import { useState } from 'react'
import { Space, Button, App } from 'antd'
import { useDispatch, useSelector } from 'react-redux'
import { SaveOutlined, FolderOpenOutlined } from '@ant-design/icons'
import { SaveDraftModal } from './SaveDraftModal'
import { DraftSelector } from './DraftSelector'
import { draftInvoiceActions } from '@/renderer/redux'
import type { SaleInvoiceFormData } from '@/common/types'
import type { IRootState } from '@/renderer/redux'

interface Props {
  type: 'WALK_IN' | 'REGISTERED'
  formData: SaleInvoiceFormData
  onRestore: (formData: SaleInvoiceFormData) => void
  disabled?: boolean
}

export const DraftActions = ({ type, formData, onRestore, disabled }: Props) => {
  const [saveModalOpen, setSaveModalOpen] = useState(false)
  const [loading, setLoading] = useState(false)
  const dispatch = useDispatch()
  const { message } = App.useApp()

  const drafts = useSelector((state: IRootState) =>
    type === 'WALK_IN' ? state.draftInvoice.walkInDrafts : state.draftInvoice.registeredDrafts
  )

  const handleSave = async (name: string) => {
    try {
      if (!formData.items || formData.items.length === 0) {
        throw new Error('Cannot save an empty draft. Please add at least one item.')
      }

      setLoading(true)
      if (type === 'WALK_IN') {
        dispatch(draftInvoiceActions.saveWalkInDraft({ name, formData }))
      } else {
        dispatch(draftInvoiceActions.saveRegisteredDraft({ name, formData }))
      }
      message.success('Draft saved successfully')
      setSaveModalOpen(false)

      // Reset form data after successful save
      onRestore({
        date: new Date(),
        items: [],
        discountAmount: 0,
        paidAmount: 0,
        invoiceNumber: formData.invoiceNumber, // Keep the invoice number
        createdById: formData.createdById, // Keep the creator ID
        paymentMethod: 'CASH',
        bankId: '',
        source: 'SMALL_COUNTER'
      })
    } catch (error: any) {
      message.error(error.message || 'Failed to save draft')
    } finally {
      setLoading(false)
    }
  }

  const handleRestore = (draftName: string) => {
    const draft = drafts.find((d) => d.name === draftName)
    if (draft) {
      onRestore(draft.formData)
      if (type === 'WALK_IN') {
        dispatch(draftInvoiceActions.removeWalkInDraft(draftName))
      } else {
        dispatch(draftInvoiceActions.removeRegisteredDraft(draftName))
      }
      message.success('Draft restored successfully')
    }
  }

  return (
    <>
      <Space>
        <Button
          className="transition-[color] duration-75"
          icon={<SaveOutlined className="transition-[color] duration-75" />}
          onClick={() => setSaveModalOpen(true)}
          disabled={disabled}
        >
          Save as Draft
        </Button>
        <DraftSelector type={type} onSelect={handleRestore} disabled={disabled} />
      </Space>

      <SaveDraftModal
        open={saveModalOpen}
        onSave={handleSave}
        onCancel={() => setSaveModalOpen(false)}
        loading={loading}
      />
    </>
  )
}
