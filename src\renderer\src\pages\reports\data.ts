import { Report, ReportCategory } from './types'
import { Channels } from '@/common/constants'
import { ReportFormat } from '@/common/types'

export const reportCategories: ReportCategory[] = [
    {
        id: 'financial',
        name: 'Financial Reports',
        description: 'Overview of financial position and cash flow',
        icon: 'BarChartOutlined',
        accentColor: 'blue'
    },
    {
        id: 'inventory',
        name: 'Inventory Reports',
        description: 'Stock levels and inventory valuation',
        icon: 'InboxOutlined',
        accentColor: 'green'
    },
    {
        id: 'sales',
        name: 'Sales Reports',
        description: 'Sales performance and customer analytics',
        icon: 'ShoppingOutlined',
        accentColor: 'purple'
    },
    {
        id: 'operations',
        name: 'Operations Reports',
        description: 'Daily operations and ageing analysis',
        icon: 'ClockCircleOutlined',
        accentColor: 'orange'
    },
    {
        id: 'compliance',
        name: 'Compliance Reports',
        description: 'Audit trails and system logs',
        icon: 'SafetyOutlined',
        accentColor: 'red'
    }
]

export const reportsList: Report[] = [
    {
        id: 'financial-overview',
        title: 'Financial Overview',
        description: 'Comprehensive view of your financial position including cash, receivables, and payables',
        icon: 'BarChartOutlined',
        category: 'Financial Reports',
        channel: Channels.GENERATE_FINANCIAL_OVERVIEW,
        defaultFormat: ReportFormat.SCREEN
    },
    {
        id: 'inventory-valuation',
        title: 'Inventory Valuation',
        description: 'Current stock levels, value, and inventory ageing analysis',
        icon: 'InboxOutlined',
        category: 'Inventory Reports',
        channel: Channels.GENERATE_INVENTORY_VALUATION,
        defaultFormat: ReportFormat.SCREEN
    },
    {
        id: 'cash-flow',
        title: 'Cash Flow',
        description: 'Detailed analysis of cash inflows and outflows',
        icon: 'FundOutlined',
        category: 'Financial Reports',
        channel: Channels.GENERATE_CASH_FLOW,
        defaultFormat: ReportFormat.SCREEN
    },
    {
        id: 'sales-performance',
        title: 'Sales Performance',
        description: 'Sales trends, top products, and payment method analysis',
        icon: 'RiseOutlined',
        category: 'Sales Reports',
        channel: Channels.GENERATE_SALES_PERFORMANCE,
        defaultFormat: ReportFormat.SCREEN
    },
    {
        id: 'customer-analytics',
        title: 'Customer Analytics',
        description: 'Customer segmentation and purchase behavior analysis',
        icon: 'UserOutlined',
        category: 'Sales Reports',
        channel: Channels.GENERATE_CUSTOMER_ANALYTICS,
        defaultFormat: ReportFormat.SCREEN
    },
    {
        id: 'ageing-report',
        title: 'Ageing Report',
        description: 'Analysis of receivables and payables by age',
        icon: 'ClockCircleOutlined',
        category: 'Operations Reports',
        channel: Channels.GENERATE_AGEING_REPORT,
        defaultFormat: ReportFormat.SCREEN
    },
    {
        id: 'daily-operations',
        title: 'Daily Operations',
        description: 'Summary of daily transactions and cash movements',
        icon: 'CalendarOutlined',
        category: 'Operations Reports',
        channel: Channels.GENERATE_DAILY_OPERATIONS,
        defaultFormat: ReportFormat.SCREEN
    },
    {
        id: 'audit-report',
        title: 'Audit Report',
        description: 'Comprehensive audit trail of system activities',
        icon: 'SafetyOutlined',
        category: 'Compliance Reports',
        channel: Channels.GENERATE_AUDIT_REPORT,
        defaultFormat: ReportFormat.SCREEN
    }
] 