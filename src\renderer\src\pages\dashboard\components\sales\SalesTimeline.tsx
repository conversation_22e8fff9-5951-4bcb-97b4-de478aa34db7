import { useEffect, useState } from 'react'
import { Card, Skeleton, Typography, Space, Button, Alert, Radio } from 'antd'
import { useApi } from '@/renderer/hooks'
import { dashboardApi } from '@/renderer/services'
import { TimelineData, TimeRange } from '@/common/types/dashBoard'
import { MdTimeline, MdRefresh } from 'react-icons/md'
import { formatCurrency } from '@/renderer/utils'
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Legend
} from 'recharts'
import { useTheme } from '@/renderer/contexts'

const { Title } = Typography

const SalesTimeline = () => {
  const [timeRange, setTimeRange] = useState<TimeRange['days']>(7)
  const { isDarkMode } = useTheme()

  const {
    data,
    isLoading,
    error,
    request: fetchSalesTimeline
  } = useApi<TimelineData[], [TimeRange]>(dashboardApi.getSalesTimeline)

  useEffect(() => {
    fetchSalesTimeline({ days: timeRange })
  }, [])

  const handleTimeRangeChange = (days: TimeRange['days']) => {
    setTimeRange(days)
    fetchSalesTimeline({ days })
  }

  if (isLoading) {
    return (
      <div className="flex flex-col gap-4">
        <Space className="flex justify-between">
          <Space>
            <MdTimeline className="text-2xl text-indigo-600" />
            <Title level={5} className="!mb-0">
              Sales Timeline
            </Title>
          </Space>
          <Button icon={<MdRefresh />} loading />
        </Space>
        <Skeleton active paragraph={{ rows: 4 }} />
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex flex-col gap-4">
        <Space className="flex justify-between">
          <Space>
            <MdTimeline className="text-2xl text-indigo-600" />
            <Title level={5} className="!mb-0">
              Sales Timeline
            </Title>
          </Space>
          <Button icon={<MdRefresh />} onClick={() => fetchSalesTimeline({ days: timeRange })} />
        </Space>
        <Alert
          message="Error"
          description="Failed to load sales timeline data"
          type="error"
          showIcon
        />
      </div>
    )
  }

  if (!data) return null

  return (
    <div className="flex flex-col gap-4">
      <Space className="flex justify-between">
        <Space>
          <MdTimeline className="text-2xl text-indigo-600" />
          <Title level={5} className="!mb-0">
            Sales Timeline
          </Title>
        </Space>
        <Space>
          <Radio.Group
            value={timeRange}
            onChange={(e) => handleTimeRangeChange(e.target.value)}
            optionType="button"
            buttonStyle="solid"
            size="small"
          >
            <Radio.Button value={1}>1d</Radio.Button>
            <Radio.Button value={7}>7d</Radio.Button>
            <Radio.Button value={14}>14d</Radio.Button>
            <Radio.Button value={30}>30d</Radio.Button>
          </Radio.Group>
          <Button
            icon={<MdRefresh />}
            onClick={() => fetchSalesTimeline({ days: timeRange })}
            size="small"
          />
        </Space>
      </Space>

      <div className="h-[300px] w-full">
        <ResponsiveContainer width="100%" height="100%">
          <LineChart
            data={data}
            margin={{
              top: 5,
              right: 30,
              left: 20,
              bottom: 5
            }}
          >
            <CartesianGrid strokeDasharray="3 3" stroke={isDarkMode ? '#374151' : '#E5E7EB'} />
            <XAxis
              dataKey="date"
              stroke={isDarkMode ? '#9CA3AF' : '#4B5563'}
              tick={{ fontSize: 12 }}
            />
            <YAxis
              stroke={isDarkMode ? '#9CA3AF' : '#4B5563'}
              tick={{ fontSize: 12 }}
              tickFormatter={(value) => formatCurrency(value)}
            />
            <Tooltip
              contentStyle={{
                backgroundColor: isDarkMode ? '#1F2937' : '#FFFFFF',
                border: `1px solid ${isDarkMode ? '#374151' : '#E5E7EB'}`,
                borderRadius: '6px'
              }}
              labelStyle={{ color: isDarkMode ? '#9CA3AF' : '#4B5563' }}
              formatter={(value: number) => [formatCurrency(value), 'Amount']}
            />
            <Legend />
            <Line
              type="monotone"
              dataKey="totalAmount"
              name="Sales"
              stroke="#4F46E5"
              activeDot={{ r: 8 }}
              strokeWidth={2}
            />
          </LineChart>
        </ResponsiveContainer>
      </div>
    </div>
  )
}

export default SalesTimeline
