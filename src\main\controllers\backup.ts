import { IRequest } from "@/common/types";
import { backupService } from "../services";
import { dialog } from "electron";

class BackupController {
    createBackup = async (_event: Electron.IpcMainInvokeEvent, req: IRequest) => {
        const result = await dialog.showOpenDialog({
            properties: ['openDirectory'],
            title: 'Select Backup Location',
            buttonLabel: 'Select Folder'
        });

        if (result.canceled || !result.filePaths[0]) {
            throw new Error('No directory selected');
        }

        const backupPath = result.filePaths[0];
        return await backupService.createBackup(backupPath);
    }

    restoreBackup = async (_event: Electron.IpcMainInvokeEvent, req: IRequest) => {
        const { backupPath } = req.body ?? {};
        if (!backupPath) {
            throw new Error('Backup path is required');
        }
        return await backupService.restoreBackup(backupPath);
    }
}

export const backupController = new BackupController(); 