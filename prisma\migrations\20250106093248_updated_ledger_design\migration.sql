/*
  Warnings:

  - A unique constraint covering the columns `[expenseRef]` on the table `Ledger` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[bankLedgerRef]` on the table `Ledger` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[cashVaultLedgerRef]` on the table `Ledger` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `sourceLocation` to the `Payments` table without a default value. This is not possible if the table is not empty.

*/
-- CreateEnum
CREATE TYPE "CashLocation" AS ENUM ('BANK', 'CASH_VAULT', 'CASH_COUNTER');

-- AlterEnum
-- This migration adds more than one value to an enum.
-- With PostgreSQL versions 11 and earlier, this is not possible
-- in a single migration. This can be worked around by creating
-- multiple migrations, each migration adding only one value to
-- the enum.


ALTER TYPE "LedgerType" ADD VALUE 'Expense';
ALTER TYPE "LedgerType" ADD VALUE 'BankTransfer';
ALTER TYPE "LedgerType" ADD VALUE 'CashTransfer';

-- AlterTable
ALTER TABLE "Ledger" ADD COLUMN     "bankId" TEXT,
ADD COLUMN     "bankLedgerRef" TEXT,
ADD COLUMN     "cashAccountId" TEXT,
ADD COLUMN     "cashDestination" "CashLocation",
ADD COLUMN     "cashSource" "CashLocation",
ADD COLUMN     "cashVaultId" TEXT,
ADD COLUMN     "cashVaultLedgerRef" TEXT,
ADD COLUMN     "expenseRef" TEXT;

-- AlterTable
ALTER TABLE "Payments" ADD COLUMN     "destinationLocation" "CashLocation",
ADD COLUMN     "sourceLocation" "CashLocation" NOT NULL;

-- AlterTable
ALTER TABLE "Product" ADD COLUMN     "barcode" TEXT,
ADD COLUMN     "imagePath" TEXT;

-- CreateIndex
CREATE UNIQUE INDEX "Ledger_expenseRef_key" ON "Ledger"("expenseRef");

-- CreateIndex
CREATE UNIQUE INDEX "Ledger_bankLedgerRef_key" ON "Ledger"("bankLedgerRef");

-- CreateIndex
CREATE UNIQUE INDEX "Ledger_cashVaultLedgerRef_key" ON "Ledger"("cashVaultLedgerRef");

-- CreateIndex
CREATE INDEX "Ledger_cashSource_idx" ON "Ledger"("cashSource");

-- CreateIndex
CREATE INDEX "Ledger_cashDestination_idx" ON "Ledger"("cashDestination");

-- CreateIndex
CREATE INDEX "Ledger_bankId_idx" ON "Ledger"("bankId");

-- CreateIndex
CREATE INDEX "Ledger_cashVaultId_idx" ON "Ledger"("cashVaultId");

-- CreateIndex
CREATE INDEX "Ledger_cashAccountId_idx" ON "Ledger"("cashAccountId");

-- CreateIndex
CREATE INDEX "Ledger_expenseRef_idx" ON "Ledger"("expenseRef");

-- CreateIndex
CREATE INDEX "Ledger_bankLedgerRef_idx" ON "Ledger"("bankLedgerRef");

-- CreateIndex
CREATE INDEX "Ledger_cashVaultLedgerRef_idx" ON "Ledger"("cashVaultLedgerRef");

-- AddForeignKey
ALTER TABLE "Ledger" ADD CONSTRAINT "Ledger_expenseRef_fkey" FOREIGN KEY ("expenseRef") REFERENCES "Expense"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Ledger" ADD CONSTRAINT "Ledger_bankLedgerRef_fkey" FOREIGN KEY ("bankLedgerRef") REFERENCES "BankLedger"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Ledger" ADD CONSTRAINT "Ledger_cashVaultLedgerRef_fkey" FOREIGN KEY ("cashVaultLedgerRef") REFERENCES "CashVaultLedger"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Ledger" ADD CONSTRAINT "Ledger_bankId_fkey" FOREIGN KEY ("bankId") REFERENCES "Banks"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Ledger" ADD CONSTRAINT "Ledger_cashVaultId_fkey" FOREIGN KEY ("cashVaultId") REFERENCES "CashVault"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Ledger" ADD CONSTRAINT "Ledger_cashAccountId_fkey" FOREIGN KEY ("cashAccountId") REFERENCES "CashAccount"("id") ON DELETE SET NULL ON UPDATE CASCADE;
