import { prisma } from '../db';
import { <PERSON>risma, Status } from '@prisma/client';
import { CreateExpenseData, GetExpenseParams } from '../../common/types/expense';
import { processDateRange } from '../utils/helperFunctions';

// make sure the ui matches this new service and type change
class ExpenseService {
    async createExpense(data: CreateExpenseData) {
        // Validate amount is positive
        if (data.amount <= 0) {
            throw new Error('Expense amount must be positive');
        }

        return await prisma.$transaction(async (tx) => {
            // Validate source balance
            switch (data.paymentSource) {
                case 'SMALL_COUNTER':
                    const cashBalance = await tx.smallCounter.findFirst();
                    if (!cashBalance || cashBalance.cashInShop < data.amount) {
                        throw new Error('Insufficient cash in counter');
                    }
                    await tx.smallCounter.updateMany({
                        data: { cashInShop: { decrement: data.amount } }
                    });
                    break;

                case 'CASH_VAULT':
                    const vaultBalance = await tx.cashVault.findFirst();
                    if (!vaultBalance || vaultBalance.balance < data.amount) {
                        throw new Error('Insufficient funds in vault');
                    }
                    await tx.cashVault.updateMany({
                        data: { balance: { decrement: data.amount } }
                    });
                    break;

                case 'BANK':
                    if (!data.bankId) throw new Error('Bank ID is required for bank expenses');
                    const bankBalance = await tx.banks.findUnique({
                        where: { id: data.bankId }
                    });
                    if (!bankBalance || bankBalance.balance < data.amount) {
                        throw new Error('Insufficient funds in bank');
                    }
                    await tx.banks.update({
                        where: { id: data.bankId },
                        data: { balance: { decrement: data.amount } }
                    });
                    break;
            }

            // Create expense record
            const expense = await tx.expense.create({
                data: {
                    category: data.category,
                    amount: data.amount,
                    description: data.description,
                    date: data.date,
                    createdById: data.createdById,
                    status: 'ACTIVE'
                }
            });

            // Create main ledger entry
            await tx.ledger.create({
                data: {
                    amount: data.amount,
                    creditOrDebit: 'DEBIT',
                    description: `Expense: ${data.category}${data.description ? ` - ${data.description}` : ''}`,
                    referenceType: 'Expense',
                    expenseRef: expense.id,
                    date: data.date,
                    status: 'ACTIVE',
                    bankId: data.paymentSource === 'BANK' ? data.bankId : undefined,
                    cashSource: data.paymentSource,
                    createdById: data.createdById
                }
            });

            return expense;
        });
    }

    async voidExpense(id: string, voidedById: string, reason: string) {
        return await prisma.$transaction(async (tx) => {
            const expense = await tx.expense.findUnique({
                where: { id },
                include: {
                    Ledger: true
                }
            });

            if (!expense) throw new Error('Expense not found');
            if (!expense.Ledger) throw new Error('Ledger entry not found');
            if (expense.status === 'VOID') throw new Error('Expense is already voided');

            // Restore balance based on cash source
            switch (expense.Ledger.cashSource) {
                case 'SMALL_COUNTER':
                    await tx.smallCounter.updateMany({
                        data: { cashInShop: { increment: expense.amount } }
                    });
                    break;

                case 'CASH_VAULT':
                    await tx.cashVault.updateMany({
                        data: { balance: { increment: expense.amount } }
                    });
                    break;

                case 'BANK':
                    if (!expense.Ledger.bankId) throw new Error('Bank ID not found');
                    await tx.banks.update({
                        where: { id: expense.Ledger.bankId },
                        data: { balance: { increment: expense.amount } }
                    });
                    break;
            }

            // Update expense status
            await tx.expense.update({
                where: { id },
                data: {
                    status: 'VOID',
                    voidedById,
                    voidedAt: new Date(),
                    voidingReason: reason
                }
            });

            // Update ledger entry
            await tx.ledger.update({
                where: { id: expense.Ledger.id },
                data: {
                    status: 'VOID',
                    voidedById
                }
            });

            return expense;
        });
    }

    async getExpenseById(id: string) {
        return await prisma.expense.findUnique({
            where: { id },
            include: {
                createdBy: {
                    select: { name: true }
                },
                voidedBy: {
                    select: { name: true }
                },
                Ledger: true
            }
        });
    }

    async getExpenses({
        page = 1,
        limit = 10,
        search,
        startDate,
        endDate,
        status,
        category
    }: GetExpenseParams) {
        const finalWhere: Prisma.ExpenseWhereInput = {
            ...((status && status !== 'ALL') && { status: status as Status }),
            ...(category && { category }),
            ...(search && {
                OR: [
                    { category: { contains: search, mode: 'insensitive' } },
                    { description: { contains: search, mode: 'insensitive' } }
                ]
            })
        };

        if (startDate || endDate) {
            // Date range filter
            const dateRange = processDateRange(startDate, endDate);
            if (dateRange) {
                finalWhere.date = dateRange;
            }
        }

        const [expenses, total] = await Promise.all([
            prisma.expense.findMany({
                where: finalWhere,
                include: {
                    createdBy: { select: { name: true } },
                    voidedBy: { select: { name: true } },
                    Ledger: {
                        select: {
                            cashSource: true,
                            bankId: true,
                            bank: {
                                select: {
                                    name: true
                                }
                            }
                        }
                    }
                },
                skip: (page - 1) * limit,
                take: limit,
                orderBy: { date: 'desc' }
            }),
            prisma.expense.count({ where: finalWhere })
        ]);

        // Calculate total expense amount for current page
        const currentPageTotal = expenses.reduce((sum, expense) => {
            // Only include active expenses in the total
            return expense.status === 'ACTIVE' ? sum + expense.amount : sum;
        }, 0);

        // Calculate total expense amount for date range if date filter is applied
        let dateRangeTotal: number | null = null;
        if (startDate || endDate) {
            // Use aggregation for efficient calculation
            const aggregateResult = await prisma.expense.aggregate({
                where: {
                    ...finalWhere,
                    status: 'ACTIVE' // Only include active expenses in the total
                },
                _sum: {
                    amount: true
                }
            });
            dateRangeTotal = aggregateResult._sum.amount || 0;
        }

        return {
            expenses,
            total,
            page,
            totalPages: Math.ceil(total / limit),
            currentPageTotal,
            dateRangeTotal
        };
    }
}

export const expenseService = new ExpenseService();