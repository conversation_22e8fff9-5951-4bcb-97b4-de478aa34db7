import { Ty<PERSON><PERSON>, Space, Button, Alert, Skeleton, Row, Col, Statistic } from 'antd'
import { useApi } from '@/renderer/hooks'
import { dashboardApi } from '@/renderer/services'
import { PartyOverview as PartyOverviewType } from '@/common/types/dashBoard'
import { MdPeople, MdRefresh } from 'react-icons/md'
import { FaStore, FaMoneyBillTransfer } from 'react-icons/fa6'
import { useEffect } from 'react'
import { useTheme } from '@/renderer/contexts'

const { Title } = Typography

const PartyOverview = () => {
  const { isDarkMode } = useTheme()

  const {
    data,
    isLoading,
    error,
    request: fetchPartyOverview
  } = useApi<PartyOverviewType, []>(dashboardApi.getPartyOverview)

  useEffect(() => {
    fetchPartyOverview()
  }, [])

  if (isLoading) {
    return (
      <div className="flex flex-col gap-4">
        <Space className="flex justify-between">
          <Space>
            <MdPeople className="text-2xl text-indigo-600" />
            <Title level={5} className="!mb-0">
              Party Overview
            </Title>
          </Space>
          <Button icon={<MdRefresh />} loading />
        </Space>
        <Skeleton active paragraph={{ rows: 4 }} />
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex flex-col gap-4">
        <Space className="flex justify-between">
          <Space>
            <MdPeople className="text-2xl text-indigo-600" />
            <Title level={5} className="!mb-0">
              Party Overview
            </Title>
          </Space>
          <Button icon={<MdRefresh />} onClick={() => fetchPartyOverview()} />
        </Space>
        <Alert
          message="Error"
          description="Failed to load party overview data"
          type="error"
          showIcon
        />
      </div>
    )
  }

  if (!data) return null

  return (
    <div className="flex flex-col gap-4">
      <Space className="flex justify-between">
        <Space>
          <MdPeople className="text-2xl text-indigo-600" />
          <Title level={5} className="!mb-0">
            Party Overview
          </Title>
        </Space>
        <Button icon={<MdRefresh />} onClick={() => fetchPartyOverview()} size="small" />
      </Space>

      <Row gutter={[16, 16]}>
        {/* Customers Section */}
        <Col span={24}>
          <div
            className={`rounded-lg border shadow-md ${isDarkMode ? 'border-gray-800 bg-gray-900' : 'border-gray-200 bg-slate-50'} p-4`}
          >
            <Space className="mb-2">
              <MdPeople className="text-xl text-blue-500" />
              <span className="text-sm font-medium">Customers</span>
            </Space>
            <Row gutter={[16, 8]}>
              <Col span={8}>
                <Statistic
                  title="Total"
                  value={data.customers.total}
                  valueStyle={{ fontSize: '1.25rem' }}
                />
              </Col>
              <Col span={8}>
                <Statistic
                  title="Active"
                  value={data.customers.active}
                  valueStyle={{ fontSize: '1.25rem' }}
                />
              </Col>
              <Col span={8}>
                <Statistic
                  title="New"
                  value={data.customers.newThisMonth}
                  valueStyle={{ fontSize: '1.25rem' }}
                  suffix={<small className="text-gray-500">this month</small>}
                />
              </Col>
            </Row>
          </div>
        </Col>

        {/* Vendors Section */}
        <Col span={24}>
          <div
            className={`rounded-lg border shadow-md ${isDarkMode ? 'border-gray-800 bg-gray-900' : 'border-gray-200 bg-slate-50'} p-4`}
          >
            <Space className="mb-2">
              <FaStore className="text-xl text-green-500" />
              <span className="text-sm font-medium">Vendors</span>
            </Space>
            <Row gutter={[16, 8]}>
              <Col span={8}>
                <Statistic
                  title="Total"
                  value={data.vendors.total}
                  valueStyle={{ fontSize: '1.25rem' }}
                />
              </Col>
              <Col span={8}>
                <Statistic
                  title="Active"
                  value={data.vendors.active}
                  valueStyle={{ fontSize: '1.25rem' }}
                />
              </Col>
              <Col span={8}>
                <Statistic
                  title="New"
                  value={data.vendors.newThisMonth}
                  valueStyle={{ fontSize: '1.25rem' }}
                  suffix={<small className="text-gray-500">this month</small>}
                />
              </Col>
            </Row>
          </div>
        </Col>

        {/* Creditors Section */}
        <Col span={24}>
          <div
            className={`rounded-lg border shadow-md ${isDarkMode ? 'border-gray-800 bg-gray-900' : 'border-gray-200 bg-slate-50'} p-4`}
          >
            <Space className="mb-2">
              <FaMoneyBillTransfer className="text-xl text-orange-500" />
              <span className="text-sm font-medium">Creditors</span>
            </Space>
            <Row gutter={[16, 8]}>
              <Col span={8}>
                <Statistic
                  title="Total"
                  value={data.creditors.total}
                  valueStyle={{ fontSize: '1.25rem' }}
                />
              </Col>
              <Col span={8}>
                <Statistic
                  title="Active"
                  value={data.creditors.active}
                  valueStyle={{ fontSize: '1.25rem' }}
                />
              </Col>
              <Col span={8}>
                <Statistic
                  title="New"
                  value={data.creditors.newThisMonth}
                  valueStyle={{ fontSize: '1.25rem' }}
                  suffix={<small className="text-gray-500">this month</small>}
                />
              </Col>
            </Row>
          </div>
        </Col>
      </Row>
    </div>
  )
}

export default PartyOverview
