import { Form, Input, InputN<PERSON>ber, Drawer, Button, message, Space, Select } from 'antd'
import { useState, useEffect } from 'react'
import { productApi, categoryApi } from '@/renderer/services'
import { CreateCategoryDrawer } from './CreateCategoryDrawer'
import { PlusOutlined } from '@ant-design/icons'
import { useProductContext } from '@/renderer/contexts'
interface CreateProductDrawerProps {
  open: boolean
  onClose: () => void
  setRefreshTrigger: (trigger: any) => void
}

export const CreateProductDrawer = ({
  open,
  onClose,
  setRefreshTrigger
}: CreateProductDrawerProps) => {
  const [form] = Form.useForm()
  const [isCategoryDrawerOpen, setIsCategoryDrawerOpen] = useState(false)
  const [categories, setCategories] = useState([])
  const [loading, setLoading] = useState(false)

  const { refreshProducts } = useProductContext()

  const fetchCategories = async () => {
    const response = await categoryApi.getCategoriesForSelect()

    if (response.error?.error || response.data.error) {
      message.error(response.error?.message || response.data.error.message)
      return
    }
    setCategories(response.data.data)
  }

  // console.log('categories', categories)

  const handleCategoryChange = async (categoryId: string) => {
    const response = await categoryApi.generateProductId(categoryId)
    if (response.error?.error || response.data.error) {
      message.error(response.error?.message || response.data.error.message)
      return
    }
    form.setFieldValue('productId', response.data.data)
  }

  useEffect(() => {
    if (open) {
      fetchCategories()
    }
  }, [open])

  const handleSubmit = async (values: any) => {
    const productData = {
      ...values,
      categoryId: values.category // Rename category to categoryId to match schema
    }

    delete productData.category // Remove the old category field

    const response = await productApi.createProduct(productData)
    console.log('create product response', response)

    if (response.error.error || response.data.error) {
      message.error(response.error.message || response.data.error.message)
      return
    }
    message.success('Product created successfully')
    setRefreshTrigger((prev: number) => prev + 1)
    refreshProducts()
    form.resetFields()
    onClose()
  }

  return (
    <>
      <Drawer
        title="Create New Product"
        placement="right"
        onClose={onClose}
        open={open}
        width={500}
        extra={
          <Space>
            <Button icon={<PlusOutlined />} onClick={() => setIsCategoryDrawerOpen(true)}>
              Add Category
            </Button>
            <Button
              className="bg-green-500 hover:!bg-green-600"
              type="primary"
              onClick={() => form.submit()}
            >
              Create
            </Button>
          </Space>
        }
      >
        <Form form={form} layout="vertical" onFinish={handleSubmit}>
          <Form.Item
            name="name"
            label="Product Name"
            rules={[{ required: true, message: 'Please enter product name' }]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="category"
            label="Category"
            rules={[{ required: true, message: 'Please select category' }]}
          >
            <Select
              placeholder="Select category"
              allowClear
              showSearch
              options={categories.map((category: any) => ({
                label: category.label,
                value: category.value
              }))}
              filterOption={(input, option) =>
                (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
              }
              onChange={handleCategoryChange}
              loading={loading}
            />
          </Form.Item>
          <Form.Item
            name="productId"
            label="Product ID"
            rules={[{ required: true, message: 'Please enter product ID' }]}
          >
            <Input />
          </Form.Item>

          <Form.Item name="nature" label="Nature">
            <Input />
          </Form.Item>

          <Form.Item name="tag" label="Tag">
            <Input />
          </Form.Item>

          <Form.Item name="minStockLevel" label="Minimum Stock Level">
            <InputNumber style={{ width: '100%' }} />
          </Form.Item>

          <Form.Item
            name="salePrice"
            label="Sale Price"
            rules={[{ required: true, message: 'Please enter sale price' }]}
          >
            <InputNumber style={{ width: '100%' }} />
          </Form.Item>
        </Form>
      </Drawer>
      <CreateCategoryDrawer
        open={isCategoryDrawerOpen}
        onClose={() => setIsCategoryDrawerOpen(false)}
        onCategoryCreated={fetchCategories}
      />
    </>
  )
}
