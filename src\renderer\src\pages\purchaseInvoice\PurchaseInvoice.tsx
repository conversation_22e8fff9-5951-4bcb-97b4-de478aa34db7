import { useEffect, useState } from 'react'
import { PlusOutlined } from '@ant-design/icons'
import './PurchaseInvoice.scss'
import AddPurchaseInvoice from './components/AddPurchaseInvoice'
import { Space, message, Button, Card } from 'antd'
import { purchaseInvoiceApi } from '@/renderer/services'
import { useApi } from '@/renderer/hooks'
import dayjs from 'dayjs'
import { FilterSection } from './components/FilterSection'
import { PurchaseInvoiceList } from './components/PurchaseInvoiceList'
import { TransitionWrapper } from '@/renderer/components'
import { useTheme } from '@/renderer/contexts'
import { PurchaseInvoiceData, PurchaseInvoiceResponse } from '@/common/types/purchaseInvoice'

const PurchaseInvoice = () => {
  const [isCreateMode, setIsCreateMode] = useState(false)
  const [refreshTrigger, setRefreshTrigger] = useState(0)
  const [selectedVendor, setSelectedVendor] = useState<string | null>(null)
  const [selectedDate, setSelectedDate] = useState<dayjs.Dayjs | null>(null)
  const [dateRange, setDateRange] = useState<[dayjs.Dayjs | null, dayjs.Dayjs | null] | null>(null)
  const [selectedStatus, setSelectedStatus] = useState<'ALL' | 'ACTIVE' | 'VOID'>('ACTIVE')
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10
  })

  const { isDarkMode } = useTheme()

  const {
    data: purchaseData,
    isLoading: loading,
    error,
    errorMessage,
    request: fetchPurchaseInvoices
  } = useApi<PurchaseInvoiceResponse, [any]>(purchaseInvoiceApi.getAllPurchaseInvoices)

  useEffect(() => {
    const params = {
      page: pagination.current,
      limit: pagination.pageSize,
      vendorId: selectedVendor,
      status: selectedStatus === 'ALL' ? undefined : selectedStatus,
      startDate: selectedDate
        ? selectedDate.startOf('day').toDate()
        : dateRange && dateRange[0]
          ? dateRange[0].startOf('day').toDate()
          : undefined,
      endDate: selectedDate
        ? selectedDate.endOf('day').toDate()
        : dateRange && dateRange[1]
          ? dateRange[1].endOf('day').toDate()
          : undefined
    }

    fetchPurchaseInvoices(params)
  }, [
    pagination.current,
    pagination.pageSize,
    selectedVendor,
    selectedDate,
    dateRange,
    selectedStatus,
    refreshTrigger
  ])

  if (error) {
    message.error(errorMessage)
  }

  const handlePurchaseCreated = () => {
    setRefreshTrigger((prev) => prev + 1)
    setIsCreateMode(false)
  }

  const handleTableChange = (pagination: any) => {
    setPagination(pagination)
  }

  return (
    <div className="relative h-[calc(100vh-var(--header-height))] overflow-hidden">
      <TransitionWrapper isVisible={isCreateMode} direction="right">
        <AddPurchaseInvoice
          onBack={() => setIsCreateMode(false)}
          onPurchaseCreated={handlePurchaseCreated}
        />
      </TransitionWrapper>

      <TransitionWrapper isVisible={!isCreateMode} direction="left">
        <Card
          className={`purchase-invoice-container m-6 bg-[length:200%_200%] bg-[center] shadow-lg ${
            isDarkMode
              ? 'bg-gradient-to-bl from-indigo-950 via-black to-indigo-900 shadow-indigo-950'
              : 'bg-gradient-to-bl from-indigo-200 via-white to-indigo-100 shadow-indigo-200'
          }`}
        >
          <Space className="flex justify-between">
            <FilterSection
              selectedVendor={selectedVendor}
              setSelectedVendor={setSelectedVendor}
              selectedDate={selectedDate}
              setSelectedDate={setSelectedDate}
              dateRange={dateRange}
              setDateRange={setDateRange}
              selectedStatus={selectedStatus}
              setSelectedStatus={setSelectedStatus}
            />

            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => setIsCreateMode(true)}
              className="!border-green-500 !bg-green-500 hover:!border-green-600 hover:!bg-green-600"
            >
              Purchase Invoice
            </Button>
          </Space>
        </Card>

        <Card
          className={`purchase-invoice-container m-6 bg-[length:200%_200%] bg-[center] shadow-lg ${
            isDarkMode
              ? 'bg-gradient-to-bl from-indigo-950 via-black to-indigo-900 shadow-indigo-950'
              : 'bg-gradient-to-bl from-indigo-200 via-white to-indigo-100 shadow-indigo-200'
          }`}
        >
          <PurchaseInvoiceList
            data={purchaseData?.invoices}
            loading={loading}
            total={purchaseData?.total}
            pagination={pagination}
            onTableChange={handleTableChange}
            onRefresh={() => setRefreshTrigger((prev) => prev + 1)}
          />
        </Card>
      </TransitionWrapper>
    </div>
  )
}

export default PurchaseInvoice
