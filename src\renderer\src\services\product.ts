import { http } from "./http";
import { Channels } from "@/common/constants";
import { UpdateProductData } from "@/common/types";
import { ProductQuantityReportParams, ProductQuantityReportResponse } from '@/common/types/product'

interface CreateProductParams {
    name: string;
    categoryId: string;
    productId: string;
    nature?: string;
    tag?: string;
    minStockLevel?: number;
    salePrice: number;
}

interface GetProductsParams {
    page?: number;
    limit?: number;
    search?: string;
}

export const createProduct = async (params: CreateProductParams) => {
    return await http.post(Channels.CREATE_PRODUCT, { body: params });
}

export const updateProduct = async (id: string, data: UpdateProductData) => {
    return await http.put(Channels.UPDATE_PRODUCT, {
        params: { id },
        body: data
    });
}

export const getProduct = async (id: string) => {
    return await http.get(Channels.GET_PRODUCT, { params: { id } });
}

export const deleteProduct = async (id: string) => {
    return await http.delete(Channels.DELETE_PRODUCT, { params: { id } });
}

export const getProducts = async ({ page = 1, limit = 10, search }: GetProductsParams = {}) => {
    return await http.get(Channels.GET_PRODUCTS, {
        query: { page, limit, search }
    });
}

export const getProductsForSelect = async () => {
    return await http.get(Channels.GET_PRODUCTS_SELECT);
}

export const getProductQuantityReport = async (params: ProductQuantityReportParams = {}) => {
    return await http.get(Channels.GET_PRODUCT_QUANTITY_REPORT, { query: params })
}




