import { http } from './http';
import { Channels } from '@/common/constants';
import { VoidSaleData, GetSaleInvoicesParams, SaleInvoiceFormData, } from '@/common/types';

export const createWalkInSale = async (data: SaleInvoiceFormData) => {
    return await http.post(Channels.CREATE_WALK_IN_SALE, {
        body: data
    });
};

export const createRegisteredSale = async (data: SaleInvoiceFormData) => {
    return await http.post(Channels.CREATE_REGISTERED_SALE, {
        body: data
    });
};

export const voidWalkInSale = async (data: VoidSaleData) => {
    return await http.post(Channels.VOID_WALK_IN_SALE, {
        body: data
    });
};

export const voidRegisteredSale = async (data: VoidSaleData) => {
    return await http.post(Channels.VOID_REGISTERED_SALE, {
        body: data
    });
};

export const getSaleInvoiceById = async (id: string) => {
    return await http.get(Channels.GET_SALE_BY_ID, {
        params: { id }
    });
};

export const getSaleInvoices = async (data: GetSaleInvoicesParams) => {
    return await http.get(Channels.GET_ALL_SALES, {
        params: data
    });
};


