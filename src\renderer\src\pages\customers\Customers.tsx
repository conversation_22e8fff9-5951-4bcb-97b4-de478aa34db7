import { useState } from 'react'
import { Button, Input, Form, Card, Typography, Divider, Space } from 'antd'
import { CustomerList } from './components/CustomerList'
import { CreateCustomerDrawer } from './components/CreateCustomerDrawer'
import { CustomerDetailsModal } from './components/CustomerDetailsModal'
import { SearchOutlined, PlusOutlined, UserOutlined } from '@ant-design/icons'
import { PartyListPdfButton } from '@/renderer/components/partyListPdfButton'
import { PartyType } from '@/common/types/party'
import './Customers.scss'
import { usePartyContext, useTheme } from '@/renderer/contexts'

const { Title } = Typography

const Customers = () => {
  const [isCreateDrawerOpen, setIsCreateDrawerOpen] = useState(false)
  const [selectedCustomer, setSelectedCustomer] = useState<string | null>(null)
  const [searchQuery, setSearchQuery] = useState('')
  const [refreshTrigger, setRefreshTrigger] = useState(0)
  const { isDarkMode } = useTheme()

  const { refreshCustomers } = usePartyContext()

  const handleCreateCustomer = () => {
    setIsCreateDrawerOpen(true)
  }

  const handleCustomerClick = (customerId: string) => {
    setSelectedCustomer(customerId)
  }

  const handleCustomerCreated = () => {
    refreshCustomers()
    setRefreshTrigger((prev) => prev + 1)
    setIsCreateDrawerOpen(false)
  }

  return (
    <div className="customers-container">
      <Card
        className={`bg-[length:200%_200%] bg-[center] shadow-lg ${
          isDarkMode
            ? 'bg-gradient-to-bl from-indigo-950 via-black to-indigo-900 shadow-indigo-950'
            : 'bg-gradient-to-bl from-indigo-200 via-white to-indigo-100 shadow-indigo-200'
        }`}
      >
        <Space wrap className="flex justify-between gap-4">
          <Title level={4}>
            <UserOutlined /> Customers Management
          </Title>

          <Space wrap className="gap-4">
            <Form.Item className="search-input" style={{ marginBottom: 0 }}>
              <Input
                placeholder="Search customers..."
                prefix={<SearchOutlined />}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </Form.Item>

            <PartyListPdfButton partyType={PartyType.CUSTOMER} search={searchQuery} />

            <Button type="primary" icon={<PlusOutlined />} onClick={handleCreateCustomer}>
              Add Customer
            </Button>
          </Space>
        </Space>

        <Divider />

        <CustomerList
          onCustomerClick={handleCustomerClick}
          searchQuery={searchQuery}
          refreshTrigger={refreshTrigger}
        />
      </Card>

      <CreateCustomerDrawer
        open={isCreateDrawerOpen}
        onClose={() => setIsCreateDrawerOpen(false)}
        onCustomerCreated={handleCustomerCreated}
      />

      <CustomerDetailsModal
        customerId={selectedCustomer}
        open={!!selectedCustomer}
        onClose={() => setSelectedCustomer(null)}
      />
    </div>
  )
}

export default Customers
