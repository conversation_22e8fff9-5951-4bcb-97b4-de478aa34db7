import { useEffect, useState } from 'react'
import { Button, Select, Space, App, Modal } from 'antd'
import { FaFilePdf, FaDownload, FaPrint } from 'react-icons/fa'
import { productApi } from '@/renderer/services'
import { generateProductQuantityPDF } from '../utils/generateProductQuantityPDF'
import { ProductQuantityReportParams } from '@/common/types/product'
import { categoryApi } from '@/renderer/services'

interface ProductQuantityReportProps {
  onClose?: () => void
}

const ProductQuantityReport = ({ onClose }: ProductQuantityReportProps) => {
  const [loading, setLoading] = useState(false)
  const [printSaveModalVisible, setPrintSaveModalVisible] = useState(false)
  const [printSaveLoading, setPrintSaveLoading] = useState(false)
  const [reportParams, setReportParams] = useState<ProductQuantityReportParams>({
    sortBy: 'name',
    sortOrder: 'asc'
  })

  const [categories, setCategories] = useState([])
  const [categoriesLoading, setCategoriesLoading] = useState(false)

  const { message } = App.useApp()

  const fetchCategories = async () => {
    setCategoriesLoading(true)
    const response = await categoryApi.getCategoriesForSelect()
    setCategoriesLoading(false)

    if (response.error?.error || response.data.error) {
      message.error(response.error?.message || response.data.error.message)
      return
    }
    setCategories(response.data.data)
  }

  useEffect(() => {
    fetchCategories()
  }, [])

  const handleGenerateReport = async () => {
    try {
      setLoading(true)
      const response = await productApi.getProductQuantityReport(reportParams)
      setLoading(false)

      if (response.error.error || response.data.error) {
        message.error(response.error.message || response.data.error.message)
        return
      }

      setPrintSaveModalVisible(true)
    } catch (error: any) {
      setLoading(false)
      message.error(error.message || 'Failed to generate report')
    }
  }

  const handlePrint = async () => {
    try {
      setPrintSaveLoading(true)
      const response = await productApi.getProductQuantityReport(reportParams)

      if (response.error.error || response.data.error) {
        throw new Error(response.error.message || response.data.error.message)
      }

      const doc = await generateProductQuantityPDF(response.data.data)

      if (doc) {
        const dataUri = doc.output('datauristring')
        await window.electron.ipcRenderer.invoke('print-pdf', dataUri)
      }

      setPrintSaveModalVisible(false)
      setPrintSaveLoading(false)
    } catch (error: any) {
      console.error('Error generating PDF:', error)
      message.error('Failed to generate PDF')
      setPrintSaveLoading(false)
    }
  }

  const handleSave = async () => {
    try {
      setPrintSaveLoading(true)
      const response = await productApi.getProductQuantityReport(reportParams)

      if (response.error.error || response.data.error) {
        throw new Error(response.error.message || response.data.error.message)
      }

      const saved = await generateProductQuantityPDF(response.data.data, true)

      if (saved) {
        message.success('Product inventory PDF generated successfully')
        setPrintSaveModalVisible(false)
      }

      setPrintSaveLoading(false)
    } catch (error: any) {
      console.error('Error generating PDF:', error)
      message.error('Failed to generate PDF')
      setPrintSaveLoading(false)
    }
  }

  const handlePrintSaveCancel = () => {
    setPrintSaveModalVisible(false)
    setPrintSaveLoading(false)
  }

  return (
    <div>
      <Space direction="vertical" className="gap-4" style={{ width: '100%', marginBottom: '20px' }}>
        {/* <Space> */}
        <Select
          placeholder="Select Category"
          className="w-full"
          allowClear
          loading={categoriesLoading}
          onChange={(value) => setReportParams({ ...reportParams, categoryId: value })}
          options={categories}
        />
        <Select
          className="w-full"
          placeholder="Sort By"
          value={reportParams.sortBy}
          onChange={(value) => setReportParams({ ...reportParams, sortBy: value })}
          options={[
            { label: 'Name', value: 'name' },
            { label: 'Quantity', value: 'quantity' },
            { label: 'Value', value: 'value' }
          ]}
        />
        <Select
          placeholder="Sort Order"
          className="w-full"
          value={reportParams.sortOrder}
          onChange={(value) => setReportParams({ ...reportParams, sortOrder: value })}
          options={[
            { label: 'Ascending', value: 'asc' },
            { label: 'Descending', value: 'desc' }
          ]}
        />
        <Button
          type="primary"
          className="mt-6 w-full"
          icon={<FaFilePdf />}
          onClick={handleGenerateReport}
          loading={loading}
        >
          Generate Report
        </Button>
        {/* </Space> */}
      </Space>

      <Modal
        title="Product Inventory Report"
        open={printSaveModalVisible}
        onCancel={handlePrintSaveCancel}
        footer={null}
      >
        <p>What would you like to do with the report?</p>
        <Space className="mt-6">
          <Button
            type="primary"
            icon={<FaPrint />}
            onClick={handlePrint}
            loading={printSaveLoading}
          >
            Print
          </Button>
          <Button icon={<FaDownload />} onClick={handleSave} loading={printSaveLoading}>
            Save as PDF
          </Button>
          <Button onClick={handlePrintSaveCancel}>Cancel</Button>
        </Space>
      </Modal>
    </div>
  )
}

export default ProductQuantityReport
