import { Status, CreditDebit, PaymentMethod, CustomerType, LedgerType } from '@prisma/client';

export enum ReportFormat {
    SCREEN = 'SCREEN',
    PDF = 'PDF',
    EXCEL = 'EXCEL'
}

export enum TimePeriod {
    DAILY = 'DAILY',
    WEEKLY = 'WEEKLY',
    MONTHLY = 'MONTHLY',
    YEARLY = 'YEARLY'
}

// Base report params
interface BaseReportParams {
    format: ReportFormat;
    startDate?: Date;
    endDate?: Date;
    timePeriod?: TimePeriod;
}

// Financial Reports Types
export interface FinancialOverviewParams extends BaseReportParams { }

export interface FinancialOverviewReport {
    // UI: Cards for summary metrics
    summary: {
        totalCashInHand: number;
        totalBankBalance: number;
        totalReceivables: number;
        totalPayables: number;
        netPosition: number;
    };
    // UI: BarChart for bank balances
    bankBalances: {
        bankName: string;
        balance: number;
        transactions: number;
    }[];
    // UI: PieChart for cash distribution
    cashDistribution: {
        location: string;
        amount: number;
        percentage: number;
    }[];
    // UI: Table
    partyBalances: {
        partyName: string;
        type: string;
        balance: number;
        lastTransactionDate: Date;
    }[];
}

export interface CashFlowParams extends BaseReportParams {
    includeDetails?: boolean;
}

export interface CashFlowReport {
    // UI: LineChart
    timeline: {
        date: Date;
        inflow: number;
        outflow: number;
        netFlow: number;
    }[];
    // UI: PieChart
    inflowSources: {
        source: string;
        amount: number;
        percentage: number;
    }[];
    // UI: PieChart
    outflowCategories: {
        category: string;
        amount: number;
        percentage: number;
    }[];
    // UI: Table
    details?: {
        date: Date;
        description: string;
        type: string;
        inflow: number;
        outflow: number;
        balance: number;
    }[];
}

// Inventory Reports Types
export interface InventoryReportParams extends BaseReportParams {
    categoryId?: string;
    minStockLevel?: number;
}

export interface InventoryValuationReport {
    // UI: Cards
    summary: {
        totalProducts: number;
        totalValue: number;
        averageValue: number;
        lowStockItems: number;
    };
    // UI: PieChart
    categoryDistribution: {
        category: string;
        itemCount: number;
        totalValue: number;
        percentage: number;
    }[];
    // UI: Table
    stockDetails: {
        productId: string;
        name: string;
        category: string;
        quantity: number;
        value: number;
        lastPurchaseDate: Date;
        lastSaleDate: Date;
    }[];
    // UI: BarChart
    ageingAnalysis: {
        ageGroup: string;
        itemCount: number;
        value: number;
    }[];
}

// Sales Reports Types
export interface SalesReportParams extends BaseReportParams {
    customerType?: CustomerType;
    categoryId?: string;
    productId?: string;
    minAmount?: number;
}

export interface SalesPerformanceReport {
    // UI: Cards
    summary: {
        totalSales: number;
        averageOrderValue: number;
        totalOrders: number;
        returnRate: number;
    };
    // UI: LineChart
    salesTrend: {
        period: string;
        amount: number;
        orders: number;
    }[];
    // UI: PieChart
    salesByCustomerType: {
        type: CustomerType;
        amount: number;
        percentage: number;
    }[];
    // UI: Table
    topProducts: {
        productId: string;
        name: string;
        quantity: number;
        amount: number;
        profit: number;
    }[];
    // UI: PieChart
    paymentMethodDistribution: {
        method: PaymentMethod;
        amount: number;
        percentage: number;
    }[];
}

// Customer Reports Types
export interface CustomerAnalyticsParams extends BaseReportParams {
    minPurchases?: number;
    customerType?: CustomerType;
}

export interface CustomerAnalyticsReport {
    // UI: Cards
    summary: {
        totalCustomers: number;
        activeCustomers: number;
        averageCustomerValue: number;
    };
    // UI: Table
    topCustomers: {
        id: string;
        name: string;
        purchases: number;
        totalSpent: number;
        lastPurchase: Date;
        creditLimit?: number;
        currentBalance: number;
    }[];
    // UI: LineChart
    purchaseFrequency: {
        frequency: string;
        customerCount: number;
    }[];
    // UI: PieChart
    customerSegmentation: {
        segment: string;
        count: number;
        percentage: number;
    }[];
}

// Receivables & Payables Types
export interface AgeingReportParams extends BaseReportParams {
    partyType: 'CUSTOMER' | 'VENDOR';
    minAmount?: number;
}

export interface AgeingReport {
    // UI: Cards
    summary: {
        totalOutstanding: number;
        averageOverdueDays: number;
        totalParties: number;
    };
    // UI: Table
    details: {
        partyId: string;
        name: string;
        total: number;
        buckets: {
            current: number;
            '1-30': number;
            '31-60': number;
            '61-90': number;
            '>90': number;
        };
        lastPaymentDate: Date;
    }[];
    // UI: BarChart
    ageingBuckets: {
        bucket: string;
        amount: number;
        percentage: number;
    }[];
}

// Operations Reports Types
export interface DailyOperationsParams extends BaseReportParams { }

export interface DailyOperationsReport {
    date: Date;
    // UI: Cards
    summary: {
        totalSales: number;
        totalPurchases: number;
        totalExpenses: number;
        netCashFlow: number;
    };
    // UI: Table
    transactions: {
        time: Date;
        type: string;
        description: string;
        amount: number;
        status: Status;
    }[];
    // UI: PieChart
    cashMovements: {
        location: string;
        inflow: number;
        outflow: number;
        netFlow: number;
    }[];
}

// Audit Reports Types
export interface AuditReportParams extends BaseReportParams {
    userId?: string;
    transactionType?: LedgerType;
}

export interface AuditReport {
    // UI: Table with filters
    transactions: {
        date: Date;
        user: string;
        action: string;
        details: string;
        originalValue?: string;
        newValue?: string;
        status: Status;
    }[];
    // UI: PieChart
    actionDistribution: {
        action: string;
        count: number;
        percentage: number;
    }[];
    // UI: Cards
    summary: {
        totalTransactions: number;
        voidTransactions: number;
        modifications: number;
        unusualActivity: number;
    };
}
