import { prisma } from '../db';
import {
    CreateAccountTransferData,
    GetAccountTransfersParams,
    VoidAccountTransferParams,
    AccountTransfersResponse,
    CreateAccountTransferResult,
    VoidAccountTransferResult,
    AccountTransferItem,
    AccountTransferStatus,
    AccountTransferSortOrder
} from '@/common/types/accountTransfer';
import { CreditDebit, LedgerType, Status } from '@prisma/client';

class AccountTransferService {
    async createAccountTransfer(data: CreateAccountTransferData): Promise<CreateAccountTransferResult> {
        // Validate parties exist and are different
        if (data.fromPartyId === data.toPartyId) {
            throw new Error('Cannot transfer to the same party');
        }

        // Validate amount
        if (data.amount <= 0) {
            throw new Error('Transfer amount must be greater than zero');
        }

        return await prisma.$transaction(async (tx) => {
            // Validate parties exist
            const [fromParty, toParty] = await Promise.all([
                tx.party.findUnique({
                    where: { id: data.fromPartyId },
                    select: { id: true, name: true, type: true, currentBalance: true }
                }),
                tx.party.findUnique({
                    where: { id: data.toPartyId },
                    select: { id: true, name: true, type: true, currentBalance: true }
                })
            ]);

            if (!fromParty) {
                throw new Error('From party not found');
            }
            if (!toParty) {
                throw new Error('To party not found');
            }

            const description = data.description || `Transfer from ${fromParty.name} to ${toParty.name}`;

            // Create TWO ledger entries for proper double-entry bookkeeping

            // 1. FROM party ledger entry (CREDIT - money goes out, reduces their balance)
            const fromLedgerEntry = await tx.ledger.create({
                data: {
                    amount: data.amount,
                    creditOrDebit: CreditDebit.CREDIT, // FROM party is CREDITED (money goes out)
                    description: `${description} - Outgoing Transfer`,
                    date: data.transferDate,
                    referenceType: LedgerType.Account_Transfer,
                    partyId: data.fromPartyId,
                    createdById: data.createdById,
                    status: Status.ACTIVE
                }
            });

            // 2. TO party ledger entry (DEBIT - money comes in, increases their balance)
            const toLedgerEntry = await tx.ledger.create({
                data: {
                    amount: data.amount,
                    creditOrDebit: CreditDebit.DEBIT, // TO party is DEBITED (money comes in)
                    description: `${description} - Incoming Transfer`,
                    date: data.transferDate,
                    referenceType: LedgerType.Account_Transfer,
                    partyId: data.toPartyId,
                    createdById: data.createdById,
                    status: Status.ACTIVE
                }
            });

            // Create account transfer
            const accountTransfer = await tx.accountTransfer.create({
                data: {
                    description: data.description,
                    amount: data.amount,
                    transferDate: data.transferDate,
                    fromPartyId: data.fromPartyId,
                    toPartyId: data.toPartyId,
                    fromLedgerId: fromLedgerEntry.id,
                    toLedgerId: toLedgerEntry.id,
                    createdById: data.createdById
                },
                include: {
                    fromParty: {
                        select: { id: true, name: true, type: true }
                    },
                    toParty: {
                        select: { id: true, name: true, type: true }
                    },
                    createdBy: {
                        select: { id: true, name: true }
                    }
                }
            });

            // Update party balances
            await Promise.all([
                // Debit from party (decrease balance)
                tx.party.update({
                    where: { id: data.fromPartyId },
                    data: {
                        currentBalance: fromParty.currentBalance - data.amount
                    }
                }),
                // Credit to party (increase balance)
                tx.party.update({
                    where: { id: data.toPartyId },
                    data: {
                        currentBalance: toParty.currentBalance + data.amount
                    }
                })
            ]);

            return {
                accountTransfer: accountTransfer as AccountTransferItem,
                message: 'Account transfer created successfully'
            };
        });
    }

    /**
     * Void an account transfer (soft delete)
     * Voids the transfer, ledger entry, and reverts party balances
     */
    async voidAccountTransfer(params: VoidAccountTransferParams): Promise<VoidAccountTransferResult> {
        const { id, deletedById, deletionReason } = params;
        return await prisma.$transaction(async (tx) => {
            // Get the transfer
            const transfer = await tx.accountTransfer.findUnique({
                where: { id }
            })

            if (!transfer) {
                throw new Error('Account transfer not found')
            }
            if (transfer.isDeleted) {
                throw new Error('Account transfer is already voided')
            }

            // Get the parties to access their current balances
            const [fromParty, toParty] = await Promise.all([
                tx.party.findUnique({
                    where: { id: transfer.fromPartyId },
                    select: { currentBalance: true }
                }),
                tx.party.findUnique({
                    where: { id: transfer.toPartyId },
                    select: { currentBalance: true }
                })
            ])

            if (!fromParty || !toParty) {
                throw new Error('Parties not found')
            }

            if (!transfer) {
                throw new Error('Account transfer not found')
            }
            if (transfer.isDeleted) {
                throw new Error('Account transfer is already voided')
            }



            // Reverse the balance changes
            await Promise.all([
                // Reverse from party balance (add back the amount)
                tx.party.update({
                    where: { id: transfer.fromPartyId },
                    data: {
                        currentBalance: fromParty.currentBalance + transfer.amount
                    }
                }),
                // Reverse to party balance (subtract back the amount)
                tx.party.update({
                    where: { id: transfer.toPartyId },
                    data: {
                        currentBalance: toParty.currentBalance - transfer.amount
                    }
                })
            ]);

            // Mark BOTH ledger entries as voided
            await Promise.all([
                // Void the FROM party ledger entry
                tx.ledger.update({
                    where: { id: transfer.fromLedgerId },
                    data: {
                        status: Status.VOID,
                        voidedById: deletedById
                    }
                }),
                // Void the TO party ledger entry
                tx.ledger.update({
                    where: { id: transfer.toLedgerId },
                    data: {
                        status: Status.VOID,
                        voidedById: deletedById
                    }
                })
            ]);

            // Mark account transfer as deleted
            await tx.accountTransfer.update({
                where: { id },
                data: {
                    isDeleted: true,
                    deletedAt: new Date(),
                    deletedById,
                    deletionReason
                }
            });

            return {
                success: true,
                message: 'Account transfer voided successfully'
            };
        })
    }

    async getAccountTransfers(params: GetAccountTransfersParams): Promise<AccountTransfersResponse> {
        const {
            page = 1,
            pageSize = 20,
            startDate,
            endDate,
            fromPartyId,
            toPartyId,
            accountId,
            status = AccountTransferStatus.ACTIVE,
            sortOrder = AccountTransferSortOrder.OLDEST_FIRST,
            search,
            amountRange
        } = params;

        const skip = (page - 1) * pageSize;

        // Build where clause
        let whereClause: any = {};

        // Status filtering
        if (status === AccountTransferStatus.ACTIVE) {
            whereClause.isDeleted = false;
        } else if (status === AccountTransferStatus.VOIDED) {
            whereClause.isDeleted = true;
        }
        // For ALL status, don't add isDeleted filter

        // Date filtering
        if (startDate && endDate) {
            if (startDate.toDateString() === endDate.toDateString()) {
                // Same day - filter for entire day
                const start = new Date(startDate);
                start.setHours(0, 0, 0, 0);
                const end = new Date(startDate);
                end.setHours(23, 59, 59, 999);
                whereClause.transferDate = {
                    gte: start,
                    lte: end
                };
            } else {
                // Date range
                whereClause.transferDate = {
                    gte: startDate,
                    lte: endDate
                };
            }
        }

        // Account filter (either sender or receiver)
        if (accountId) {
            whereClause.OR = [
                { fromPartyId: accountId },
                { toPartyId: accountId }
            ];
        }

        // Specific filters
        if (fromPartyId) whereClause.fromPartyId = fromPartyId;
        if (toPartyId) whereClause.toPartyId = toPartyId;

        // Amount range filter
        if (amountRange) {
            whereClause.amount = {
                gte: amountRange.fromAmount,
                lte: amountRange.toAmount
            };
        }

        // Search functionality
        if (search) {
            whereClause.OR = [
                { description: { contains: search, mode: 'insensitive' } },
                { fromParty: { name: { contains: search, mode: 'insensitive' } } },
                { toParty: { name: { contains: search, mode: 'insensitive' } } }
            ];
        }

        // Determine sort order
        const orderBy = sortOrder === AccountTransferSortOrder.NEWEST_FIRST
            ? { transferDate: 'desc' as const }
            : sortOrder === AccountTransferSortOrder.AMOUNT_HIGH_TO_LOW
                ? { amount: 'desc' as const }
                : sortOrder === AccountTransferSortOrder.AMOUNT_LOW_TO_HIGH
                    ? { amount: 'asc' as const }
                    : { transferDate: 'asc' as const };

        const [transfers, total] = await Promise.all([
            prisma.accountTransfer.findMany({
                where: whereClause,
                include: {
                    fromParty: {
                        select: { id: true, name: true, type: true }
                    },
                    toParty: {
                        select: { id: true, name: true, type: true }
                    },
                    createdBy: {
                        select: { id: true, name: true }
                    },
                    deletedBy: {
                        select: { id: true, name: true }
                    }
                },
                orderBy,
                skip,
                take: pageSize
            }),
            prisma.accountTransfer.count({
                where: whereClause
            })
        ]);

        return {
            accountTransfers: transfers as AccountTransferItem[],
            total,
            page,
            limit: pageSize,
            totalPages: Math.ceil(total / pageSize)
        };
    }

    async getAccountTransferById(id: string): Promise<AccountTransferItem | null> {
        const transfer = await prisma.accountTransfer.findUnique({
            where: { id },
            include: {
                fromParty: {
                    select: { id: true, name: true, type: true }
                },
                toParty: {
                    select: { id: true, name: true, type: true }
                },
                createdBy: {
                    select: { id: true, name: true }
                },
                deletedBy: {
                    select: { id: true, name: true }
                }
            }
        });

        return transfer as AccountTransferItem | null;
    }
}

export const accountTransferService = new AccountTransferService();