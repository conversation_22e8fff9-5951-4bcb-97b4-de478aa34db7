import { Drawer, Form, DatePicker, Input, InputNumber, Space, Button, App, Typography } from 'antd'
import { useState } from 'react'
import { PaymentSourceSelect } from './PaymentSourceSelect'
import { PaymentDetailsCard } from './PaymentDetailCard'
import { paymentApi } from '@/renderer/services'
import { useSelector } from 'react-redux'
import { IRootState } from '@/renderer/redux'
import { handleDatePickerValue } from '@/renderer/utils'

const { Title } = Typography

interface AddTransferDrawerProps {
  open: boolean
  onClose: () => void
  setRefreshTrigger: (trigger: any) => void
}

export const AddTransferDrawer = ({ open, onClose, setRefreshTrigger }: AddTransferDrawerProps) => {
  const [form] = Form.useForm()
  const { message } = App.useApp()
  const user = useSelector((state: IRootState) => state.user.data)

  // Watch form values for dynamic updates
  const fromSource = Form.useWatch(['fromSource'], form)
  const toSource = Form.useWatch(['toSource'], form)
  const fromBank = Form.useWatch(['frombankId'], form)
  const toBank = Form.useWatch(['tobankId'], form)
  const amount = Form.useWatch(['amount'], form)

  const handleSubmit = async (values: any) => {
    try {
      const response = await paymentApi.transferBetweenLocations({
        amount: values.amount,
        date: handleDatePickerValue(values.date.toDate()),
        description: values.description,
        fromLocation: values.fromSource,
        toLocation: values.toSource,
        fromLocationId: values.fromSource === 'BANK' ? values.frombankId : undefined,
        toLocationId: values.toSource === 'BANK' ? values.tobankId : undefined,
        createdById: user?.id || ''
      })

      if (response.error.error || response.data.error) {
        throw new Error(response.error.message || response.data.error.message)
      }

      message.success('Transfer created successfully')
      form.resetFields()
      setRefreshTrigger((prev: number) => prev + 1)
      onClose()
    } catch (error: any) {
      message.error(error.message || 'Failed to create transfer')
    }
  }

  const handleClose = () => {
    form.resetFields()
    onClose()
  }

  return (
    <Drawer
      title="Add Location Transfer"
      placement="right"
      width={720}
      onClose={handleClose}
      open={open}
    >
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        initialValues={{
          date: null,
          amount: 0
        }}
      >
        <Form.Item
          name="date"
          label="Date"
          rules={[{ required: true, message: 'Please select date' }]}
        >
          <DatePicker className="w-full" />
        </Form.Item>

        <div className="space-y-6">
          <div>
            <Title level={5} className="mb-4">
              Transfer From
            </Title>
            <PaymentSourceSelect form={form} namePrefix="from" />
            {fromSource && (
              <PaymentDetailsCard
                source={fromSource}
                selectedBank={fromBank}
                paidAmount={amount || 0}
              />
            )}
          </div>

          <Form.Item
            name="amount"
            label="Transfer Amount"
            rules={[{ required: true, message: 'Please enter amount' }]}
          >
            <InputNumber
              className="w-full"
              formatter={(value) => `PKR ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
              parser={(value) => value!.replace(/PKR\s?|(,*)/g, '')}
            />
          </Form.Item>

          <div>
            <Title level={5} className="mb-4">
              Transfer To
            </Title>
            <PaymentSourceSelect form={form} namePrefix="to" />
            {toSource && (
              <PaymentDetailsCard source={toSource} selectedBank={toBank} paidAmount={0} />
            )}
          </div>
        </div>

        <Form.Item
          name="description"
          label="Description"
          rules={[{ required: true, message: 'Please enter description' }]}
        >
          <Input.TextArea rows={4} />
        </Form.Item>

        <div className="absolute bottom-0 left-0 right-0 border-t p-4">
          <Space className="w-full justify-end">
            <Button onClick={handleClose}>Cancel</Button>
            <Button type="primary" htmlType="submit" className="bg-blue-500">
              Submit
            </Button>
          </Space>
        </div>
      </Form>
    </Drawer>
  )
}
