import { CreateBankData, GetAllBanksFilter } from '@/common/types';
import { http } from './http';
import { Channels } from '@/common/constants';





export const createBank = async (data: CreateBankData) => {
    return await http.post(Channels.CREATE_BANK, { body: data });
};

export const getBankById = async (id: string) => {
    return await http.get(Channels.GET_BANK_BY_ID, { params: { id } });
};

export const deleteBank = async (id: string) => {
    return await http.delete(Channels.DELETE_BANK, { params: { id } });
};

export const getBanksForSelect = async () => {
    return await http.get(Channels.GET_BANKS_FOR_SELECT);
};

export const getAllBanks = async (filter: GetAllBanksFilter) => {
    return await http.get(Channels.GET_ALL_BANKS, { body: filter });
};

export const deactivateBank = async (id: string) => {
    return await http.post(Channels.DEACTIVATE_BANK, { params: { id } });
};

export const reactivateBank = async (id: string) => {
    return await http.post(Channels.REACTIVATE_BANK, { params: { id } });
};
