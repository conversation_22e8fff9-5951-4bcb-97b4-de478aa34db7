import { But<PERSON>, <PERSON>, Divider, Modal, Progress, Space, Typography, Upload, message } from 'antd'
import { ExclamationCircleOutlined, UploadOutlined } from '@ant-design/icons'
import { backupApi, resetApi } from '@/renderer/services'
import { IServerResponse } from '@/common/types'
import { useEffect } from 'react'
import { useState } from 'react'

const { Title, Text, Paragraph } = Typography
const { confirm } = Modal

const Reset = () => {
  const [backupProgress, setBackupProgress] = useState(0)
  const [restoreProgress, setRestoreProgress] = useState(0)
  const [backupStatus, setBackupStatus] = useState('')
  const [restoreStatus, setRestoreStatus] = useState('')
  const [isBackingUp, setIsBackingUp] = useState(false)
  const [isRestoring, setIsRestoring] = useState(false)

  useEffect(() => {
    // Listen for backup progress using the exposed API
    const removeBackupListener = window.api.onBackupProgress((data) => {
      setBackupProgress(data.progress)
      setBackupStatus(data.status)

      if (data.progress === 100) {
        // Reset progress after a delay when complete
        setTimeout(() => {
          setBackupProgress(0)
          setBackupStatus('')
          setIsBackingUp(false)
        }, 2000)
      }
    })

    // Listen for restore progress using the exposed API
    const removeRestoreListener = window.api.onRestoreProgress((data) => {
      setRestoreProgress(data.progress)
      setRestoreStatus(data.status)

      if (data.progress === 100) {
        // Reset progress after a delay when complete
        setTimeout(() => {
          setRestoreProgress(0)
          setRestoreStatus('')
          setIsRestoring(false)
        }, 2000)
      }
    })

    return () => {
      removeBackupListener()
      removeRestoreListener()
    }
  }, [])

  const handleBackup = async () => {
    try {
      setIsBackingUp(true)
      setBackupProgress(0)
      setBackupStatus('Starting backup...')
      const response = await backupApi.createBackup()
      if (response.error.error || response.data.error) {
        message.error(response.error.message || response.data.error.message)
        // Reset on error
        setBackupProgress(0)
        setBackupStatus('')
        setIsBackingUp(false)
      } else {
        message.success('Backup created successfully')
      }
    } catch (error: any) {
      message.error(error.message)
      // Reset on error
      setBackupProgress(0)
      setBackupStatus('')
      setIsBackingUp(false)
    }
  }

  const handleRestore = async (file: File) => {
    try {
      setIsRestoring(true)
      setRestoreProgress(0)
      setRestoreStatus('Starting restore...')
      const response = await backupApi.restoreBackup(file.path)
      if (response.error.error || response.data.error) {
        message.error(response.error.message || response.data.error.message)
        // Reset on error
        setRestoreProgress(0)
        setRestoreStatus('')
        setIsRestoring(false)
      } else {
        message.success('Database restored successfully')
      }
    } catch (error: any) {
      message.error(error.message)
      // Reset on error
      setRestoreProgress(0)
      setRestoreStatus('')
      setIsRestoring(false)
    }
  }

  const showConfirm = (title: string, action: () => Promise<IServerResponse>) => {
    confirm({
      title: `Are you sure you want to ${title}?`,
      icon: <ExclamationCircleOutlined />,
      content: 'This action cannot be undone.',
      okText: 'Yes',
      okType: 'danger',
      cancelText: 'No',
      async onOk() {
        const response = await action()
        if (response.error) {
          message.error(response.error.message)
        } else {
          message.success('Data cleared successfully')
        }
      }
    })
  }

  return (
    <div className="p-6">
      <Card>
        <Title level={2}>Database Backup & Restore</Title>
        <Space direction="vertical" style={{ width: '100%' }}>
          <Space>
            <Button onClick={handleBackup} loading={isBackingUp}>
              Create Backup
            </Button>
            <Upload
              accept=".sql"
              beforeUpload={(file) => {
                if (!file.name.endsWith('.sql')) {
                  message.error('Please select a valid SQL backup file')
                  return false
                }
                confirm({
                  title: 'Restore Database',
                  icon: <ExclamationCircleOutlined />,
                  content: 'This will overwrite your current database. Are you sure?',
                  okText: 'Yes',
                  okType: 'danger',
                  cancelText: 'No',
                  onOk: () => handleRestore(file)
                })
                return false
              }}
              showUploadList={false}
              disabled={isRestoring}
            >
              <Button icon={<UploadOutlined />} loading={isRestoring}>
                Restore from Backup
              </Button>
            </Upload>
          </Space>

          {(backupProgress > 0 || isBackingUp) && (
            <div>
              <Text>Backup Progress:</Text>
              <Progress
                percent={Math.round(backupProgress)}
                status={backupProgress === 100 ? 'success' : 'active'}
              />
              <Text type="secondary">{backupStatus}</Text>
            </div>
          )}

          {(restoreProgress > 0 || isRestoring) && (
            <div>
              <Text>Restore Progress:</Text>
              <Progress
                percent={Math.round(restoreProgress)}
                status={restoreProgress === 100 ? 'success' : 'active'}
              />
              <Text type="secondary">{restoreStatus}</Text>
            </div>
          )}
        </Space>

        <Divider />

        <Title level={2}>Development Reset Tools</Title>
        <Paragraph className="text-red-500">
          Warning: These tools are for development purposes only. They will permanently delete data
          from the database. Make sure you have backups before proceeding.
        </Paragraph>

        <Divider orientation="left">Ledger</Divider>
        <Button danger onClick={() => showConfirm('clear ledger entries', resetApi.clearLedger)}>
          Clear Ledger Entries
        </Button>

        <Divider orientation="left">Sales</Divider>
        <Space wrap>
          <Button
            danger
            onClick={() => showConfirm('clear stock entries', resetApi.clearStockEntries)}
          >
            Clear Stock Entries
          </Button>
          <Button
            danger
            onClick={() => showConfirm('clear walk-in sale items', resetApi.clearWalkInSaleItems)}
          >
            Clear Walk-in Sale Items
          </Button>
          <Button danger onClick={() => showConfirm('clear sale items', resetApi.clearSaleItems)}>
            Clear Sale Items
          </Button>
          <Button
            danger
            onClick={() =>
              showConfirm('clear walk-in sale invoices', resetApi.clearWalkInSaleInvoices)
            }
          >
            Clear Walk-in Sale Invoices
          </Button>
          <Button
            danger
            onClick={() => showConfirm('clear sale invoices', resetApi.clearSaleInvoices)}
          >
            Clear Sale Invoices
          </Button>
        </Space>

        <Divider orientation="left">Purchases</Divider>
        <Space wrap>
          <Button
            danger
            onClick={() => showConfirm('clear purchase items', resetApi.clearPurchaseItems)}
          >
            Clear Purchase Items
          </Button>
          <Button danger onClick={() => showConfirm('clear stock', resetApi.clearStock)}>
            Clear Stock
          </Button>
          <Button
            danger
            onClick={() => showConfirm('clear purchase invoices', resetApi.clearPurchaseInvoices)}
          >
            Clear Purchase Invoices
          </Button>
        </Space>

        <Divider orientation="left">Products & Categories</Divider>
        <Space>
          <Button danger onClick={() => showConfirm('clear products', resetApi.clearProducts)}>
            Clear Products
          </Button>
          <Button danger onClick={() => showConfirm('clear categories', resetApi.clearCategories)}>
            Clear Categories
          </Button>
        </Space>

        <Divider orientation="left">Payments & Banks</Divider>
        <Space wrap>
          <Button danger onClick={() => showConfirm('clear payments', resetApi.clearPayments)}>
            Clear Payments
          </Button>

          <Button danger onClick={() => showConfirm('clear banks', resetApi.clearBanks)}>
            Clear Banks
          </Button>
        </Space>

        <Divider orientation="left">Cash Management</Divider>
        <Space>
          <Button
            danger
            onClick={() => showConfirm('clear small counter', resetApi.clearSmallCounter)}
          >
            Clear Small Counter
          </Button>
          <Button danger onClick={() => showConfirm('clear cash vault', resetApi.clearCashVault)}>
            Clear Cash Vault
          </Button>
        </Space>

        <Divider orientation="left">Expenses</Divider>
        <Button danger onClick={() => showConfirm('clear expenses', resetApi.clearExpenses)}>
          Clear Expenses
        </Button>

        <Divider orientation="left">Invoice Numbers</Divider>
        <Button
          danger
          onClick={() => showConfirm('clear invoice numbers', resetApi.clearInvoiceNumbers)}
        >
          Clear Invoice Numbers
        </Button>

        <Divider orientation="left">Parties</Divider>
        <Button danger onClick={() => showConfirm('clear parties', resetApi.clearParties)}>
          Clear Parties
        </Button>

        <Divider orientation="left">Admin</Divider>
        <Button danger onClick={() => showConfirm('clear admins', resetApi.clearAdmins)}>
          Clear Admins
        </Button>

        <Divider />

        <div className="mt-8">
          <Title level={4} className="text-red-500">
            Clear All Data
          </Title>
          <Paragraph className="text-red-500">
            Warning: This will clear ALL data from the database. This action cannot be undone.
          </Paragraph>
          <Button
            danger
            type="primary"
            size="large"
            onClick={() => showConfirm('clear ALL data', resetApi.clearAllData)}
          >
            Clear All Data
          </Button>
        </div>
      </Card>
    </div>
  )
}

export default Reset
