import { useEffect, useState } from 'react'
import { DatePicker, Card, Table, Space } from 'antd'
import type { ColumnsType } from 'antd/es/table'
import dayjs from 'dayjs'
import { useApi } from '@/renderer/hooks'
import { bankLedgerApi } from '@/renderer/services'
import { DailyClosingBalance, DailyClosingBalancesParams } from '@/common/types/bankLedger'
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer
} from 'recharts'

interface DailyBalancesProps {
  bankId?: string
}

export const DailyBalances = ({ bankId }: DailyBalancesProps) => {
  const [dateRange, setDateRange] = useState<[Date, Date]>([
    dayjs().startOf('month').toDate(),
    dayjs().endOf('month').toDate()
  ])

  const { request, data, isLoading } = useApi<DailyClosingBalance[], [DailyClosingBalancesParams]>(
    bankLedgerApi.getDailyClosingBalances
  )

  useEffect(() => {
    if (bankId) {
      loadBalances()
    }
  }, [dateRange, bankId])

  const loadBalances = async () => {
    if (!bankId) return
    await request({
      bankId,
      startDate: dateRange[0],
      endDate: dateRange[1]
    })
  }

  const columns: ColumnsType<DailyClosingBalance> = [
    {
      title: 'Date',
      dataIndex: 'date',
      key: 'date',
      render: (date) => dayjs(date).format('YYYY-MM-DD')
    },
    {
      title: 'Credits',
      dataIndex: 'totalCredits',
      key: 'totalCredits',
      render: (amount) => (amount ? `Rs. ${amount.toLocaleString()}` : '-')
    },
    {
      title: 'Debits',
      dataIndex: 'totalDebits',
      key: 'totalDebits',
      render: (amount) => (amount ? `Rs. ${amount.toLocaleString()}` : '-')
    },
    {
      title: 'Closing Balance',
      dataIndex: 'closingBalance',
      key: 'closingBalance',
      render: (amount) => `Rs. ${amount.toLocaleString()}`
    }
  ]

  const chartData =
    data?.map((item) => ({
      ...item,
      date: dayjs(item.date).format('MMM DD'),
      formattedBalance: item.closingBalance
    })) ?? []

  return (
    <div className="space-y-4">
      <Card>
        <Space>
          <DatePicker.RangePicker
            value={[dayjs(dateRange[0]), dayjs(dateRange[1])]}
            onChange={(dates) => {
              if (dates?.[0] && dates?.[1]) {
                setDateRange([dates[0].toDate(), dates[1].toDate()])
              }
            }}
          />
        </Space>
      </Card>

      <Card title="Daily Closing Balance Trend">
        <div style={{ width: '100%', minHeight: '400px', position: 'relative' }}>
          <ResponsiveContainer width="100%" height={400}>
            <LineChart data={chartData} margin={{ top: 10, right: 30, left: 20, bottom: 5 }}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="date" />
              <YAxis tickFormatter={(value) => `Rs. ${value.toLocaleString()}`} />
              <Tooltip
                formatter={(value: number) => [`Rs. ${value.toLocaleString()}`, 'Closing Balance']}
              />
              <Line
                type="monotone"
                dataKey="formattedBalance"
                stroke="#8884d8"
                name="Closing Balance"
                dot={false}
                strokeWidth={2}
              />
            </LineChart>
          </ResponsiveContainer>
        </div>
      </Card>

      <Table
        size="small"
        columns={columns}
        dataSource={data ?? []}
        loading={isLoading}
        rowKey={(record) => record.date.toString()}
        pagination={false}
        scroll={{ y: 300 }}
      />
    </div>
  )
}
