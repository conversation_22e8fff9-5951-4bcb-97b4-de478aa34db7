import { createSlice, PayloadAction } from '@reduxjs/toolkit'
import type { SaleInvoiceFormData } from '@/common/types'

interface DraftInvoice {
    name: string
    formData: SaleInvoiceFormData
}

export interface DraftInvoiceState {
    walkInDrafts: DraftInvoice[]
    registeredDrafts: DraftInvoice[]
}

const initialState: DraftInvoiceState = {
    walkInDrafts: [],
    registeredDrafts: []
}

const draftInvoiceSlice = createSlice({
    name: 'draftInvoice',
    initialState,
    reducers: {
        saveWalkInDraft: (state, action: PayloadAction<DraftInvoice>) => {
            // Check for duplicate name
            if (state.walkInDrafts.some(draft => draft.name === action.payload.name)) {
                throw new Error('Draft name already exists')
            }
            state.walkInDrafts.push(action.payload)
        },
        saveRegisteredDraft: (state, action: PayloadAction<DraftInvoice>) => {
            // Check for duplicate name
            if (state.registeredDrafts.some(draft => draft.name === action.payload.name)) {
                throw new Error('Draft name already exists')
            }
            state.registeredDrafts.push(action.payload)
        },
        removeWalkInDraft: (state, action: PayloadAction<string>) => {
            state.walkInDrafts = state.walkInDrafts.filter(draft => draft.name !== action.payload)
        },
        removeRegisteredDraft: (state, action: PayloadAction<string>) => {
            state.registeredDrafts = state.registeredDrafts.filter(draft => draft.name !== action.payload)
        },
        clearAllDrafts: (state) => {
            state.walkInDrafts = []
            state.registeredDrafts = []
        }
    }
})

export const draftInvoiceActions = draftInvoiceSlice.actions
export const draftInvoiceReducer = draftInvoiceSlice.reducer 