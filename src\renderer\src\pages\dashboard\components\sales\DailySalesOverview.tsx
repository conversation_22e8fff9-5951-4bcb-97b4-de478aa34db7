import { Card, Skeleton, Typography, Statistic, Space, Button, Alert } from 'antd'
import { useApi } from '@/renderer/hooks'
import { dashboardApi } from '@/renderer/services'
import { DailyOverview } from '@/common/types/dashBoard'
import { MdPointOfSale, MdRefresh } from 'react-icons/md'
import { FaArrowDown, FaArrowUp } from 'react-icons/fa'
import { formatCurrency } from '@/renderer/utils'
import { useEffect } from 'react'

const { Title } = Typography

const DailySalesOverview = () => {
  const {
    data,
    isLoading,
    error,
    request: fetchDailySales
  } = useApi<DailyOverview, []>(dashboardApi.getDailySalesOverview)

  useEffect(() => {
    fetchDailySales()
  }, [])

  if (isLoading) {
    return (
      <div className="flex flex-col gap-4">
        <Space className="flex justify-between">
          <Space>
            <MdPointOfSale className="text-2xl text-indigo-600" />
            <Title level={5} className="!mb-0">
              1d Sales Overview
            </Title>
          </Space>
          <Button icon={<MdRefresh />} loading />
        </Space>
        <Skeleton active paragraph={{ rows: 4 }} />
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex flex-col gap-4">
        <Space className="flex justify-between">
          <Space>
            <MdPointOfSale className="text-2xl text-indigo-600" />
            <Title level={5} className="!mb-0">
              1d Sales Overview
            </Title>
          </Space>
          <Button icon={<MdRefresh />} onClick={() => fetchDailySales()} />
        </Space>
        <Alert
          message="Error"
          description="Failed to load daily sales data"
          type="error"
          showIcon
        />
      </div>
    )
  }

  if (!data) return null

  const getPercentageChange = (current: number, previous: number) => {
    if (previous === 0) return current > 0 ? 100 : 0
    return ((current - previous) / previous) * 100
  }

  const amountChange = getPercentageChange(data.totalAmount, data.comparisonWithPrevious.amount)
  const invoicesChange = getPercentageChange(
    data.totalInvoices,
    data.comparisonWithPrevious.invoices
  )
  const averageChange = getPercentageChange(
    data.averageInvoiceValue,
    data.comparisonWithPrevious.average
  )

  return (
    <div className="flex flex-col gap-4">
      <Space className="flex justify-between">
        <Space>
          <MdPointOfSale className="text-2xl text-indigo-600" />
          <Title level={5} className="!mb-0">
            1d Sales Overview
          </Title>
        </Space>
        <Button icon={<MdRefresh />} onClick={() => fetchDailySales()} />
      </Space>

      <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-1 xl:grid-cols-1">
        <Statistic
          title="Total Sales"
          value={data.totalAmount}
          formatter={(value) => formatCurrency(value as number)}
          prefix={
            amountChange >= 0 ? (
              <FaArrowUp className="text-green-500" />
            ) : (
              <FaArrowDown className="text-red-500" />
            )
          }
          suffix={
            <small className={amountChange >= 0 ? 'text-green-500' : 'text-red-500'}>
              {Math.abs(amountChange).toFixed(1)}%
            </small>
          }
        />
        <Statistic
          title="Total Invoices"
          value={data.totalInvoices}
          prefix={
            invoicesChange >= 0 ? (
              <FaArrowUp className="text-green-500" />
            ) : (
              <FaArrowDown className="text-red-500" />
            )
          }
          suffix={
            <small className={invoicesChange >= 0 ? 'text-green-500' : 'text-red-500'}>
              {Math.abs(invoicesChange).toFixed(1)}%
            </small>
          }
        />
        <Statistic
          title="Average Invoice Value"
          value={data.averageInvoiceValue}
          formatter={(value) => formatCurrency(value as number)}
          prefix={
            averageChange >= 0 ? (
              <FaArrowUp className="text-green-500" />
            ) : (
              <FaArrowDown className="text-red-500" />
            )
          }
          suffix={
            <small className={averageChange >= 0 ? 'text-green-500' : 'text-red-500'}>
              {Math.abs(averageChange).toFixed(1)}%
            </small>
          }
        />
      </div>
    </div>
  )
}

export default DailySalesOverview
