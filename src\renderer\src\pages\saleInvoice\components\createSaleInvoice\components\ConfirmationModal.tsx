import { Modal } from 'antd'
import { useTheme } from '@/renderer/contexts'
import { cancelInvoiceNumber } from '../utils/invoiceNumber'

interface ConfirmationModalProps {
  open: boolean
  onConfirm: () => void
  onCancel: () => void
  title?: string
  content?: string
  invoiceNumber?: string
}

export const ConfirmationModal = ({
  open,
  onConfirm,
  onCancel,
  title = 'Are you sure you want to go back?',
  content = 'This will cancel the current invoice. This action cannot be undone.',
  invoiceNumber
}: ConfirmationModalProps) => {
  const { isDarkMode } = useTheme()

  const handleConfirm = async () => {
    if (invoiceNumber) {
      await cancelInvoiceNumber(invoiceNumber)
    }
    onConfirm()
  }

  return (
    <Modal
      title={title}
      open={open}
      onOk={handleConfirm}
      onCancel={onCancel}
      okText="Yes"
      cancelText="No"
      okButtonProps={{
        danger: true,
        className: isDarkMode ? 'bg-red-500 hover:!bg-red-600' : ''
      }}
      className={isDarkMode ? 'dark-theme-modal' : ''}
    >
      <p>{content}</p>
    </Modal>
  )
}
