import jsPDF from 'jspdf'
import 'jspdf-autotable'
import dayjs from 'dayjs'
import { A4_CONFIG, PDF_STYLES, savePDF, printPDF } from '@/renderer/utils/pdfUtils'
import { cashVaultApi } from '@/renderer/services'
import type { CashVaultStatement, GetCashVaultStatementParams } from '@/common/types'

export const generateCashVaultStatementPDF = async (
    startDate: Date,
    endDate: Date
): Promise<jsPDF> => {
    // Fetch all statement entries for PDF (no pagination)
    const response = await cashVaultApi.generateCashVaultStatement({
        startDate,
        endDate,
        page: 1,
        pageSize: 100000 // Large page size to get all entries
    })

    if (response.error.error || response.data.error) {
        throw new Error(response.error.message || response.data.error.message)
    }

    const statement: CashVaultStatement = response.data.data

    // Create PDF document with A4 format
    const doc = new jsPDF(A4_CONFIG)
    const pageWidth = doc.internal.pageSize.width
    const pageHeight = doc.internal.pageSize.height
    const margin = 15 // mm
    const effectiveWidth = pageWidth - 2 * margin

    // Format a number with commas and decimal places
    const formatNumber = (num: number | null | undefined): string => {
        if (num === null || num === undefined || isNaN(num)) return '0.00';
        return num.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 });
    };

    // Draw header
    doc.setFontSize(PDF_STYLES.header.fontSize)
    doc.setFont('helvetica', 'bold')
    doc.text('Cash Vault Statement', pageWidth / 2, margin, { align: 'center' })

    // Add company info (you can add your company logo here if available)
    doc.setFontSize(PDF_STYLES.header.titleFontSize)
    doc.setFont('helvetica', 'normal')
    doc.text('SJ Lace Pvt Ltd', pageWidth / 2, margin + 8, { align: 'center' })

    // Add statement period
    doc.setFontSize(10)
    doc.text(`Statement Period: ${dayjs(statement.startDate).format('DD/MM/YYYY')} - ${dayjs(statement.endDate).format('DD/MM/YYYY')}`,
        pageWidth / 2, margin + 15, { align: 'center' })

    // Add horizontal line
    doc.setDrawColor(0, 0, 0)
    doc.setLineWidth(0.2)
    doc.line(margin, margin + 20, pageWidth - margin, margin + 20)

    // Create summary cards in a table format
    const summaryY = margin + 25

    doc.setFillColor(248, 249, 250) // Light gray background for summary section
    doc.rect(margin, summaryY, effectiveWidth, 30, 'F')

    // Summary headings
    doc.setFontSize(10)
    doc.setFont('helvetica', 'bold')

    const col1X = margin + 5
    const col2X = margin + effectiveWidth / 4 + 5
    const col3X = margin + effectiveWidth / 2 + 5
    const col4X = margin + 3 * effectiveWidth / 4 + 5

    doc.text('Opening Balance', col1X, summaryY + 7)
    doc.text('Total Credits', col2X, summaryY + 7)
    doc.text('Total Debits', col3X, summaryY + 7)
    doc.text('Closing Balance', col4X, summaryY + 7)

    // Summary values
    doc.setFontSize(11)
    doc.setFont('helvetica', 'normal')

    // Set colors based on values
    doc.setTextColor(0, 0, 0) // Black for opening balance
    doc.text(`Rs. ${formatNumber(statement.openingBalance)}`, col1X, summaryY + 15)

    doc.setTextColor(0, 128, 0) // Green for credits
    doc.text(`Rs. ${formatNumber(statement.summary.totalCredits)}`, col2X, summaryY + 15)

    doc.setTextColor(255, 0, 0) // Red for debits
    doc.text(`Rs. ${formatNumber(statement.summary.totalDebits)}`, col3X, summaryY + 15)

    // Closing balance color depends on value
    doc.setTextColor(statement.closingBalance >= 0 ? 0 : 255, statement.closingBalance >= 0 ? 128 : 0, 0)
    doc.text(`Rs. ${formatNumber(statement.closingBalance)}`, col4X, summaryY + 15)

    // Net change
    doc.setTextColor(0, 0, 0) // Reset to black
    doc.setFontSize(9)
    doc.text(`Net Change: Rs. ${formatNumber(statement.summary.net)}`, col1X, summaryY + 23)

    // Add the transaction table
    const tableY = summaryY + 35

    // Define columns for the table
    const tableColumns = [
        { header: 'S.No', dataKey: 'serialNumber', width: 10 },
        { header: 'Date', dataKey: 'date', width: 25 },
        { header: 'Description', dataKey: 'description', width: 75 },
        { header: 'Credit (Rs.)', dataKey: 'credit', width: 25 },
        { header: 'Debit (Rs.)', dataKey: 'debit', width: 25 },
        { header: 'Balance (Rs.)', dataKey: 'balance', width: 30 }
    ]

    // Prepare table data
    const tableRows = statement.entries.map((entry, index) => ({
        serialNumber: index + 1,
        date: entry.date ? dayjs(entry.date).format('DD/MM/YYYY') : '-',
        description: entry.description || '',
        credit: entry.credit ? formatNumber(entry.credit) : '-',
        debit: entry.debit ? formatNumber(entry.debit) : '-',
        balance: formatNumber(entry.runningBalance),
        // Store raw values for conditional styling
        _creditValue: entry.credit || 0,
        _debitValue: entry.debit || 0,
        _balanceValue: entry.runningBalance
    }))

    // Reset text color for table
    doc.setTextColor(0, 0, 0)

    // @ts-ignore - jspdf-autotable types are not complete
    doc.autoTable({
        startY: tableY,
        columns: tableColumns,
        body: tableRows,
        headStyles: {
            ...PDF_STYLES.table.headStyles,
            halign: 'center'
        },
        styles: {
            ...PDF_STYLES.table.styles
        },
        columnStyles: {
            0: { halign: 'center' }, // S.No centered
            1: { halign: 'center' }, // Date centered
            2: { halign: 'left' },   // Description left-aligned
            3: { halign: 'right' },  // Credit right-aligned
            4: { halign: 'right' },  // Debit right-aligned
            5: { halign: 'right' }   // Balance right-aligned
        },
        margin: { left: margin, right: margin },
        // Apply conditional styling to cells
        didParseCell: function (data: any) {
            if (data.section === 'body') {
                const row = data.row.raw as any;

                // Credit column - Green text for values > 0
                if (data.column.dataKey === 'credit' && row._creditValue > 0) {
                    data.cell.styles.textColor = [0, 128, 0]; // Green
                }

                // Debit column - Red text for values > 0
                if (data.column.dataKey === 'debit' && row._debitValue > 0) {
                    data.cell.styles.textColor = [255, 0, 0]; // Red
                }

                // Balance column - Green for positive, Red for negative
                if (data.column.dataKey === 'balance') {
                    data.cell.styles.textColor = row._balanceValue >= 0 ? [0, 128, 0] : [255, 0, 0];
                }
            }
        },
        didDrawPage: function (data: any) {
            // Add footer with page numbers on each page
            const pageCount = (doc as any).internal.getNumberOfPages()
            doc.setFontSize(PDF_STYLES.footer.fontSize)
            doc.setTextColor(0, 0, 0)
            doc.text(
                `Page ${data.pageNumber} of ${pageCount}`,
                pageWidth / 2,
                pageHeight - PDF_STYLES.footer.margin,
                { align: 'center' }
            )

            // Add a timestamp
            const timestamp = `Generated on: ${dayjs().format('DD/MM/YYYY HH:mm')}`
            doc.text(
                timestamp,
                pageWidth - margin,
                pageHeight - PDF_STYLES.footer.margin,
                { align: 'right' }
            )
        }
    })

    return doc
}

export const handleCashVaultStatementPDF = async (
    startDate: Date,
    endDate: Date,
    action: 'save' | 'print'
): Promise<void> => {
    try {
        const doc = await generateCashVaultStatementPDF(startDate, endDate)
        const filename = `CashVault_Statement_${dayjs(startDate).format('YYYY-MM-DD')}_to_${dayjs(endDate).format('YYYY-MM-DD')}`

        if (action === 'save') {
            await savePDF(doc, filename)
        } else {
            await printPDF(doc)
        }
    } catch (error) {
        console.error('Failed to generate PDF:', error)
        throw error
    }
} 