import { prisma } from "../db"
import { exec } from 'child_process';
import { promisify } from 'util';
import path from 'path';
import { app, BrowserWindow } from 'electron';
import { format } from 'date-fns';
import fs from 'fs';

const execAsync = promisify(exec);

class BackupService {
    async createBackup(backupPath: string): Promise<{ success: boolean; error?: string }> {
        try {
            const win = BrowserWindow.getFocusedWindow();
            const databaseUrl = process.env.DATABASE_URL;
            if (!databaseUrl) {
                throw new Error('Database URL not found');
            }

            // Parse connection details from DATABASE_URL
            const url = new URL(databaseUrl);
            const username = url.username;
            const password = url.password;
            const database = url.pathname.slice(1);
            const host = url.hostname;
            const port = url.port;

            // Create backup filename with timestamp
            const timestamp = format(new Date(), 'yyyy-MM-dd_HH-mm-ss');
            const backupFileName = `sj-lace_backup_${timestamp}.sql`;
            const fullBackupPath = path.join(backupPath, backupFileName);

            // Use the full path to pg_dump
            const pgDumpPath = 'C:\\Program Files\\PostgreSQL\\17\\bin\\pg_dump.exe';
            const psqlPath = 'C:\\Program Files\\PostgreSQL\\17\\bin\\psql.exe';

            // Verify pg_dump exists
            if (!fs.existsSync(pgDumpPath)) {
                throw new Error(`pg_dump not found at ${pgDumpPath}. Please verify PostgreSQL installation.`);
            }

            win?.webContents.send('backup-progress', { progress: 0, status: 'Starting backup...' });

            // Test connection
            try {
                const testConnection = `"${psqlPath}" -h ${host} -p ${port} -U ${username} -d ${database} -c "SELECT 1"`;
                await execAsync(testConnection, { env: { ...process.env, PGPASSWORD: password } });
            } catch (error: any) {
                console.error('Connection test failed:', error);
                throw new Error(`Database connection failed: ${error.message}`);
            }

            // Optimized pg_dump command
            // -F p: plain text format
            // -v: verbose mode for progress tracking
            // --if-exists: add IF EXISTS to DROP commands
            // --clean: clean (drop) database objects before recreating
            // --no-owner: skip restoration of object ownership
            // --no-privileges: skip restoration of access privileges (grant/revoke)
            const command = `"${pgDumpPath}" -h ${host} -p ${port} -U ${username} -F p -v --if-exists --clean --no-owner --no-privileges ${database} > "${fullBackupPath}"`;

            // Set PGPASSWORD environment variable for authentication
            const env = { ...process.env, PGPASSWORD: password };

            // Execute backup command
            const child = exec(command, { env });

            let errorOutput = '';
            let tableCount = 0;
            let completedTables = 0;

            // Track progress through stderr
            child.stderr?.on('data', (data) => {
                const dataStr = data.toString();
                errorOutput += dataStr;

                // Count tables and track progress
                if (dataStr.includes('* dumping table')) {
                    tableCount++;
                    win?.webContents.send('backup-progress', {
                        progress: 10 + Math.min(Math.round((tableCount / 20) * 40), 40),
                        status: `Found ${tableCount} tables...`
                    });
                } else if (dataStr.includes('* dumped table')) {
                    completedTables++;
                    const progress = Math.min(50 + Math.round((completedTables / (tableCount || 1)) * 40), 90);
                    win?.webContents.send('backup-progress', {
                        progress,
                        status: `Backed up ${completedTables} of ${tableCount} tables...`
                    });
                }
            });

            await new Promise((resolve, reject) => {
                child.on('exit', (code) => {
                    if (code === 0) {
                        if (fs.existsSync(fullBackupPath) && fs.statSync(fullBackupPath).size > 0) {
                            win?.webContents.send('backup-progress', {
                                progress: 100,
                                status: 'Backup completed successfully!'
                            });
                            resolve(null);
                        } else {
                            reject(new Error('Backup file was not created or is empty'));
                        }
                    } else {
                        reject(new Error(`Backup failed with code ${code}. Error output: ${errorOutput}`));
                    }
                });

                child.on('error', (error) => {
                    reject(new Error(`Failed to start backup process: ${error.message}`));
                });
            });

            return { success: true };
        } catch (error: any) {
            console.error('Backup failed:', error);
            return {
                success: false,
                error: error.message || 'Failed to create backup'
            };
        }
    }

    async restoreBackup(backupPath: string): Promise<{ success: boolean; error?: string }> {
        try {
            const win = BrowserWindow.getFocusedWindow();
            const databaseUrl = process.env.DATABASE_URL;
            if (!databaseUrl) {
                throw new Error('Database URL not found');
            }

            // Parse connection details from DATABASE_URL
            const url = new URL(databaseUrl);
            const username = url.username;
            const password = url.password;
            const database = url.pathname.slice(1);
            const host = url.hostname;
            const port = url.port;

            // Path to PostgreSQL binaries
            const psqlPath = 'C:\\Program Files\\PostgreSQL\\17\\bin\\psql.exe';

            win?.webContents.send('restore-progress', { progress: 0, status: 'Starting restore...' });

            // First, disconnect Prisma
            win?.webContents.send('restore-progress', { progress: 5, status: 'Disconnecting active connections...' });
            await prisma.$disconnect();
            await new Promise(resolve => setTimeout(resolve, 1000));

            // Then, drop all existing connections except ours
            win?.webContents.send('restore-progress', { progress: 10, status: 'Closing database connections...' });
            const dropConnections = `"${psqlPath}" -h ${host} -p ${port} -U ${username} -d postgres -c "SELECT pg_terminate_backend(pid) FROM pg_stat_activity WHERE datname = '${database}' AND pid <> pg_backend_pid();"`;
            await execAsync(dropConnections, { env: { ...process.env, PGPASSWORD: password } });
            await new Promise(resolve => setTimeout(resolve, 2000));

            // Drop and recreate the database
            win?.webContents.send('restore-progress', { progress: 20, status: 'Recreating database...' });
            try {
                await execAsync(`"${psqlPath}" -h ${host} -p ${port} -U ${username} -d postgres -c "DROP DATABASE IF EXISTS ${database};"`, { env: { ...process.env, PGPASSWORD: password } });
                await new Promise(resolve => setTimeout(resolve, 1000));

                await execAsync(`"${psqlPath}" -h ${host} -p ${port} -U ${username} -d postgres -c "CREATE DATABASE ${database};"`, { env: { ...process.env, PGPASSWORD: password } });
                await new Promise(resolve => setTimeout(resolve, 1000));
            } catch (error: any) {
                console.error('Database recreation error:', error);
                throw new Error(`Failed to recreate database: ${error.message}`);
            }

            win?.webContents.send('restore-progress', { progress: 40, status: 'Restoring data...' });

            // Use psql to restore the backup
            // -v ON_ERROR_STOP=1: Stop on first error
            // --single-transaction: Wrap restore in a single transaction
            const restoreCommand = `"${psqlPath}" -h ${host} -p ${port} -U ${username} -d ${database} -v ON_ERROR_STOP=1 --single-transaction -f "${backupPath}"`;
            const child = exec(restoreCommand, { env: { ...process.env, PGPASSWORD: password } });

            let errorOutput = '';
            let lastProgress = 40;

            // Track progress through stderr
            child.stderr?.on('data', (data) => {
                const dataStr = data.toString();
                errorOutput += dataStr;

                // Update progress based on the output
                if (dataStr.includes('SET')) {
                    lastProgress = Math.min(lastProgress + 1, 90);
                    win?.webContents.send('restore-progress', {
                        progress: lastProgress,
                        status: 'Restoring database objects...'
                    });
                }
            });

            await new Promise((resolve, reject) => {
                child.on('exit', async (code) => {
                    if (code === 0) {
                        try {
                            // Reconnect Prisma
                            await prisma.$connect();

                            win?.webContents.send('restore-progress', {
                                progress: 100,
                                status: 'Restore completed successfully!'
                            });
                            resolve(null);
                        } catch (error: any) {
                            console.error('Database reconnection error:', error);
                            reject(new Error(`Failed to reconnect to database: ${error.message}`));
                        }
                    } else {
                        reject(new Error(`Restore failed with code ${code}. Error output: ${errorOutput}`));
                    }
                });

                child.on('error', (error) => {
                    reject(new Error(`Failed to start restore process: ${error.message}`));
                });
            });

            // Add a delay before returning to allow Prisma to fully reconnect
            await new Promise(resolve => setTimeout(resolve, 2000));

            return { success: true };
        } catch (error: any) {
            console.error('Restore failed:', error);
            return {
                success: false,
                error: error.message || 'Failed to restore backup'
            };
        }
    }
}

export const backupService = new BackupService();
