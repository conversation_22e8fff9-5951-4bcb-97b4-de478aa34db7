// src/renderer/src/pages/banks/components/BankDetailsModal/TransactionSummary.tsx
import { useEffect, useState } from 'react'
import { Card, DatePicker, Table, Space } from 'antd'
import type { ColumnsType } from 'antd/es/table'
import dayjs from 'dayjs'
import { useApi } from '@/renderer/hooks'
import { bankLedgerApi } from '@/renderer/services'
import {
  BankTransactionSummary,
  BankTransactionSummaryParams,
  TransactionSummaryByType
} from '@/common/types/bankLedger'

interface TransactionSummaryProps {
  bankId: string
}

export const TransactionSummary = ({ bankId }: TransactionSummaryProps) => {
  const [dateRange, setDateRange] = useState<[Date, Date]>([
    dayjs().startOf('month').toDate(),
    dayjs().endOf('month').toDate()
  ])

  const { request, data, isLoading } = useApi<
    BankTransactionSummary,
    [BankTransactionSummaryParams]
  >(bankLedgerApi.getBankTransactionSummary)

  useEffect(() => {
    loadSummary()
  }, [dateRange, bankId])

  const loadSummary = async () => {
    await request({
      bankId,
      startDate: dateRange[0],
      endDate: dateRange[1]
    })
  }

  const columns: ColumnsType<TransactionSummaryByType> = [
    {
      title: 'Reference Type',
      dataIndex: 'referenceType',
      key: 'referenceType'
    },
    {
      title: 'Credits',
      dataIndex: 'creditTotal',
      key: 'creditTotal',
      render: (amount) => `Rs. ${amount.toLocaleString()}`
    },
    {
      title: 'Debits',
      dataIndex: 'debitTotal',
      key: 'debitTotal',
      render: (amount) => `Rs. ${amount.toLocaleString()}`
    },
    {
      title: 'Count',
      dataIndex: 'count',
      key: 'count'
    }
  ]

  return (
    <Space direction="vertical" size="large" style={{ width: '100%' }}>
      <DatePicker.RangePicker
        value={[dayjs(dateRange[0]), dayjs(dateRange[1])]}
        onChange={(dates) => {
          if (dates) {
            setDateRange([dates[0]!.toDate(), dates[1]!.toDate()])
          }
        }}
      />

      <Table
        size="small"
        columns={columns}
        dataSource={data?.summaryByType}
        loading={isLoading}
        rowKey="referenceType"
        pagination={false}
        summary={() => (
          <Table.Summary>
            <Table.Summary.Row>
              <Table.Summary.Cell index={0}>Overall Totals</Table.Summary.Cell>
              <Table.Summary.Cell index={1}>
                Rs. {data?.overall.totalCredits.toLocaleString()}
              </Table.Summary.Cell>
              <Table.Summary.Cell index={2}>
                Rs. {data?.overall.totalDebits.toLocaleString()}
              </Table.Summary.Cell>
              <Table.Summary.Cell index={3}>{data?.overall.totalCount}</Table.Summary.Cell>
            </Table.Summary.Row>
          </Table.Summary>
        )}
      />
    </Space>
  )
}
