import { useRef, useState } from 'react'
import { Button, Form, InputNumber, Select, Space, Table, App } from 'antd'
import type { RefSelectProps } from 'antd/es/select'
import { DeleteOutlined, PlusOutlined } from '@ant-design/icons'
import { useProductContext, useTheme } from '@/renderer/contexts'
import type { SaleItemData } from '@/common/types'

interface Props {
  items: SaleItemData[]
  onItemsChange: (items: SaleItemData[]) => void
  selectedProductId: string
  setSelectedProductId: (id: string) => void
  productDetails: any
}

export const ItemsTable = ({
  items,
  onItemsChange,
  selectedProductId,
  setSelectedProductId,
  productDetails
}: Props) => {
  const [quantity, setQuantity] = useState<number>(0)
  const { products } = useProductContext()
  const { isDarkMode } = useTheme()
  const { message } = App.useApp()
  const productSelectRef = useRef<RefSelectProps>(null)

  const handleAddItem = () => {
    if (!selectedProductId || !quantity || !productDetails?.salePrice) return

    // Check if item already exists
    const existingItemIndex = items.findIndex((item) => item.productId === selectedProductId)
    if (existingItemIndex !== -1) {
      message.error('This product is already in the invoice. Please delete it first.')
      return
    }

    // Check if quantity exceeds available stock
    if (quantity > productDetails.quantityInStock) {
      message.error('Quantity cannot exceed available stock')
      return
    }

    // Add new item
    onItemsChange([
      ...items,
      {
        productId: selectedProductId,
        totalQuantity: quantity,
        salePrice: productDetails.salePrice
      }
    ])

    // Reset form
    setSelectedProductId('')
    setQuantity(0)
    productSelectRef.current?.focus()
  }

  const handleDeleteItem = (index: number) => {
    const updatedItems = [...items]
    updatedItems.splice(index, 1)
    onItemsChange(updatedItems)
  }

  // Function to determine row className
  const getRowClassName = (record: SaleItemData) => {
    if (selectedProductId && record.productId === selectedProductId) {
      return 'bg-red-200 dark:bg-red-900/40 animate-pulse'
    }
    return ''
  }

  const columns = [
    {
      title: 'Sr. No.',
      dataIndex: 'index',
      key: 'index',
      width: 70,
      render: (_, __, index) => index + 1
    },
    {
      title: 'Product',
      dataIndex: 'productId',
      key: 'productId',
      render: (productId: string) => products.find((p) => p.value === productId)?.label
    },
    {
      title: 'Quantity',
      dataIndex: 'totalQuantity',
      key: 'totalQuantity',
      align: 'right' as const,
      render: (quantity: number) => quantity.toLocaleString('en-US')
    },
    {
      title: 'Sale Price',
      dataIndex: 'salePrice',
      key: 'salePrice',
      align: 'right' as const,
      render: (price: number) => price.toLocaleString('en-US', { minimumFractionDigits: 2 })
    },
    {
      title: 'Total',
      key: 'total',
      align: 'right' as const,
      render: (_, record: SaleItemData) =>
        (record.totalQuantity * record.salePrice).toLocaleString('en-US', {
          minimumFractionDigits: 2
        })
    },
    {
      title: 'Action',
      key: 'action',
      width: 60,
      render: (_, _record: SaleItemData, index: number) => (
        <Button
          type="text"
          danger
          icon={<DeleteOutlined />}
          onClick={() => handleDeleteItem(index)}
        />
      )
    }
  ]

  return (
    <div className="space-y-4">
      <Form layout="inline">
        <Form.Item
          label="Product"
          required
          validateStatus={selectedProductId ? 'success' : undefined}
          className="w-96"
        >
          <Select
            ref={productSelectRef}
            showSearch
            className="w-full"
            allowClear
            placeholder="Select product"
            value={selectedProductId || undefined}
            onChange={(value) => {
              setSelectedProductId(value)
            }}
            options={products}
            filterOption={(input, option) =>
              (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
            }
          />
        </Form.Item>

        <Form.Item label="Quantity" required validateStatus={quantity > 0 ? 'success' : undefined}>
          <InputNumber
            min={0}
            max={productDetails?.quantityInStock || 0}
            value={quantity}
            onChange={(value) => setQuantity(value || 0)}
            formatter={(value) => value?.toLocaleString('en-US') || ''}
            parser={(value) => parseFloat(value?.replace(/,/g, '') || '0')}
            className="w-32"
          />
        </Form.Item>

        <Form.Item
          label="Sale Price"
          required
          validateStatus={productDetails?.salePrice > 0 ? 'success' : undefined}
        >
          <InputNumber
            disabled={true}
            min={Number(0)}
            value={productDetails?.salePrice}
            className="w-32"
            formatter={(value) =>
              value ? value.toLocaleString('en-US', { minimumFractionDigits: 2 }) : ''
            }
            parser={(value) => parseFloat(value?.replace(/,/g, '') || '0')}
          />
        </Form.Item>

        <Form.Item>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={handleAddItem}
            disabled={!selectedProductId || !quantity || !productDetails?.salePrice}
            className={`!border-green-500 !bg-green-500 hover:!border-green-600 hover:!bg-green-600 ${
              isDarkMode
                ? 'disabled:!border-gray-900 disabled:!bg-gray-600 disabled:!text-gray-400'
                : 'disabled:!border-gray-500 disabled:!bg-gray-300 disabled:!text-gray-50'
            }`}
          >
            Add Item
          </Button>
        </Form.Item>
      </Form>

      <Table
        size="small"
        rowKey="productId"
        columns={columns}
        dataSource={items}
        pagination={false}
        rowClassName={getRowClassName}
        summary={(data) => {
          const total = data.reduce((sum, item) => sum + item.totalQuantity * item.salePrice, 0)
          return (
            <Table.Summary.Row>
              <Table.Summary.Cell index={0} colSpan={3} align="right">
                <strong>Total:</strong>
              </Table.Summary.Cell>
              <Table.Summary.Cell index={1} align="right">
                <strong>{total.toLocaleString('en-US', { minimumFractionDigits: 2 })}</strong>
              </Table.Summary.Cell>
              <Table.Summary.Cell index={2} />
            </Table.Summary.Row>
          )
        }}
      />
    </div>
  )
}
