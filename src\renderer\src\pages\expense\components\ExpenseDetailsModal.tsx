import { useEffect, useState } from 'react'
import { Modal, Spin, Descriptions, Tag, App } from 'antd'
import { expenseApi } from '@/renderer/services'
import dayjs from 'dayjs'
import { ExpenseItem } from '@/common/types/expense'
import { formatCurrency } from '@/renderer/utils'

interface ExpenseDetailsModalProps {
  expenseId: string | null
  open: boolean
  onClose: () => void
}

export const ExpenseDetailsModal = ({ expenseId, open, onClose }: ExpenseDetailsModalProps) => {
  const [loading, setLoading] = useState(false)
  const [expense, setExpense] = useState<ExpenseItem | null>(null)
  const { message } = App.useApp()

  useEffect(() => {
    if (expenseId && open) {
      fetchExpenseDetails()
    }
  }, [expenseId, open])

  const fetchExpenseDetails = async () => {
    if (!expenseId) return
    setLoading(true)

    const response = await expenseApi.getExpense(expenseId)

    setLoading(false)

    if (response.error.error || response.data.error) {
      message.error(response.error.message || response.data.error.message)
      return
    }

    setExpense(response.data.data)
  }

  const handleCancel = () => {
    setExpense(null)
    onClose()
  }

  const getPaymentSourceDisplay = () => {
    if (!expense?.Ledger) return 'Unknown'

    if (expense.Ledger.cashSource === 'BANK') {
      return `Bank: ${expense.Ledger.bank?.name || 'Unknown'}`
    }

    return expense.Ledger.cashSource === 'SMALL_COUNTER' ? 'Cash Counter' : 'Cash Vault'
  }

  return (
    <Modal title="Expense Details" open={open} onCancel={handleCancel} footer={null} width={600}>
      {loading ? (
        <div className="flex items-center justify-center py-8">
          <Spin size="large" />
        </div>
      ) : expense ? (
        <Descriptions bordered column={1} size="small">
          <Descriptions.Item label="Category">{expense.category}</Descriptions.Item>
          <Descriptions.Item label="Amount">{formatCurrency(expense.amount)}</Descriptions.Item>
          <Descriptions.Item label="Description">{expense.description || '-'}</Descriptions.Item>
          <Descriptions.Item label="Date">
            {dayjs(expense.date).format('YYYY-MM-DD')}
          </Descriptions.Item>
          <Descriptions.Item label="Payment Source">{getPaymentSourceDisplay()}</Descriptions.Item>
          <Descriptions.Item label="Status">
            <Tag color={expense.status === 'ACTIVE' ? 'green' : 'red'}>{expense.status}</Tag>
          </Descriptions.Item>
          <Descriptions.Item label="Created By">{expense.createdBy?.name || '-'}</Descriptions.Item>

          {expense.status === 'VOID' && (
            <>
              <Descriptions.Item label="Voided By">
                {expense.voidedBy?.name || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="Voided At">
                {expense.voidedAt ? dayjs(expense.voidedAt).format('YYYY-MM-DD HH:mm:ss') : '-'}
              </Descriptions.Item>
              <Descriptions.Item label="Voiding Reason">
                {expense.voidingReason || '-'}
              </Descriptions.Item>
            </>
          )}
        </Descriptions>
      ) : (
        <div className="py-4 text-center">No expense data found</div>
      )}
    </Modal>
  )
}
