import { http } from './http';
import { Channels } from '@/common/constants';
import {
    FinancialOverviewParams,
    InventoryReportParams,
    CashFlowParams,
    SalesReportParams,
    CustomerAnalyticsParams,
    AgeingReportParams,
    DailyOperationsParams,
    AuditReportParams
} from '@/common/types';

export const generateFinancialOverview = async (params: FinancialOverviewParams) => {
    console.log(params)
    return await http.get(Channels.GENERATE_FINANCIAL_OVERVIEW, { params });
};

export const generateInventoryValuation = async (params: InventoryReportParams) => {
    return await http.get(Channels.GENERATE_INVENTORY_VALUATION, { params });
};

export const generateCashFlow = async (params: CashFlowParams) => {
    return await http.get(Channels.GENERATE_CASH_FLOW, { params });
};

export const generateSalesPerformance = async (params: SalesReportParams) => {
    return await http.get(Channels.GENERATE_SALES_PERFORMANCE, { params });
};

export const generateCustomerAnalytics = async (params: CustomerAnalyticsParams) => {
    return await http.get(Channels.GENERATE_CUSTOMER_ANALYTICS, { params });
};

export const generateAgeingReport = async (params: AgeingReportParams) => {
    return await http.get(Channels.GENERATE_AGEING_REPORT, { params });
};

export const generateDailyOperations = async (params: DailyOperationsParams) => {
    return await http.get(Channels.GENERATE_DAILY_OPERATIONS, { params });
};

export const generateAuditReport = async (params: AuditReportParams) => {
    return await http.get(Channels.GENERATE_AUDIT_REPORT, { params });
};