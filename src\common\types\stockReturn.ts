import { CustomerType, CashLocation } from '@prisma/client'

export interface StockReturnItem {
    productId: string
    quantity: number
    purchasePrice: number
    salePrice: number
    originalInvoiceId: string
    originalInvoiceType: CustomerType
}

export interface LegacyStockReturnItem {
    productId: string
    quantity: number
    purchasePrice: number
}

export interface SaleInvoiceDetails {
    id: string
    invoiceNumber: string
    date: Date
    customerType: CustomerType
    customerName?: string
    discountAmount: number
    totalAmount: number
    items: Array<{
        id: string
        productId: string
        productName: string
        totalQuantity: number
        salePrice: number
        total: number
        returnedQuantity?: number
    }>
}

export interface ProcessStockReturnParams {
    items: StockReturnItem[]
    originalInvoiceNumber: string
    adminId: string
    paymentDetails?: {
        source: CashLocation
        bankId?: string
    }
}

export interface ProcessLegacyStockReturnParams {
    items: LegacyStockReturnItem[]
    customerId?: string  // Optional for registered customers
    customerName?: string // For walk-in customers
    customerType: CustomerType
    adminId: string
    approverNote: string // Reason/justification for accepting legacy return
    paymentDetails?: {   // Optional for registered customers, required for walk-in
        source: CashLocation
        bankId?: string
    }
    returnDate?: Date // Date when the return is processed
}

export interface VoidReturnStockParams {
    returnInvoiceId: string
    adminId: string
    reason: string
}

// Removed StockReturnType enum - too complex for filtering

export enum StockReturnStatus {
    ALL = 'ALL',
    ACTIVE = 'ACTIVE',
    VOIDED = 'VOIDED'
}

export enum StockReturnSortOrder {
    NEWEST_FIRST = 'NEWEST_FIRST',
    OLDEST_FIRST = 'OLDEST_FIRST',
    AMOUNT_HIGH_TO_LOW = 'AMOUNT_HIGH_TO_LOW',
    AMOUNT_LOW_TO_HIGH = 'AMOUNT_LOW_TO_HIGH'
}

export interface GetStockReturnParams {
    startDate?: Date
    endDate?: Date
    productId?: string
    customerId?: string
    returnType?: StockReturnType
    status?: StockReturnStatus
    sortOrder?: StockReturnSortOrder
    search?: string
    page?: number
    limit?: number
}

export interface GetSaleInvoiceByNumberParams {
    invoiceNumber: string
    customerType: CustomerType
}

export interface StockReturnResponse {
    data: Array<{
        id: string
        invoiceNumber: string
        originalInvoiceNumber?: string
        date: Date
        status: string
        type?: string
        items: Array<{
            id: string
            quantity: number
            purchasePrice: number
            product: {
                name: string
                productId: string
            }
        }>
        customer?: {
            id: string
            name: string
            phoneNumber: string | null
        } | null
        customerType?: string
        createdBy: {
            name: string
        }
        voidingReason?: string
    }>
    total: number
    page: number
    totalPages: number
}
