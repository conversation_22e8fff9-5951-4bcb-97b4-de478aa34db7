// src/renderer/src/pages/banks/Banks.tsx
import { useState } from 'react'
import { Card, Space, Button, App, Divider } from 'antd'
import { PlusOutlined } from '@ant-design/icons'
import { useTheme } from '@/renderer/contexts'
import { BankList } from './components/BankList'
import { CreateBankDrawer } from './components/CreateBankDrawer'
import { BankDetailsModal } from './components/BankDetailsModal'

const Banks = () => {
  const { isDarkMode } = useTheme()
  const { message } = App.useApp()

  const [isCreateDrawerOpen, setIsCreateDrawerOpen] = useState(false)
  const [selectedBankId, setSelectedBankId] = useState<string | null>(null)
  const [refreshTrigger, setRefreshTrigger] = useState(0)

  const handleBankCreated = () => {
    setIsCreateDrawerOpen(false)
    setRefreshTrigger((prev) => prev + 1)
    message.success('Bank created successfully')
  }

  return (
    <Card
      className={`m-6 bg-[length:200%_200%] bg-[center] shadow-lg ${
        isDarkMode
          ? 'bg-gradient-to-bl from-indigo-950 via-black to-indigo-900 shadow-indigo-950'
          : 'bg-gradient-to-bl from-indigo-200 via-white to-indigo-100 shadow-indigo-200'
      }`}
    >
      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        <div className="flex justify-end">
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => setIsCreateDrawerOpen(true)}
          >
            Add Bank
          </Button>
        </div>

        <BankList
          onBankSelect={setSelectedBankId}
          refreshTrigger={refreshTrigger}
          setRefreshTrigger={setRefreshTrigger}
        />
      </Space>

      <CreateBankDrawer
        open={isCreateDrawerOpen}
        onClose={() => setIsCreateDrawerOpen(false)}
        onBankCreated={handleBankCreated}
      />

      <BankDetailsModal
        bankId={selectedBankId}
        open={!!selectedBankId}
        onClose={() => setSelectedBankId(null)}
      />
    </Card>
  )
}

export default Banks
