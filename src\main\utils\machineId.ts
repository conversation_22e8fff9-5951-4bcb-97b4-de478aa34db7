import { platform } from 'os';
import { execSync } from 'child_process';

function getWindowsCPUID(): string {
    try {
        // Get Windows-specific CPU ID using WMIC
        const output = execSync('wmic cpu get ProcessorId').toString();
        const lines = output.split('\n').filter(line => line.trim());
        // Second line contains the ID
        return lines[1]?.trim() || '';
    } catch (error) {
        console.error('Failed to get Windows CPU ID:', error);
        return '';
    }
}

function getUnixCPUID(): string {
    try {
        // Try to get CPU serial from /proc/cpuinfo on Linux
        const output = execSync('cat /proc/cpuinfo | grep Serial').toString();
        const serial = output.split(':')[1]?.trim() || '';
        return serial;
    } catch (error) {
        console.error('Failed to get Unix CPU ID:', error);
        return '';
    }
}

export function getMachineId(): string {
    let cpuId = '';

    // Get platform-specific CPU ID
    if (platform() === 'win32') {
        cpuId = getWindowsCPUID();
    } else {
        cpuId = getUnixCPUID();
    }

    if (!cpuId) {
        throw new Error('Could not retrieve CPU ID');
    }

    // Create a hash of the CPU ID
    // const hash = createHash('sha256');
    // hash.update(cpuId);

    // return hash.digest('hex');

    return cpuId;  // Return raw CPU ID without hashing
} 