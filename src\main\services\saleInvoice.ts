import { prisma } from '../db';
import { Prisma, PaymentType, Status, CustomerType } from '@prisma/client';
import { SaleItemData, GetSaleInvoicesParams, SaleInvoiceFormData } from '@/common/types';
import { processDateRange } from '../utils/helperFunctions';


class SaleInvoiceService {
    private async calculateSaleDetails(tx: Prisma.TransactionClient, items: SaleItemData[]) {
        let totalAmount = 0;
        let totalProfit = 0;
        const stockEntries: any[] = [];

        for (const item of items) {
            // Get available stocks sorted by purchase price (FIFO)
            const availableStocks = await tx.stock.findMany({
                where: {
                    productId: item.productId,
                    status: 'IN_STOCK',
                    quantity: { gt: 0 }
                },
                orderBy: { purchasePrice: 'asc' }
            });

            let remainingQuantity = item.totalQuantity;
            let itemProfit = 0;

            // Validate quantity is positive
            if (item.totalQuantity <= 0) {
                throw new Error(`Invalid quantity for product ${item.productId}: Quantity must be positive`);
            }

            // Validate sale price is not negative
            if (item.salePrice < 0) {
                throw new Error(`Invalid sale price for product ${item.productId}: Price cannot be negative`);
            }

            for (const stock of availableStocks) {
                if (remainingQuantity <= 0) break;

                const quantityToUse = Math.min(remainingQuantity, stock.quantity);
                const profit = (item.salePrice - stock.purchasePrice) * quantityToUse;

                stockEntries.push({
                    stockId: stock.id,
                    productId: item.productId,
                    quantityUsed: quantityToUse,
                    purchasePrice: stock.purchasePrice,
                    salePrice: item.salePrice,
                    profit,
                    remainingQuantity: stock.quantity - quantityToUse
                });

                itemProfit += profit;
                remainingQuantity -= quantityToUse;
            }

            if (remainingQuantity > 0) {
                throw new Error(`Insufficient stock for product ${item.productId}`);
            }

            totalAmount += item.salePrice * item.totalQuantity;
            totalProfit += itemProfit;
        }

        return { totalAmount, totalProfit, stockEntries };
    }

    async createWalkInSale(data: SaleInvoiceFormData) {
        return await prisma.$transaction(async (tx) => {
            // Validate inputs
            if (!data.items || data.items.length === 0) {
                throw new Error('No items provided for sale');
            }

            if (data.discountAmount < 0) {
                throw new Error('Discount amount cannot be negative');
            }

            if (data.paidAmount < 0) {
                throw new Error('Paid amount cannot be negative');
            }

            // Validate item quantities and prices
            for (const item of data.items) {
                if (item.totalQuantity <= 0) {
                    throw new Error(`Invalid quantity for product ${item.productId}: Quantity must be positive`);
                }
                if (item.salePrice < 0) {
                    throw new Error(`Invalid sale price for product ${item.productId}: Price cannot be negative`);
                }
            }

            // Calculate totals
            const { totalAmount, totalProfit, stockEntries } =
                await this.calculateSaleDetails(tx, data.items);

            const finalAmount = totalAmount - data.discountAmount;

            // Validate payment source has sufficient balance
            if (data.source === 'BANK' && !data.bankId) {
                throw new Error('Bank ID is required for bank payment');
            }

            // Create payment record first
            const payment = await tx.payments.create({
                data: {
                    date: data.date,
                    type: PaymentType.RECEIVED,
                    amount: finalAmount,
                    paymentMethod: data.paymentMethod ?? 'CASH',
                    referenceNumber: data.invoiceNumber,
                    destinationLocation: data.source,
                    locationId: data.source === 'BANK' ? data.bankId : null,
                    createdById: data.createdById,
                    status: Status.ACTIVE,
                    description: `Payment for: Walk-in Sale Invoice #${data.invoiceNumber}`
                }
            });

            // Create ledger entry for payment
            await tx.ledger.create({
                data: {
                    date: data.date,
                    amount: finalAmount,
                    creditOrDebit: 'CREDIT',
                    description: `Payment for: Walk-in Sale Invoice #${data.invoiceNumber}`,
                    referenceType: 'Payment',
                    paymentRef: payment.id,
                    status: 'ACTIVE',
                    bankId: data.source === 'BANK' ? data.bankId : null,
                    cashDestination: data.source,
                    createdById: data.createdById
                }
            });

            // Create walk-in sale invoice with items
            const invoice = await tx.walkInSaleInvoice.create({
                data: {
                    invoiceNumber: data.invoiceNumber,
                    customerType: 'WALK_IN',
                    customerName: data.customerName,
                    discountAmount: data.discountAmount,
                    totalAmount,
                    totalProfit,
                    paidAmount: finalAmount,
                    date: data.date,
                    paymentId: payment.id,
                    createdById: data.createdById,
                    items: {
                        create: data.items.map(item => ({
                            totalQuantity: item.totalQuantity,
                            salePrice: item.salePrice,
                            total: item.totalQuantity * item.salePrice,
                            totalProfit: stockEntries
                                .filter(entry => entry.productId === item.productId)
                                .reduce((sum, entry) => sum + entry.profit, 0),
                            productId: item.productId
                        }))
                    }
                },
                include: {
                    items: true,
                    payment: true,
                    createdBy: {
                        select: {
                            name: true
                        }
                    }
                }
            });

            // Create stock entries and update stock quantities
            for (const entry of stockEntries) {
                const walkInSaleItem = invoice.items.find(
                    item => item.productId === entry.productId
                );
                if (!walkInSaleItem) continue;

                // Create stock entry connected to WalkInSaleItem
                await tx.stockEntry.create({
                    data: {
                        quantityUsed: entry.quantityUsed,
                        purchasePrice: entry.purchasePrice,
                        salePrice: entry.salePrice,
                        profit: entry.profit,
                        stockId: entry.stockId,
                        walkInSaleItemId: walkInSaleItem.id
                    }
                });

                // Update stock quantity and status
                await tx.stock.update({
                    where: { id: entry.stockId },
                    data: {
                        quantity: { decrement: entry.quantityUsed },
                        status: entry.remainingQuantity <= 0 ? 'SOLD_OUT' : 'IN_STOCK'
                    }
                });

                // Update product's quantityInStock
                await tx.product.update({
                    where: { id: walkInSaleItem.productId },
                    data: {
                        quantityInStock: {
                            decrement: entry.quantityUsed
                        }
                    }
                });
            }

            // Update cash location balance based on payment source
            if (data.source === 'BANK' && !data.bankId) {
                throw new Error('Bank ID is required for bank payment');
            }

            switch (data.source) {
                case 'SMALL_COUNTER':
                    const smallCounter = await tx.smallCounter.findFirst();
                    if (!smallCounter) throw new Error('Small counter not found');
                    await tx.smallCounter.update({
                        where: { id: smallCounter.id },
                        data: { cashInShop: { increment: data.paidAmount } }
                    });
                    break;
                case 'CASH_VAULT':
                    const cashVault = await tx.cashVault.findFirst();
                    if (!cashVault) throw new Error('Cash vault not found');
                    await tx.cashVault.update({
                        where: { id: cashVault.id },
                        data: { balance: { increment: data.paidAmount } }
                    });
                    break;
                case 'BANK':
                    const bank = await tx.banks.findFirst({
                        where: { id: data.bankId }
                    });
                    if (!bank) throw new Error('Bank not found');
                    await tx.banks.update({
                        where: { id: bank.id },
                        data: { balance: { increment: data.paidAmount } }
                    });
                    break;
            }

            // Create ledger entry for sale - Fix: Add reference to the invoice
            await tx.ledger.create({
                data: {
                    date: data.date,
                    amount: finalAmount,
                    creditOrDebit: 'DEBIT',
                    description: `Walk-in Sale Invoice #${data.invoiceNumber}`,
                    referenceType: 'SaleInvoice',
                    // The schema doesn't have a walkInSaleRef field, so we can't directly link to the walk-in sale
                    // We'll use the payment reference instead since it's already linked to the walk-in sale
                    paymentRef: payment.id,
                    status: 'ACTIVE',
                    // bankId: data.source === 'BANK' ? data.bankId : undefined,
                    createdById: data.createdById
                }
            });

            return invoice;
        });
    }

    async createRegisteredSale(data: SaleInvoiceFormData) {
        return await prisma.$transaction(async (tx) => {
            // Validate inputs
            if (!data.items || data.items.length === 0) {
                throw new Error('No items provided for sale');
            }

            if (data.discountAmount < 0) {
                throw new Error('Discount amount cannot be negative');
            }

            if (data.paidAmount < 0) {
                throw new Error('Paid amount cannot be negative');
            }

            if (!data.customerId) {
                throw new Error('Customer ID is required');
            }

            // Validate item quantities and prices
            for (const item of data.items) {
                if (item.totalQuantity <= 0) {
                    throw new Error(`Invalid quantity for product ${item.productId}: Quantity must be positive`);
                }
                if (item.salePrice < 0) {
                    throw new Error(`Invalid sale price for product ${item.productId}: Price cannot be negative`);
                }
            }

            // Get customer's current balance
            const customer = await tx.party.findUnique({
                where: { id: data.customerId }
            });
            if (!customer) throw new Error('Customer not found');

            // Calculate totals
            const { totalAmount, totalProfit, stockEntries } =
                await this.calculateSaleDetails(tx, data.items);

            const finalAmount = totalAmount - data.discountAmount;
            const newBalance = customer.currentBalance - (finalAmount - data.paidAmount);

            // Create sale invoice
            const invoice = await tx.saleInvoice.create({
                data: {
                    invoiceNumber: data.invoiceNumber,
                    customerId: data.customerId,
                    discountAmount: data.discountAmount,
                    totalAmount,
                    totalProfit,
                    paidAmount: data.paidAmount,
                    previousBalance: customer.currentBalance,
                    newBalance,
                    date: data.date,
                    createdById: data.createdById,
                    items: {
                        create: data.items.map(item => ({
                            totalQuantity: item.totalQuantity,
                            salePrice: item.salePrice,
                            total: item.totalQuantity * item.salePrice,
                            totalProfit: stockEntries
                                .filter(entry => entry.productId === item.productId)
                                .reduce((sum, entry) => sum + entry.profit, 0),
                            productId: item.productId
                        }))
                    }
                },
                include: {
                    items: true
                }
            });

            // Create stock entries and update quantities
            for (const entry of stockEntries) {
                const saleItem = invoice.items.find(
                    item => item.productId === entry.productId
                );
                if (!saleItem) continue;

                await tx.stockEntry.create({
                    data: {
                        quantityUsed: entry.quantityUsed,
                        purchasePrice: entry.purchasePrice,
                        salePrice: entry.salePrice,
                        profit: entry.profit,
                        stock: {
                            connect: { id: entry.stockId }
                        },
                        saleItem: {
                            connect: { id: saleItem.id }
                        }
                    }
                });

                await tx.stock.update({
                    where: { id: entry.stockId },
                    data: {
                        quantity: { decrement: entry.quantityUsed },
                        status: entry.remainingQuantity <= 0 ? 'SOLD_OUT' : 'IN_STOCK'
                    }
                });

                // Update product's quantityInStock
                await tx.product.update({
                    where: { id: saleItem.productId },
                    data: {
                        quantityInStock: {
                            decrement: entry.quantityUsed
                        }
                    }
                });
            }

            // Update customer balance
            await tx.party.update({
                where: { id: data.customerId },
                data: { currentBalance: newBalance }
            });

            // Create ledger entry for sale
            await tx.ledger.create({
                data: {
                    date: data.date,
                    amount: finalAmount,
                    creditOrDebit: 'DEBIT',
                    description: `Sale Invoice #${data.invoiceNumber}`,
                    referenceType: 'SaleInvoice',
                    saleRef: invoice.id,
                    status: 'ACTIVE',
                    partyId: data.customerId,
                    createdById: data.createdById
                }
            });

            // Create payment if any amount is paid
            let paymentId: string | null = null;
            if (data.paidAmount > 0) {
                const payment = await tx.payments.create({
                    data: {
                        date: data.date,
                        type: PaymentType.RECEIVED,
                        amount: data.paidAmount,
                        paymentMethod: data.paymentMethod ?? 'CASH',
                        destinationLocation: data.source,
                        referenceNumber: data.invoiceNumber,
                        locationId: data.source === 'BANK' ? data.bankId : null,
                        partyId: data.customerId,
                        createdById: data.createdById,
                        status: Status.ACTIVE,
                        description: `Payment for: Sale Invoice #${data.invoiceNumber}`
                    }
                });
                paymentId = payment.id;

                // Update the sale invoice with the payment reference
                if (paymentId) {
                    await tx.saleInvoice.update({
                        where: { id: invoice.id },
                        data: { paymentId }
                    });
                }

                // Create ledger entry for payment
                await tx.ledger.create({
                    data: {
                        date: data.date,
                        amount: data.paidAmount,
                        creditOrDebit: 'CREDIT',
                        description: `Payment for: Sale Invoice #${data.invoiceNumber}`,
                        referenceType: 'Payment',
                        paymentRef: payment.id,
                        status: 'ACTIVE',
                        bankId: data.source === 'BANK' ? data.bankId : null,
                        partyId: data.customerId,
                        cashDestination: data.source,
                        createdById: data.createdById
                    }
                });

                // Update cash location balance
                if (data.paidAmount > 0 && data.source) {
                    switch (data.source) {
                        case 'SMALL_COUNTER':
                            const smallCounter = await tx.smallCounter.findFirst();
                            if (!smallCounter) throw new Error('Small counter not found');
                            await tx.smallCounter.update({
                                where: { id: smallCounter.id },
                                data: { cashInShop: { increment: data.paidAmount } }
                            });
                            break;
                        case 'CASH_VAULT':
                            const cashVault = await tx.cashVault.findFirst();
                            if (!cashVault) throw new Error('Cash vault not found');
                            await tx.cashVault.update({
                                where: { id: cashVault.id },
                                data: { balance: { increment: data.paidAmount } }
                            });
                            break;
                        case 'BANK':
                            if (!data.bankId) throw new Error('Bank ID is required for bank payment');
                            const bank = await tx.banks.findFirst({
                                where: { id: data.bankId }
                            });
                            if (!bank) throw new Error('Bank not found');
                            await tx.banks.update({
                                where: { id: bank.id },
                                data: { balance: { increment: data.paidAmount } }
                            });
                            break;
                    }
                }
            }

            return invoice;
        });
    }

    // Helper method to check for returns
    private async checkForReturns(invoiceNumber: string, tx: any) {
        const hasReturns = await tx.purchaseInvoice.findFirst({
            where: {
                // status: "ACTIVE",
                vendor: {
                    name: 'RETURN_STOCK'
                },
                invoiceNumber: {
                    startsWith: invoiceNumber
                }

            }
        });

        if (hasReturns) {
            throw new Error('Cannot void invoice: Some items have been returned');
        }
    }

    // Helper method to restore stock quantities
    private async restoreStockQuantities(items: any[], tx: any) {
        for (const item of items) {
            for (const entry of item.stockEntries) {
                // Restore stock quantity
                await tx.stock.update({
                    where: { id: entry.stockId },
                    data: {
                        quantity: { increment: entry.quantityUsed },
                        status: 'IN_STOCK'
                    }
                });

                // Update product quantity
                await tx.product.update({
                    where: { id: item.productId },
                    data: {
                        quantityInStock: { increment: entry.quantityUsed }
                    }
                });
            }
        }
    }

    // Helper method to restore cash balance and void payment
    private async restoreCashBalanceAndVoidPayment(payment: any, adminId: string, reason: string, tx: any) {
        if (!payment) return;

        const { amount, destinationLocation, locationId } = payment;

        // Restore cash balance based on payment source
        switch (destinationLocation) {
            case 'SMALL_COUNTER':
                await tx.smallCounter.updateMany({
                    data: { cashInShop: { decrement: amount } }
                });
                break;

            case 'CASH_VAULT':
                await tx.cashVault.updateMany({
                    data: { balance: { decrement: amount } }
                });
                break;

            case 'BANK':
                if (!locationId) throw new Error('Bank ID not found');
                await tx.banks.update({
                    where: { id: locationId },
                    data: { balance: { decrement: amount } }
                });
                break;
        }

        // Void payment
        await tx.payments.update({
            where: { id: payment.id },
            data: {
                status: 'VOID',
                voidedById: adminId,
                voidedAt: new Date(),
                voidingReason: reason
            }
        });

        // Void ledger entriy of the payment
        await tx.ledger.updateMany({
            where: { paymentRef: payment.id },
            data: { status: 'VOID' }
        });
    }

    async voidWalkInSale(id: string, adminId: string, reason: string) {
        return await prisma.$transaction(async (tx) => {
            // Get sale invoice with related data
            const invoice = await tx.walkInSaleInvoice.findUnique({
                where: { id },
                include: {
                    items: {
                        include: {
                            stockEntries: true,
                            product: true
                        }
                    },
                    payment: true
                }
            });

            if (!invoice) throw new Error('Sale invoice not found');
            if (invoice.status === 'VOID') throw new Error('Invoice already voided');

            // Check if any stock has been returned from this invoice
            await this.checkForReturns(invoice.invoiceNumber, tx);

            // Restore stock quantities
            await this.restoreStockQuantities(invoice.items, tx);

            // Restore cash balance and void payment
            await this.restoreCashBalanceAndVoidPayment(invoice.payment, adminId, reason, tx);

            // since we have not put a walkInSaleRef in the ledger we are using the payment ref to delete if
            // as there is a unique payment for each walk in sale
            // Void ledger entries
            await tx.ledger.updateMany({
                where: {
                    paymentRef: invoice.payment.id,
                    referenceType: 'SaleInvoice'
                },
                data: { status: 'VOID' }
            });

            // Update invoice status
            return await tx.walkInSaleInvoice.update({
                where: { id },
                data: {
                    status: 'VOID',
                    voidedById: adminId,
                    voidedAt: new Date(),
                    voidingReason: reason
                }
            });
        });
    }

    async voidRegisteredSale(id: string, adminId: string, reason: string) {
        return await prisma.$transaction(async (tx) => {
            // Get sale invoice with related data
            const invoice = await tx.saleInvoice.findUnique({
                where: { id },
                include: {
                    items: {
                        include: {
                            stockEntries: true,
                            product: true
                        }
                    },
                    customer: true,
                    payment: true
                }
            });

            if (!invoice) throw new Error('Sale invoice not found');
            if (invoice.status === 'VOID') throw new Error('Invoice already voided');

            // Check if any stock has been returned from this invoice
            await this.checkForReturns(invoice.invoiceNumber, tx);

            // Check if there are any later invoices for this customer
            const laterInvoice = await tx.saleInvoice.findFirst({
                where: {
                    customerId: invoice.customerId,
                    date: {
                        gt: invoice.date
                    },
                    status: 'ACTIVE'
                },
                orderBy: {
                    date: 'asc'
                }
            });

            if (laterInvoice) {
                throw new Error(`Cannot void invoice: There are more recent invoices for this customer. Please void invoice ${laterInvoice.invoiceNumber} first.`);
            }

            // Restore stock quantities
            await this.restoreStockQuantities(invoice.items, tx);

            // Restore customer balance
            const balanceAdjustment = invoice.totalAmount - invoice.discountAmount - invoice.paidAmount;
            await tx.party.update({
                where: { id: invoice.customerId },
                data: {
                    currentBalance: { increment: balanceAdjustment }
                }
            });

            // Void associated payment if exists
            if (invoice.payment) {
                await this.restoreCashBalanceAndVoidPayment(invoice.payment, adminId, reason, tx);
            }

            // Void ledger entries
            await tx.ledger.updateMany({
                where: { saleRef: id },
                data: { status: 'VOID' }
            });

            // Update invoice status
            return await tx.saleInvoice.update({
                where: { id },
                data: {
                    status: 'VOID',
                    voidedById: adminId,
                    voidedAt: new Date(),
                    voidingReason: reason
                }
            });
        });
    }

    async getSaleInvoices({
        page = 1,
        limit = 10,
        search,
        startDate,
        endDate,
        partyId,
        productId,
        status = 'ACTIVE',
        invoiceType = 'REGISTERED'
    }: GetSaleInvoicesParams) {
        // Build base where condition
        const where: any = {};

        // Add date range filter if provided
        const dateRange = processDateRange(startDate, endDate);
        if (dateRange) {
            where.date = dateRange;
        }

        // Add status filter
        if (status !== 'ALL') {
            where.status = status;
        }

        // Common pagination settings
        const skip = (page - 1) * limit;
        const take = limit;
        const orderBy = { date: 'desc' as const };

        if (invoiceType === 'REGISTERED') {
            // Build search condition for registered sales
            const searchCondition = search ? {
                OR: [
                    { invoiceNumber: { contains: search, mode: 'insensitive' } },
                    { customer: { name: { contains: search, mode: 'insensitive' } } }
                ]
            } : {};

            // Add customer filter if provided
            const customerFilter = partyId ? { customerId: partyId } : {};

            // Add product filter if provided
            const productFilter = productId ? {
                items: {
                    some: {
                        productId: productId
                    }
                }
            } : {};

            // Final where condition for registered sales
            const finalWhere = {
                ...where,
                ...searchCondition,
                ...customerFilter,
                ...productFilter
            };

            // Get registered sale invoices
            const [invoices, total] = await Promise.all([
                prisma.saleInvoice.findMany({
                    where: finalWhere,
                    orderBy,
                    skip,
                    take,
                    select: {
                        id: true,
                        invoiceNumber: true,
                        date: true,
                        totalAmount: true,
                        discountAmount: true,
                        paidAmount: true,
                        previousBalance: true,
                        newBalance: true,
                        totalProfit: true,
                        status: true,
                        createdAt: true,
                        voidedAt: true,
                        voidingReason: true,
                        customer: {
                            select: {
                                name: true,
                                type: true
                            }
                        },
                        items: {
                            select: {
                                totalQuantity: true,
                                salePrice: true,
                                total: true,
                                totalProfit: true,
                                product: {
                                    select: {
                                        name: true,
                                        productId: true,
                                        tag: true,
                                        nature: true,
                                        category: {
                                            select: {
                                                name: true
                                            }
                                        }
                                    }
                                }
                            }
                        },
                        createdBy: {
                            select: {
                                name: true,
                                id: true
                            }
                        },
                        voidedBy: {
                            select: {
                                name: true,
                                id: true
                            }
                        }
                    }
                }),
                prisma.saleInvoice.count({ where: finalWhere })
            ]);

            return {
                invoices,
                total,
                page,
                totalPages: Math.ceil(total / limit)
            };
        } else {
            // Build search condition for walk-in sales
            const searchCondition = search ? {
                OR: [
                    { invoiceNumber: { contains: search, mode: 'insensitive' } },
                    { customerName: { contains: search, mode: 'insensitive' } }
                ]
            } : {};

            // Add product filter if provided
            const productFilter = productId ? {
                items: {
                    some: {
                        productId: productId
                    }
                }
            } : {};

            // Final where condition for walk-in sales
            const finalWhere = {
                ...where,
                ...searchCondition,
                ...productFilter
            };

            // Get walk-in sale invoices
            const [invoices, total] = await Promise.all([
                prisma.walkInSaleInvoice.findMany({
                    where: finalWhere,
                    orderBy,
                    skip,
                    take,
                    select: {
                        id: true,
                        invoiceNumber: true,
                        customerName: true,
                        date: true,
                        totalAmount: true,
                        discountAmount: true,
                        paidAmount: true,
                        totalProfit: true,
                        status: true,
                        createdAt: true,
                        voidedAt: true,
                        voidingReason: true,
                        items: {
                            select: {
                                totalQuantity: true,
                                salePrice: true,
                                total: true,
                                totalProfit: true,
                                product: {
                                    select: {
                                        name: true,
                                        productId: true,
                                        tag: true,
                                        nature: true,
                                        category: {
                                            select: {
                                                name: true
                                            }
                                        }
                                    }
                                }
                            }
                        },
                        payment: {
                            select: {
                                paymentMethod: true
                            }
                        },
                        createdBy: {
                            select: {
                                name: true,
                                id: true
                            }
                        },
                        voidedBy: {
                            select: {
                                name: true,
                                id: true
                            }
                        }
                    }
                }),
                prisma.walkInSaleInvoice.count({ where: finalWhere })
            ]);

            return {
                invoices,
                total,
                page,
                totalPages: Math.ceil(total / limit)
            };
        }
    }
}

export const saleInvoiceService = new SaleInvoiceService();