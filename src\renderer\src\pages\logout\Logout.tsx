import { useEffect } from 'react'
import { useDispatch } from 'react-redux'
import { userActions } from '@/renderer/redux'
import { Navigate } from 'react-router-dom'
import { App_Routes } from '@/common/constants'

const Logout = () => {
  const dispatch = useDispatch()

  useEffect(() => {
    dispatch(userActions.logout())
  }, [dispatch])

  return <Navigate to={App_Routes.LOGIN} replace />
}

export default Logout
