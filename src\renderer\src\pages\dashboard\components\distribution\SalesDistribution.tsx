import { useEffect, useState } from 'react'
import { Typography, Space, Button, Alert, Skeleton, Radio, Row, Col, Statistic } from 'antd'
import { useApi } from '@/renderer/hooks'
import { dashboardApi } from '@/renderer/services'
import { SalesDistribution as SalesDistributionType, TimeRange } from '@/common/types/dashBoard'
import { MdPieChart, MdRefresh } from 'react-icons/md'
import { formatCurrency } from '@/renderer/utils'
import { Pie<PERSON>hart, Pie, Cell, ResponsiveContainer, Legend, Tooltip } from 'recharts'
import { useTheme } from '@/renderer/contexts'

const { Title } = Typography

const COLORS = ['#4F46E5', '#10B981']

const SalesDistribution = () => {
  const [timeRange, setTimeRange] = useState<TimeRange['days']>(7)
  const { isDarkMode } = useTheme()

  const {
    data,
    isLoading,
    error,
    request: fetchSalesDistribution
  } = useApi<SalesDistributionType, [TimeRange]>(dashboardApi.getSalesDistribution)

  useEffect(() => {
    fetchSalesDistribution({ days: timeRange })
  }, [])

  const handleTimeRangeChange = (days: TimeRange['days']) => {
    setTimeRange(days)
    fetchSalesDistribution({ days })
  }

  if (isLoading) {
    return (
      <div className="flex flex-col gap-4">
        <Space className="flex justify-between">
          <Space>
            <MdPieChart className="text-2xl text-indigo-600" />
            <Title level={5} className="!mb-0">
              Sales Distribution
            </Title>
          </Space>
          <Button icon={<MdRefresh />} loading />
        </Space>
        <Skeleton active paragraph={{ rows: 4 }} />
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex flex-col gap-4">
        <Space className="flex justify-between">
          <Space>
            <MdPieChart className="text-2xl text-indigo-600" />
            <Title level={5} className="!mb-0">
              Sales Distribution
            </Title>
          </Space>
          <Button
            icon={<MdRefresh />}
            onClick={() => fetchSalesDistribution({ days: timeRange })}
          />
        </Space>
        <Alert
          message="Error"
          description="Failed to load sales distribution data"
          type="error"
          showIcon
        />
      </div>
    )
  }

  if (!data) return null

  const totalAmount = data.walkIn.amount + data.registered.amount
  const totalCount = data.walkIn.count + data.registered.count

  const chartData = [
    { name: 'Walk-in', value: data.walkIn.amount },
    { name: 'Registered', value: data.registered.amount }
  ]

  return (
    <div className="flex flex-col gap-4">
      <Space className="flex justify-between">
        <Space>
          <MdPieChart className="text-2xl text-indigo-600" />
          <Title level={5} className="!mb-0">
            Sales Distribution
          </Title>
        </Space>
        <Space>
          <Radio.Group
            value={timeRange}
            onChange={(e) => handleTimeRangeChange(e.target.value)}
            optionType="button"
            buttonStyle="solid"
            size="small"
          >
            <Radio.Button value={1}>1d</Radio.Button>
            <Radio.Button value={7}>7d</Radio.Button>
            <Radio.Button value={14}>14d</Radio.Button>
            <Radio.Button value={30}>30d</Radio.Button>
          </Radio.Group>
          <Button
            icon={<MdRefresh />}
            onClick={() => fetchSalesDistribution({ days: timeRange })}
            size="small"
          />
        </Space>
      </Space>

      <Row gutter={[16, 16]}>
        <Col span={24}>
          <div className="h-[250px] w-full">
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={chartData}
                  cx="50%"
                  cy="50%"
                  innerRadius={60}
                  outerRadius={80}
                  paddingAngle={5}
                  dataKey="value"
                >
                  {chartData.map((_, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index]} />
                  ))}
                </Pie>
                <Tooltip
                  contentStyle={{
                    backgroundColor: isDarkMode ? '#1F2937' : '#FFFFFF',
                    border: `1px solid ${isDarkMode ? '#374151' : '#E5E7EB'}`,
                    borderRadius: '6px'
                  }}
                  formatter={(value: number) => formatCurrency(value)}
                />
                <Legend />
              </PieChart>
            </ResponsiveContainer>
          </div>
        </Col>

        <Col span={12}>
          <div
            className={`rounded-lg border shadow-md ${isDarkMode ? 'border-gray-800 bg-gray-900' : 'border-gray-200 bg-slate-50'} p-4`}
          >
            <span className="text-sm font-medium">Walk-in Sales</span>
            <Statistic
              value={data.walkIn.amount}
              formatter={(value) => formatCurrency(value as number)}
              valueStyle={{ fontSize: '1.25rem' }}
            />
            <div className="mt-2 text-xs text-gray-500">
              {data.walkIn.count} invoices ({((data.walkIn.count / totalCount) * 100).toFixed(1)}%)
            </div>
          </div>
        </Col>

        <Col span={12}>
          <div
            className={`rounded-lg border shadow-md ${isDarkMode ? 'border-gray-800 bg-gray-900' : 'border-gray-200 bg-slate-50'} p-4`}
          >
            <span className="text-sm font-medium">Registered Sales</span>
            <Statistic
              value={data.registered.amount}
              formatter={(value) => formatCurrency(value as number)}
              valueStyle={{ fontSize: '1.25rem' }}
            />
            <div className="mt-2 text-xs text-gray-500">
              {data.registered.count} invoices (
              {((data.registered.count / totalCount) * 100).toFixed(1)}%)
            </div>
          </div>
        </Col>
      </Row>
    </div>
  )
}

export default SalesDistribution
