import { prisma } from '../db';
import { Prisma, PartyType } from '@prisma/client';
import { CreatePartyData, GetPartiesParams, GetPartiesByBalanceParams, GetPartiesByTypeParams } from '../../common/types/party';


class PartyService {
    async createParty(data: CreatePartyData) {
        return await prisma.$transaction(async (tx) => {

            if (data.name === 'RETURN_STOCK' || data.name === 'OPENING_STOCK') {
                throw new Error('System reserved party names cannot be used');
            }

            const existingParty = await tx.party.findFirst({
                where: { name: data.name }
            });

            if (existingParty) {
                throw new Error('Party with this name already exists');
            }

            // Create the party
            const party = await tx.party.create({
                data: {
                    name: data.name,
                    type: data.type,
                    contact: data.contact,
                    address: data.address,
                    phoneNumber: data.phoneNumber,
                    currentBalance: data.openingBalance || 0
                }
            });

            // If there's an opening balance, create ledger entry
            if (data.openingBalance) {
                await tx.ledger.create({
                    data: {
                        partyId: party.id,
                        amount: Math.abs(data.openingBalance),
                        creditOrDebit: data.openingBalance > 0 ? 'CREDIT' : 'DEBIT',
                        description: `Opening Balance for ${party.name}`,
                        referenceType: 'OpeningBalance',
                        date: new Date(),
                        status: 'ACTIVE',
                        createdById: data.createdById
                    }
                });
            }

            return party;
        });
    }

    async getPartyById(id: string) {
        return await prisma.party.findUnique({
            where: { id },
            include: {
                _count: {
                    select: {
                        PurchaseInvoice: true,
                        SaleInvoice: true,
                        Payments: true
                    }
                }
            }
        });
    }

    async updateParty(id: string, data: Prisma.PartyUpdateInput) {
        return await prisma.party.update({
            where: { id },
            data
        });
    }

    async deleteParty(id: string) {
        try {
            // Check if party exists and get its relationships count
            const party = await prisma.party.findUnique({
                where: { id },
            });

            if (!party) {
                throw new Error('Party not found');
            }

            const [purchaseInvoice, saleInvoice, payments, ledger] = await Promise.all([
                prisma.purchaseInvoice.findFirst({ where: { vendorId: id, status: 'ACTIVE' } }),
                prisma.saleInvoice.findFirst({ where: { customerId: id, status: 'ACTIVE' } }),
                prisma.payments.findFirst({ where: { partyId: id, status: 'ACTIVE' } }),
                prisma.ledger.findFirst({ where: { partyId: id, NOT: { referenceType: 'OpeningBalance' }, status: 'ACTIVE' } })
            ]);

            // Check if party only has opening balance entry
            const hasOnlyOpeningBalance =
                purchaseInvoice === null &&
                saleInvoice === null &&
                payments === null &&
                ledger === null; // Only opening balance ledger entry

            if (hasOnlyOpeningBalance) {
                return await prisma.$transaction(async (tx) => {
                    // Delete ledger entries first
                    await tx.ledger.deleteMany({
                        where: {
                            partyId: id,
                            referenceType: 'OpeningBalance'
                        }
                    });

                    // Clean up all related records (including soft-deleted ones)
                    // to maintain referential integrity and prevent orphaned records

                    await tx.purchaseInvoice.deleteMany({
                        where: { vendorId: id }
                    });

                    await tx.saleInvoice.deleteMany({
                        where: { customerId: id }
                    });

                    await tx.payments.deleteMany({
                        where: { partyId: id }
                    });

                    // Finally delete the party
                    return await tx.party.delete({
                        where: { id }
                    });
                });
            }

            throw new Error('Cannot delete party with existing transactions');
        } catch (error) {
            if (error instanceof Prisma.PrismaClientKnownRequestError) {
                if (error.code === 'P2003') {
                    throw new Error('Cannot delete party with existing transactions');
                }
            }
            throw error;
        }
    }

    async getParties({ page, limit, search, type, where = {} }: GetPartiesParams) {
        const finalWhere: Prisma.PartyWhereInput = {
            ...where,
            name: {
                notIn: [
                    'RETURN_STOCK',
                    'OPENING_STOCK'
                ]
            },
            ...(search ? {
                OR: [
                    { name: { contains: search, mode: 'insensitive' } },
                    { phoneNumber: { contains: search, mode: 'insensitive' } }
                ]
            } : {}),
            ...(type ? { type } : {})
        };

        const [parties, total] = await Promise.all([
            prisma.party.findMany({
                where: finalWhere,
                skip: (page - 1) * limit,
                take: limit,
                orderBy: { createdAt: 'desc' }
            }),
            prisma.party.count({ where: finalWhere })
        ]);

        return { parties, total, page, totalPages: Math.ceil(total / limit) };
    }

    async getPartiesByBalance({ page, limit, balanceType }: GetPartiesByBalanceParams) {
        const where: Prisma.PartyWhereInput = {
            currentBalance: {
                ...(balanceType === 'positive' ? { gt: 0 } :
                    balanceType === 'negative' ? { lt: 0 } :
                        { equals: 0 })
            }
        };

        return await this.getParties({ page, limit, where });
    }

    async getPartiesForSelect(type?: PartyType) {
        const response = await prisma.party.findMany({
            where: type ? { type } : undefined,
            select: {
                id: true,
                name: true,
            },
            orderBy: { name: 'asc' }
        });

        // Filter out special parties before mapping to avoid null values in the result array
        return response
            .filter(party => party.name !== 'RETURN_STOCK' && party.name !== 'OPENING_STOCK')
            .map(party => ({
                value: party.id,
                label: party.name
            }));
    }

    async getPartiesByTypeForPDF({ type, search, sortBy = 'name', sortOrder = 'asc' }: GetPartiesByTypeParams) {
        try {
            // Build the where clause
            const where: Prisma.PartyWhereInput = {
                type,
                name: {
                    notIn: [
                        'RETURN_STOCK',
                        'OPENING_STOCK'
                    ]
                }
            };

            // Add search condition if provided
            if (search) {
                where.OR = [
                    { name: { contains: search, mode: 'insensitive' } },
                    { phoneNumber: { contains: search, mode: 'insensitive' } }
                ];
            }

            // Build the orderBy object based on sortBy and sortOrder
            const orderBy: any = {};
            if (sortBy === 'balance') {
                // For balance, we need to use the currentBalance field
                orderBy.currentBalance = sortOrder as Prisma.SortOrder;
            } else {
                // For other fields, use the field directly
                orderBy[sortBy] = sortOrder as Prisma.SortOrder;
            }

            // Fetch all parties with sorting (no pagination)
            const parties = await prisma.party.findMany({
                where,
                select: {
                    id: true,
                    name: true,
                    phoneNumber: true,
                    currentBalance: true
                },
                orderBy
            });

            // Calculate summary statistics
            const totalCount = parties.length;
            let totalPayables = 0;    // Positive balances (I owe them)
            let totalReceivables = 0; // Negative balances (They owe me)

            parties.forEach(party => {
                if (party.currentBalance > 0) {
                    // Positive balance means I owe them (payables)
                    totalPayables += party.currentBalance;
                } else if (party.currentBalance < 0) {
                    // Negative balance means they owe me (receivables)
                    totalReceivables += Math.abs(party.currentBalance);
                }
            });

            // Net balance is always receivables - payables
            // Negative net balance means overall I owe more than is owed to me
            // Positive net balance means overall more is owed to me than I owe
            const netBalance = totalReceivables - totalPayables;

            // Format the response
            const formattedParties = parties.map(party => ({
                id: party.id,
                name: party.name,
                phoneNumber: party.phoneNumber,
                balance: party.currentBalance
            }));

            return {
                parties: formattedParties,
                summary: {
                    totalCount,
                    totalPositiveBalance: totalPayables,     // Renamed but keeping interface compatibility
                    totalNegativeBalance: totalReceivables,  // Renamed but keeping interface compatibility
                    netBalance
                }
            };
        } catch (error) {
            throw error;
        }
    }
}

export const partyService = new PartyService();