import { Form, Input, Modal, Button, message, Space } from 'antd'
import { categoryApi } from '@/renderer/services'

interface CategoryModalProps {
  open: boolean
  onClose: () => void
  onCategoryCreated: () => void
}

export const CreateCategoryModal = ({ open, onClose, onCategoryCreated }: CategoryModalProps) => {
  const [form] = Form.useForm()

  const handleSubmit = async (values: any) => {
    const response = await categoryApi.createCategory(values)

    if (response.error.error || response.data.error) {
      message.error(response.error.message || response.data.error.message)
      return
    }

    message.success('Category created successfully')
    form.resetFields()
    onCategoryCreated()
    onClose()
  }

  return (
    <Modal
      title="Create New Category"
      open={open}
      onCancel={onClose}
      footer={
        <Space>
          <Button onClick={onClose}>Cancel</Button>
          <Button
            type="primary"
            onClick={() => form.submit()}
            className="bg-green-500 hover:!bg-green-600"
          >
            Create
          </Button>
        </Space>
      }
    >
      <Form form={form} layout="vertical" onFinish={handleSubmit}>
        <Form.Item
          name="name"
          label="Category Name"
          rules={[{ required: true, message: 'Please enter category name' }]}
        >
          <Input />
        </Form.Item>
      </Form>
    </Modal>
  )
}
