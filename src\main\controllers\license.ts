import { IRequest } from '@/common/types';
import { licenseService } from '../services';


class LicenseController {


    async getLicense(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const license = await licenseService.getLicense();
        return license;
    }

    async verifyLicense(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { encryptedLicense } = req.body ?? {};
        if (!encryptedLicense) throw new Error('Encrypted license is required');
        const license = await licenseService.verifyLicense(encryptedLicense);
        return license;
    }

}

export const licenseController = new LicenseController();
