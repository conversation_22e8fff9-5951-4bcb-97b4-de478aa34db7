import { http } from './http';
import { Channels } from '@/common/constants';
import { CreateUserData, UpdatePasswordData, ResetPasswordData, GetUsersParams, LoginData } from '@/common/types';

export const login = async (data: LoginData) => {
    return await http.post(Channels.LOGIN, { body: data });
};

export const createUser = async (data: CreateUserData) => {
    return await http.post(Channels.CREATE_USER, { body: data });
};

export const updatePassword = async (data: UpdatePasswordData) => {
    return await http.put(Channels.UPDATE_PASSWORD, { body: data });
};

export const resetPassword = async (data: ResetPasswordData) => {
    return await http.put(Channels.RESET_PASSWORD, { body: data });
};

export const getUsers = async (params: GetUsersParams) => {
    return await http.get(Channels.GET_USERS, { query: params });
};

export const deactivateUser = async (userId: string, adminId: string) => {
    return await http.put(Channels.DEACTIVATE_USER, {
        params: { userId },
        body: { adminId }
    });
};