export const processDateRange = (startDate?: Date, endDate?: Date) => {
    if (!startDate && !endDate) return undefined;

    if (startDate && endDate) {
        const start = new Date(startDate);
        const end = new Date(endDate);

        // If same date
        if (start.toDateString() === end.toDateString()) {
            start.setHours(0, 0, 0, 0);
            end.setHours(23, 59, 59, 999);
        } else {
            // Different dates
            start.setHours(0, 0, 0, 0);
            end.setHours(23, 59, 59, 999);
        }

        return {
            gte: start,
            lte: end
        };
    }

    // If only start date
    if (startDate) {
        const start = new Date(startDate);
        start.setHours(0, 0, 0, 0);
        return { gte: start };
    }

    // If only end date
    if (endDate) {
        const end = new Date(endDate);
        end.setHours(23, 59, 59, 999);
        return { lte: end };
    }

    return undefined;
}