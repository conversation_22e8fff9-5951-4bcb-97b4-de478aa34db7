import { AdminRole } from '@prisma/client'
import { Roles } from '../constants'

export type IObject = {
  [key: string | number]: any
}

export type IServerResponse = {
  data: null | any
  error: {
    message: string
    error: null | any
  }
}

export type IRoles = {
  ADMIN: 'admin'
  USER: 'user'
}

export type IMenu = {
  key: string
  label: string
  roles: `${Roles}`[]
  icon: React.ReactNode
}

type IRoleKeys = keyof IRoles

export type IUser = {
  id: string
  username: string
  name: string
  password?: string
  role: AdminRole
  isActive: boolean
  createdAt: string
  updatedAt: string
}

export type IRequest = {
  params?: IObject
  query?: IObject
  body?: IObject
}

export enum LedgerType {
  SALE = 'SALE',
  PURCHASE = 'PURCHASE',
  PAYMENT = 'PAYMENT',
  RECEIPT = 'RECEIPT',
  TRANSFER = 'TRANSFER',
  EXPENSE = 'EXPENSE',
  STOCK_RETURN = 'STOCK_RETURN'
}

export enum CreditDebit {
  CREDIT = 'CREDIT',
  DEBIT = 'DEBIT'
}

export enum Status {
  ACTIVE = 'ACTIVE',
  VOID = 'VOID'
}

export enum PartyType {
  CREDITOR = 'CREDITOR',
  VENDOR = 'VENDOR',
  CUSTOMER = 'CUSTOMER'
}

export * from './expense'
export * from './payments'
export * from './banks'
export * from './cashVault'
export * from './party'
export * from './saleInvoice'
export * from './bankLedger'
export * from './user'
export * from './license'
export * from './smallCounter'
export * from './ledger'
export * from './invoiceNumber'
export * from './stockReturn'
export * from './purchaseInvoice'
export * from './dashBoard'
export * from './product'
export * from './openingStock'
export * from './vendorReturn'
export * from './reports'
export * from './manualEntry'
export * from './accountTransfer'
