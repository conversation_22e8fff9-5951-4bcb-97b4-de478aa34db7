import { useEffect, useState } from 'react'
import { message, Tabs } from 'antd'
import { smallCounterApi } from '@/renderer/services'
import { SmallCounterTransaction, SmallCounterReconcileResponse } from '@/common/types'
import {
  InitializeDrawer,
  ReconcileDrawer,
  SmallCounterHeader,
  TransactionList,
  SmallCounterStatement
} from './components'
import { useSelector } from 'react-redux'
import { IRootState } from '@/renderer/redux'

const SmallCounter = () => {
  // State
  const [balance, setBalance] = useState(0)
  const [isInitialized, setIsInitialized] = useState(true)
  const [transactions, setTransactions] = useState<SmallCounterTransaction[]>([])
  const [loading, setLoading] = useState(false)
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0
  })
  const [dateRange, setDateRange] = useState<[Date | null, Date | null]>([null, null])
  const [includeDeleted, setIncludeDeleted] = useState(false)

  const user = useSelector((state: IRootState) => state.user.data)

  // Drawer states
  const [reconcileDrawerOpen, setReconcileDrawerOpen] = useState(false)
  const [reconcileLoading, setReconcileLoading] = useState(false)
  const [reconcileData, setReconcileData] = useState<SmallCounterReconcileResponse>()
  const [initializeDrawerOpen, setInitializeDrawerOpen] = useState(false)
  const [initializeLoading, setInitializeLoading] = useState(false)

  // Load data
  const loadBalance = async () => {
    const response = await smallCounterApi.getCurrentBalance()
    if (response.error.error || response.data.error) {
      // message.error(response.error.message || response.data.error.message)
      setIsInitialized(false)
      return
    }

    setBalance(response.data.data)
  }

  const loadTransactions = async () => {
    setLoading(true)
    const response = await smallCounterApi.getAllTransactions({
      page: pagination.current,
      pageSize: pagination.pageSize,
      startDate: dateRange[0] || undefined,
      endDate: dateRange[1] || undefined,
      includeDeleted
    })

    if (response.error.error || response.data.error) {
      message.error(response.error.message || response.data.error.message)
      return
    }

    setTransactions(response.data.data.transactions)
    setPagination((prev) => ({
      ...prev,
      total: response.data.data.pagination.total
    }))

    setLoading(false)
  }

  useEffect(() => {
    loadBalance()
  }, [])

  useEffect(() => {
    if (isInitialized) {
      loadTransactions()
    }
  }, [pagination.current, pagination.pageSize, dateRange, includeDeleted, isInitialized])

  // Handlers
  const handleReconcile = async () => {
    setReconcileLoading(true)
    const response = await smallCounterApi.reconcileSmallCounter()
    setReconcileLoading(false)

    if (response.error.error || response.data.error) {
      message.error(response.error.message || response.data.error.message)
      return
    }

    setReconcileData(response.data.data)
    setReconcileLoading(false)
  }

  const handleInitialize = async (amount: number) => {
    setInitializeLoading(true)
    const response = await smallCounterApi.initializeSmallCounter({
      amount,
      adminId: user?.id || ''
    })
    setInitializeLoading(false)

    if (response.error.error || response.data.error) {
      message.error(response.error.message || response.data.error.message)
      return
    }
    message.success('Small counter initialized successfully')
    setIsInitialized(true)
    loadBalance()
  }

  return (
    <div className="p-6">
      <SmallCounterHeader
        balance={balance}
        onReconcile={() => {
          setReconcileDrawerOpen(true)
          handleReconcile()
        }}
        onInitialize={() => setInitializeDrawerOpen(true)}
        isInitialized={isInitialized}
      />

      {isInitialized && (
        <Tabs defaultActiveKey="transactions">
          <Tabs.TabPane tab="Transactions" key="transactions">
            <TransactionList
              transactions={transactions}
              loading={loading}
              pagination={{
                current: pagination.current,
                pageSize: pagination.pageSize,
                total: pagination.total
              }}
              onPaginationChange={(page, pageSize) =>
                setPagination((prev) => ({ ...prev, current: page, pageSize }))
              }
              onDateRangeChange={setDateRange}
              onIncludeDeletedChange={setIncludeDeleted}
              includeDeleted={includeDeleted}
            />
          </Tabs.TabPane>
          <Tabs.TabPane tab="Statement" key="statement">
            <SmallCounterStatement />
          </Tabs.TabPane>
        </Tabs>
      )}

      <ReconcileDrawer
        open={reconcileDrawerOpen}
        onClose={() => {
          setReconcileDrawerOpen(false)
          setReconcileData(undefined)
        }}
        loading={reconcileLoading}
        data={reconcileData}
      />

      <InitializeDrawer
        open={initializeDrawerOpen}
        onClose={() => setInitializeDrawerOpen(false)}
        onInitialize={handleInitialize}
        loading={initializeLoading}
      />
    </div>
  )
}

export default SmallCounter
