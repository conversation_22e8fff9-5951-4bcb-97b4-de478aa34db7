import { InitializeVaultData, IRequest, VaultTransactionData } from '../../common';
import { cashVaultService } from '../services';

class CashVaultController {
    isInitialized = async (_event: Electron.IpcMainInvokeEvent, req: IRequest) => {
        const isInitialized = await cashVaultService.isInitialized();
        return isInitialized;
    };

    initializeVault = async (_event: Electron.IpcMainInvokeEvent, req: IRequest) => {
        const { amount, adminId } = req.body as InitializeVaultData;
        if (!amount || !adminId) {
            throw new Error('Amount and adminId are required');
        }

        await cashVaultService.initializeVault({ amount, adminId });
        return true;
    };

    getCurrentBalance = async (_event: Electron.IpcMainInvokeEvent, req: IRequest) => {
        const balance = await cashVaultService.getCurrentBalance();
        return balance;
    };

    depositToVault = async (_event: Electron.IpcMainInvokeEvent, req: IRequest) => {
        const { amount, adminId, description } = req.body as VaultTransactionData;
        if (!amount || !adminId || !description) {
            throw new Error('Amount, adminId and description are required');
        }

        await cashVaultService.depositToVault({ amount, adminId, description });
        return true;
    };

    withdrawFromVault = async (_event: Electron.IpcMainInvokeEvent, req: IRequest) => {
        const { amount, adminId, description } = req.body as VaultTransactionData;
        if (!amount || !adminId || !description) {
            throw new Error('Amount, adminId and description are required');
        }

        await cashVaultService.withdrawFromVault({ amount, adminId, description });
        return true;
    };

    getAllTransactions = async (_event: Electron.IpcMainInvokeEvent, req: IRequest) => {
        const { page, pageSize, startDate, endDate, includeDeleted } = req.query || {};
        const response = await cashVaultService.getAllTransactions({
            page: page ? parseInt(page as string) : undefined,
            pageSize: pageSize ? parseInt(pageSize as string) : undefined,
            startDate: startDate ? new Date(startDate as string) : undefined,
            endDate: endDate ? new Date(endDate as string) : undefined,
            includeDeleted: includeDeleted === 'true'
        });
        return response;
    };

    reconcileVault = async () => {
        return await cashVaultService.reconcileVault();
    };

    generateCashVaultStatement = async (_event: Electron.IpcMainInvokeEvent, req: IRequest) => {
        const { startDate, endDate, page, pageSize } = req.query || {};

        if (!startDate || !endDate) {
            throw new Error('Start date and end date are required');
        }

        const response = await cashVaultService.generateCashVaultStatement({
            startDate: new Date(startDate as string),
            endDate: new Date(endDate as string),
            page: page ? parseInt(page as string) : undefined,
            pageSize: pageSize ? parseInt(pageSize as string) : undefined
        });

        return response;
    };
}

export const cashVaultController = new CashVaultController();