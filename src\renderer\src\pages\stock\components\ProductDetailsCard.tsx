import { Card, Typography, Tag, Statistic, Tooltip, Badge } from 'antd'
import {
  BarcodeOutlined,
  TagsOutlined,
  BoxPlotOutlined,
  DollarOutlined,
  InfoCircleOutlined,
  WarningOutlined
} from '@ant-design/icons'
import { formatCurrency } from '@/renderer/utils'
import { useTheme } from '@/renderer/contexts'

const { Title, Text } = Typography

interface ProductDetailsCardProps {
  product: any
  visible: boolean
}

const ProductDetailsCard = ({ product, visible }: ProductDetailsCardProps) => {
  const { isDarkMode } = useTheme()

  const getStockLevelColor = () => {
    if (!product) return 'gray'
    return product.quantityInStock <= product.minStockLevel ? '#ff4d4f' : '#52c41a'
  }

  if (!product) {
    return null
  }

  return (
    <Card
      className={`mb-4 transition-all duration-500 ${
        visible ? 'mt-2 max-h-[250px] opacity-100' : 'mt-0 max-h-0 overflow-hidden opacity-0'
      } ${
        isDarkMode
          ? 'bg-gradient-to-br from-indigo-950 via-black to-gray-900 shadow-lg shadow-indigo-900/30'
          : 'bg-gradient-to-br from-violet-50 via-white to-indigo-50 shadow-md shadow-indigo-200/50'
      }`}
      bodyStyle={{ padding: '16px', height: '100%' }}
    >
      <div className="flex h-full flex-col gap-4 sm:flex-row">
        {/* Left section - Product Information */}
        <div className="min-w-0 flex-1">
          <div className="mb-3 flex items-start justify-between gap-2">
            <Title level={5} className="!mb-0 line-clamp-2 !leading-tight" title={product.name}>
              {product.name}
            </Title>
            <div className="mt-1 flex flex-wrap gap-1">
              {product.nature && (
                <Tag color="purple" className="m-0">
                  {product.nature}
                </Tag>
              )}
              {product.tag && (
                <Tag color="cyan" className="m-0">
                  {product.tag}
                </Tag>
              )}
            </div>
          </div>

          <div className="grid grid-cols-1 gap-2 md:grid-cols-2">
            <div className="flex items-center gap-2 text-sm">
              <BarcodeOutlined className={isDarkMode ? 'text-indigo-400' : 'text-indigo-500'} />
              <Text strong title={product.productId} className="truncate">
                {product.productId}
              </Text>
            </div>
            <div className="flex items-center gap-2 text-sm">
              <TagsOutlined className={isDarkMode ? 'text-indigo-400' : 'text-indigo-500'} />
              <Text title={product.category?.name} className="truncate">
                {product.category?.name || 'Uncategorized'}
              </Text>
            </div>
            <div className="flex items-center gap-2 text-sm md:col-span-2">
              <DollarOutlined className={isDarkMode ? 'text-indigo-400' : 'text-indigo-500'} />
              <Text strong className="text-sm">
                Sale Price: {formatCurrency(product.salePrice || 0)}
              </Text>
            </div>
          </div>
        </div>

        {/* Right section - Stock Information */}
        <div className="mt-2 flex flex-row items-center justify-between gap-2 border-t pt-2 sm:mt-0 sm:flex-col sm:justify-center sm:border-l sm:border-t-0 sm:pl-4 sm:pt-0">
          <div className="flex flex-col items-center">
            <Text type="secondary" className="mb-1 text-xs">
              Stock Level
            </Text>
            <Badge
              count={
                product.quantityInStock <= product.minStockLevel ? (
                  <Tooltip title="Below minimum stock level!">
                    <WarningOutlined className="text-red-500" />
                  </Tooltip>
                ) : null
              }
              offset={[-5, 0]}
            >
              <Statistic
                value={product.quantityInStock || 0}
                valueStyle={{
                  color: getStockLevelColor(),
                  fontSize: '1.5rem',
                  fontWeight: 'bold',
                  margin: 0,
                  lineHeight: 1
                }}
              />
            </Badge>
          </div>

          <div className="flex flex-col items-center sm:mt-2">
            <Text type="secondary" className="mb-1 text-xs">
              Min Stock
            </Text>
            <Text strong className="text-lg">
              {product.minStockLevel}
            </Text>
          </div>
        </div>
      </div>
    </Card>
  )
}

export default ProductDetailsCard
