/*
  Warnings:

  - You are about to drop the column `customerName` on the `SaleInvoice` table. All the data in the column will be lost.
  - Added the required column `discountAmount` to the `SaleInvoice` table without a default value. This is not possible if the table is not empty.
  - Made the column `customerId` on table `SaleInvoice` required. This step will fail if there are existing NULL values in that column.

*/
-- CreateEnum
CREATE TYPE "StockStatus" AS ENUM ('IN_STOCK', 'SOLD_OUT');

-- CreateEnum
CREATE TYPE "InvoiceStatus" AS ENUM ('RESERVED', 'CONFIRMED', 'CANCELED', 'AVAILABLE');

-- CreateEnum
CREATE TYPE "BankTransactionType" AS ENUM ('PAYMENT', 'TRANSFER', 'DEPOSIT', 'WITHDRAWAL');

-- CreateEnum
CREATE TYPE "CashTransactionType" AS ENUM ('PAYMENT', 'DEPOSIT', 'WITHDRAWAL', 'DAILY_CLOSING');

-- DropForeignKey
ALTER TABLE "SaleInvoice" DROP CONSTRAINT "SaleInvoice_customerId_fkey";

-- AlterTable
ALTER TABLE "Expense" ADD COLUMN     "voidingReason" TEXT;

-- AlterTable
ALTER TABLE "Payments" ADD COLUMN     "voidingReason" TEXT;

-- AlterTable
ALTER TABLE "Product" ADD COLUMN     "quantityInStock" DOUBLE PRECISION NOT NULL DEFAULT 0;

-- AlterTable
ALTER TABLE "PurchaseInvoice" ADD COLUMN     "voidingReason" TEXT;

-- AlterTable
ALTER TABLE "SaleInvoice" DROP COLUMN "customerName",
ADD COLUMN     "discountAmount" DOUBLE PRECISION NOT NULL,
ADD COLUMN     "voidingReason" TEXT,
ALTER COLUMN "customerType" SET DEFAULT 'REGISTERED',
ALTER COLUMN "previousBalance" DROP DEFAULT,
ALTER COLUMN "newBalance" DROP DEFAULT,
ALTER COLUMN "customerId" SET NOT NULL;

-- AlterTable
ALTER TABLE "Stock" ADD COLUMN     "status" "StockStatus" NOT NULL DEFAULT 'IN_STOCK';

-- AlterTable
ALTER TABLE "StockEntry" ADD COLUMN     "walkInSaleItemId" TEXT;

-- CreateTable
CREATE TABLE "InvoiceNumber" (
    "id" TEXT NOT NULL,
    "number" INTEGER NOT NULL,
    "invoiceNumber" TEXT NOT NULL,
    "type" "CustomerType" NOT NULL,
    "year" INTEGER NOT NULL,
    "status" "InvoiceStatus" NOT NULL DEFAULT 'RESERVED',
    "issuedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "confirmedAt" TIMESTAMP(3),
    "canceledAt" TIMESTAMP(3),
    "expiresAt" TIMESTAMP(3) NOT NULL,
    "issuedById" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "InvoiceNumber_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "WalkInSaleInvoice" (
    "id" TEXT NOT NULL,
    "invoiceNumber" TEXT NOT NULL,
    "customerType" "CustomerType" NOT NULL DEFAULT 'WALK_IN',
    "customerName" TEXT,
    "discountAmount" DOUBLE PRECISION NOT NULL,
    "totalAmount" DOUBLE PRECISION NOT NULL,
    "totalProfit" DOUBLE PRECISION NOT NULL,
    "paidAmount" DOUBLE PRECISION NOT NULL,
    "status" "Status" NOT NULL DEFAULT 'ACTIVE',
    "date" TIMESTAMP(3) NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "paymentId" TEXT NOT NULL,
    "createdById" TEXT NOT NULL,
    "voidedById" TEXT,
    "voidedAt" TIMESTAMP(3),
    "voidingReason" TEXT,

    CONSTRAINT "WalkInSaleInvoice_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "WalkInSaleItem" (
    "id" TEXT NOT NULL,
    "totalQuantity" DOUBLE PRECISION NOT NULL,
    "salePrice" DOUBLE PRECISION NOT NULL,
    "totalProfit" DOUBLE PRECISION NOT NULL,
    "total" DOUBLE PRECISION NOT NULL,
    "saleInvoiceId" TEXT NOT NULL,
    "productId" TEXT NOT NULL,

    CONSTRAINT "WalkInSaleItem_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "BankLedger" (
    "id" TEXT NOT NULL,
    "date" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "amount" DOUBLE PRECISION NOT NULL,
    "description" TEXT NOT NULL,
    "creditOrDebit" "CreditDebit" NOT NULL,
    "status" "Status" NOT NULL DEFAULT 'ACTIVE',
    "referenceType" "BankTransactionType" NOT NULL,
    "bankId" TEXT NOT NULL,
    "paymentRef" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "BankLedger_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "CashVaultLedger" (
    "id" TEXT NOT NULL,
    "date" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "amount" DOUBLE PRECISION NOT NULL,
    "description" TEXT NOT NULL,
    "creditOrDebit" "CreditDebit" NOT NULL,
    "status" "Status" NOT NULL DEFAULT 'ACTIVE',
    "referenceType" "CashTransactionType" NOT NULL,
    "paymentRef" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "CashVaultLedger_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "BankDailyBalance" (
    "id" TEXT NOT NULL,
    "date" TIMESTAMP(3) NOT NULL,
    "balance" DOUBLE PRECISION NOT NULL,
    "bankId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "BankDailyBalance_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "CashVault" (
    "id" TEXT NOT NULL,
    "balance" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "CashVault_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "InvoiceNumber_invoiceNumber_key" ON "InvoiceNumber"("invoiceNumber");

-- CreateIndex
CREATE INDEX "InvoiceNumber_status_expiresAt_idx" ON "InvoiceNumber"("status", "expiresAt");

-- CreateIndex
CREATE INDEX "InvoiceNumber_type_year_idx" ON "InvoiceNumber"("type", "year");

-- CreateIndex
CREATE UNIQUE INDEX "InvoiceNumber_type_number_year_key" ON "InvoiceNumber"("type", "number", "year");

-- CreateIndex
CREATE UNIQUE INDEX "WalkInSaleInvoice_invoiceNumber_key" ON "WalkInSaleInvoice"("invoiceNumber");

-- CreateIndex
CREATE UNIQUE INDEX "WalkInSaleInvoice_paymentId_key" ON "WalkInSaleInvoice"("paymentId");

-- CreateIndex
CREATE INDEX "WalkInSaleInvoice_customerType_idx" ON "WalkInSaleInvoice"("customerType");

-- CreateIndex
CREATE UNIQUE INDEX "BankLedger_paymentRef_key" ON "BankLedger"("paymentRef");

-- CreateIndex
CREATE INDEX "BankLedger_bankId_idx" ON "BankLedger"("bankId");

-- CreateIndex
CREATE INDEX "BankLedger_paymentRef_idx" ON "BankLedger"("paymentRef");

-- CreateIndex
CREATE UNIQUE INDEX "CashVaultLedger_paymentRef_key" ON "CashVaultLedger"("paymentRef");

-- CreateIndex
CREATE INDEX "CashVaultLedger_paymentRef_idx" ON "CashVaultLedger"("paymentRef");

-- CreateIndex
CREATE UNIQUE INDEX "BankDailyBalance_date_key" ON "BankDailyBalance"("date");

-- CreateIndex
CREATE INDEX "BankDailyBalance_bankId_date_idx" ON "BankDailyBalance"("bankId", "date");

-- AddForeignKey
ALTER TABLE "InvoiceNumber" ADD CONSTRAINT "InvoiceNumber_issuedById_fkey" FOREIGN KEY ("issuedById") REFERENCES "Admin"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "SaleInvoice" ADD CONSTRAINT "SaleInvoice_customerId_fkey" FOREIGN KEY ("customerId") REFERENCES "Party"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "WalkInSaleInvoice" ADD CONSTRAINT "WalkInSaleInvoice_paymentId_fkey" FOREIGN KEY ("paymentId") REFERENCES "Payments"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "WalkInSaleInvoice" ADD CONSTRAINT "WalkInSaleInvoice_createdById_fkey" FOREIGN KEY ("createdById") REFERENCES "Admin"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "WalkInSaleInvoice" ADD CONSTRAINT "WalkInSaleInvoice_voidedById_fkey" FOREIGN KEY ("voidedById") REFERENCES "Admin"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "WalkInSaleItem" ADD CONSTRAINT "WalkInSaleItem_saleInvoiceId_fkey" FOREIGN KEY ("saleInvoiceId") REFERENCES "WalkInSaleInvoice"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "WalkInSaleItem" ADD CONSTRAINT "WalkInSaleItem_productId_fkey" FOREIGN KEY ("productId") REFERENCES "Product"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "StockEntry" ADD CONSTRAINT "StockEntry_walkInSaleItemId_fkey" FOREIGN KEY ("walkInSaleItemId") REFERENCES "WalkInSaleItem"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BankLedger" ADD CONSTRAINT "BankLedger_bankId_fkey" FOREIGN KEY ("bankId") REFERENCES "Banks"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BankLedger" ADD CONSTRAINT "BankLedger_paymentRef_fkey" FOREIGN KEY ("paymentRef") REFERENCES "Payments"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CashVaultLedger" ADD CONSTRAINT "CashVaultLedger_paymentRef_fkey" FOREIGN KEY ("paymentRef") REFERENCES "Payments"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BankDailyBalance" ADD CONSTRAINT "BankDailyBalance_bankId_fkey" FOREIGN KEY ("bankId") REFERENCES "Banks"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
