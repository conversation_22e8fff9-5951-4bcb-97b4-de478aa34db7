import { useState, useEffect } from 'react'
import { Modal, Form, Input, Button, Spin, Row, Col, Card, Statistic, message } from 'antd'
import { partyApi } from '@/renderer/services'

interface CustomerDetailsModalProps {
  customerId: string | null
  open: boolean
  onClose: () => void
}

interface CustomerDetails {
  id: string
  name: string
  contact?: string
  address?: string
  phoneNumber?: string
  currentBalance: number
  _count: {
    SaleInvoice: number
    Payments: number
  }
}

export const CustomerDetailsModal = ({ customerId, open, onClose }: CustomerDetailsModalProps) => {
  const [form] = Form.useForm()
  const [editing, setEditing] = useState(false)
  const [loading, setLoading] = useState(false)
  const [customerDetails, setCustomerDetails] = useState<CustomerDetails | null>(null)

  useEffect(() => {
    if (customerId && open) {
      fetchCustomerDetails()
    }
  }, [customerId, open])

  const fetchCustomerDetails = async () => {
    if (!customerId) return
    setLoading(true)
    try {
      const response = await partyApi.getParty(customerId)
      setCustomerDetails(response.data.data)
      form.setFieldsValue(response.data.data)
    } catch (error) {
      message.error('Failed to fetch customer details')
    } finally {
      setLoading(false)
    }
  }

  const handleSubmit = async (values: any) => {
    if (!customerId) return
    try {
      await partyApi.updateParty(customerId, values)
      message.success('Customer updated successfully')
      setEditing(false)
      fetchCustomerDetails()
    } catch (error) {
      message.error('Failed to update customer')
    }
  }

  return (
    <Modal
      title="Customer Details"
      open={open}
      onCancel={onClose}
      width={800}
      footer={
        editing
          ? [
              <Button key="cancel" onClick={() => setEditing(false)}>
                Cancel
              </Button>,
              <Button key="submit" type="primary" onClick={() => form.submit()}>
                Save Changes
              </Button>
            ]
          : [
              <Button key="edit" type="primary" onClick={() => setEditing(true)}>
                Edit
              </Button>
            ]
      }
    >
      {loading ? (
        <div className="loading-container">
          <Spin />
        </div>
      ) : (
        <>
          <Row gutter={[16, 16]}>
            <Col span={8}>
              <Card>
                <Statistic
                  title="Current Balance"
                  value={customerDetails?.currentBalance || 0}
                  precision={2}
                  prefix="Rs."
                />
              </Card>
            </Col>
            <Col span={8}>
              <Card>
                <Statistic title="Total Sales" value={customerDetails?._count.SaleInvoice || 0} />
              </Card>
            </Col>
            <Col span={8}>
              <Card>
                <Statistic title="Total Payments" value={customerDetails?._count.Payments || 0} />
              </Card>
            </Col>
          </Row>

          <Form
            form={form}
            layout="vertical"
            onFinish={handleSubmit}
            disabled={!editing}
            style={{ marginTop: '24px' }}
          >
            <Form.Item
              name="name"
              label="Customer Name"
              rules={[{ required: true, message: 'Please enter customer name' }]}
            >
              <Input />
            </Form.Item>

            <Form.Item name="contact" label="Contact Person">
              <Input />
            </Form.Item>

            <Form.Item name="phoneNumber" label="Phone Number">
              <Input />
            </Form.Item>

            <Form.Item name="address" label="Address">
              <Input.TextArea rows={4} />
            </Form.Item>
          </Form>
        </>
      )}
    </Modal>
  )
}
