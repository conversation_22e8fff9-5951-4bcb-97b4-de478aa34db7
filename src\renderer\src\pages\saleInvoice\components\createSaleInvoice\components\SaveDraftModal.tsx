import { useState } from 'react'
import { Modal, Form, Input, Space, Button } from 'antd'

interface Props {
  open: boolean
  onSave: (name: string) => void
  onCancel: () => void
  loading?: boolean
}

export const SaveDraftModal = ({ open, onSave, onCancel, loading }: Props) => {
  const [form] = Form.useForm()

  const handleSubmit = () => {
    form.validateFields().then((values) => {
      onSave(values.name)
      form.resetFields()
    })
  }

  return (
    <Modal
      title="Save Draft Invoice"
      open={open}
      onCancel={onCancel}
      footer={
        <Space>
          <Button onClick={onCancel}>Cancel</Button>
          <Button
            type="primary"
            onClick={handleSubmit}
            loading={loading}
            className="bg-green-500 hover:!bg-green-600"
          >
            Save
          </Button>
        </Space>
      }
    >
      <Form form={form} layout="vertical">
        <Form.Item
          name="name"
          label="Draft Name"
          rules={[
            { required: true, message: 'Please enter a name for the draft' },
            { min: 3, message: 'Name must be at least 3 characters' },
            { max: 50, message: 'Name cannot be longer than 50 characters' },
            {
              pattern: /^[a-zA-Z0-9-_ ]+$/,
              message: 'Name can only contain letters, numbers, spaces, hyphens and underscores'
            }
          ]}
        >
          <Input placeholder="Enter a name for this draft" />
        </Form.Item>
      </Form>
    </Modal>
  )
}
