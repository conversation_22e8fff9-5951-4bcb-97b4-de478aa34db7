import { useEffect } from 'react'
import { <PERSON><PERSON>, Card, Col, Row, Table, Spin } from 'antd'
import { useTheme } from '@/renderer/contexts'
import { useApi } from '@/renderer/hooks'
import { reportsApi } from '@/renderer/services'
import { DailyOperationsReport, ReportFormat } from '@/common/types'
import { PieChart, Pie, Cell, ResponsiveContainer, Tooltip } from 'recharts'

interface Props {
  format: ReportFormat
  shouldGenerate: boolean
  onGenerateComplete: () => void
  currentReportId: string
  dateRange?: [Date, Date]
}

const DailyOperations = ({
  format,
  shouldGenerate,
  onGenerateComplete,
  currentReportId,
  dateRange
}: Props) => {
  const { isDarkMode } = useTheme()
  const { request, data, isLoading, errorMessage } = useApi<
    DailyOperationsReport,
    [{ format: ReportFormat; startDate?: Date; endDate?: Date }]
  >(reportsApi.generateDailyOperations)

  useEffect(() => {
    if (shouldGenerate && currentReportId === 'daily-operations') {
      request({
        format,
        startDate: dateRange?.[0],
        endDate: dateRange?.[1]
      }).finally(() => {
        onGenerateComplete()
      })
    }
  }, [shouldGenerate])

  if (isLoading) {
    return <Spin size="large" className="flex w-full justify-center p-8" />
  }

  if (errorMessage) {
    return <Alert type="error" message={errorMessage} className="mb-4" />
  }

  if (!data) return null

  const COLORS = ['#6366f1', '#8b5cf6', '#d946ef', '#f43f5e', '#f97316']

  return (
    <div className="space-y-6">
      {/* Summary Cards */}
      <Row gutter={[16, 16]}>
        <Col span={6}>
          <Card className={isDarkMode ? 'bg-black' : 'bg-white'}>
            <div className="text-center">
              <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Total Sales
              </p>
              <h2 className={`text-2xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-800'}`}>
                ₹{data.summary.totalSales.toLocaleString()}
              </h2>
            </div>
          </Card>
        </Col>
        <Col span={6}>
          <Card className={isDarkMode ? 'bg-black' : 'bg-white'}>
            <div className="text-center">
              <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Total Purchases
              </p>
              <h2 className={`text-2xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-800'}`}>
                ₹{data.summary.totalPurchases.toLocaleString()}
              </h2>
            </div>
          </Card>
        </Col>
        <Col span={6}>
          <Card className={isDarkMode ? 'bg-black' : 'bg-white'}>
            <div className="text-center">
              <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Total Expenses
              </p>
              <h2 className={`text-2xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-800'}`}>
                ₹{data.summary.totalExpenses.toLocaleString()}
              </h2>
            </div>
          </Card>
        </Col>
        <Col span={6}>
          <Card className={isDarkMode ? 'bg-black' : 'bg-white'}>
            <div className="text-center">
              <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Net Cash Flow
              </p>
              <h2 className={`text-2xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-800'}`}>
                ₹{data.summary.netCashFlow.toLocaleString()}
              </h2>
            </div>
          </Card>
        </Col>
      </Row>

      {/* Cash Movements Chart */}
      <Card title="Cash Movements by Location" className={isDarkMode ? 'bg-black' : 'bg-white'}>
        <div style={{ height: 400 }}>
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie
                data={data.cashMovements}
                dataKey="netFlow"
                nameKey="location"
                cx="50%"
                cy="50%"
                outerRadius={120}
                label={({ location, netFlow }) => `${location} (₹${netFlow.toLocaleString()})`}
              >
                {data.cashMovements.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                ))}
              </Pie>
              <Tooltip
                formatter={(value) => `₹${Number(value).toLocaleString()}`}
                labelFormatter={(label) => `Location: ${label}`}
              />
            </PieChart>
          </ResponsiveContainer>
        </div>
      </Card>

      {/* Transactions Table */}
      <Card title="Daily Transactions" className={isDarkMode ? 'bg-black' : 'bg-white'}>
        <Table
          dataSource={data.transactions}
          columns={[
            {
              title: 'Time',
              dataIndex: 'time',
              key: 'time',
              render: (time) => new Date(time).toLocaleTimeString()
            },
            {
              title: 'Type',
              dataIndex: 'type',
              key: 'type'
            },
            {
              title: 'Description',
              dataIndex: 'description',
              key: 'description'
            },
            {
              title: 'Amount',
              dataIndex: 'amount',
              key: 'amount',
              render: (value) => `₹${value.toLocaleString()}`
            },
            {
              title: 'Status',
              dataIndex: 'status',
              key: 'status'
            }
          ]}
          pagination={false}
          scroll={{ x: true }}
        />
      </Card>
    </div>
  )
}

export default DailyOperations
