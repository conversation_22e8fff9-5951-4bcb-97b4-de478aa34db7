import { Table, Modal, Space, Input, message, Tag, Descriptions, Tooltip, Button } from 'antd'
import { DeleteConfirmPopover } from './DeleteConfirmPopover'
import { formatCurrency, formatDate } from '@/renderer/utils'
import { useState } from 'react'
import { purchaseInvoiceApi } from '@/renderer/services'
import type { ColumnsType } from 'antd/es/table'
import { IRootState } from '@/renderer/redux'
import { useSelector } from 'react-redux'
import { PurchaseInvoiceData } from '@/common/types/purchaseInvoice'
import {
  BarcodeOutlined,
  TagsOutlined,
  BoxPlotOutlined,
  InfoCircleOutlined,
  EyeOutlined,
  DeleteOutlined
} from '@ant-design/icons'
import PurchaseInvoiceDetailsModal from './PurchaseInvoiceDetailsModal'

interface PurchaseInvoiceListProps {
  data: PurchaseInvoiceData[] | undefined
  loading: boolean
  total: number | undefined
  pagination: {
    current: number
    pageSize: number
  }
  onTableChange: (pagination: any) => void
  onRefresh: () => void
}

export const PurchaseInvoiceList = ({
  data,
  loading,
  total,
  pagination,
  onTableChange,
  onRefresh
}: PurchaseInvoiceListProps) => {
  const [voidReason, setVoidReason] = useState('')
  const [selectedInvoice, setSelectedInvoice] = useState<PurchaseInvoiceData | null>(null)
  const [isVoidModalVisible, setIsVoidModalVisible] = useState(false)
  const [isDetailsModalVisible, setIsDetailsModalVisible] = useState(false)

  const user = useSelector((state: IRootState) => state.user.data)

  const handleVoidInvoice = async () => {
    if (!selectedInvoice || !voidReason.trim()) {
      message.error('Please enter the reason for voiding this Purchase')
      return
    }

    const response = await purchaseInvoiceApi.voidPurchaseInvoice({
      id: selectedInvoice.id,
      adminId: user?.id || '',
      reason: voidReason
    })

    if (response.error.error || response.data.error) {
      message.error(response.error.message || response.data.error.message)
      return
    }

    message.success('Invoice voided successfully')
    setIsVoidModalVisible(false)
    setSelectedInvoice(null)
    setVoidReason('')
    onRefresh()
  }

  const columns: ColumnsType<PurchaseInvoiceData> = [
    {
      title: 'Sr. No.',
      dataIndex: 'index',
      key: 'index',
      render: (_, __, index) => (pagination.current - 1) * pagination.pageSize + index + 1
    },
    {
      title: 'Invoice #',
      dataIndex: 'invoiceNumber',
      key: 'invoiceNumber'
    },
    {
      title: 'Date',
      dataIndex: 'date',
      key: 'date',
      render: (date: string) => formatDate(date)
    },
    {
      title: 'Vendor',
      dataIndex: ['vendor', 'name'],
      key: 'vendorName'
    },
    {
      title: 'Total Amount',
      dataIndex: 'totalAmount',
      key: 'totalAmount',
      align: 'right',
      render: (amount: number) => formatCurrency(amount)
    },
    {
      title: 'Paid Amount',
      dataIndex: 'paidAmount',
      key: 'paidAmount',
      align: 'right',
      render: (amount: number) => formatCurrency(amount)
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status: string, record: PurchaseInvoiceData) => (
        <Tooltip title={status === 'VOID' ? record.voidingReason : null}>
          <Tag color={status === 'ACTIVE' ? 'green' : 'red'}>{status}</Tag>
        </Tooltip>
      )
    },
    {
      title: 'Actions',
      key: 'actions',
      width: 120,
      render: (_, record) => (
        <Space>
          <Button
            type="text"
            icon={<EyeOutlined />}
            onClick={() => {
              setSelectedInvoice(record)
              setIsDetailsModalVisible(true)
            }}
          />
          {record.status === 'ACTIVE' && (
            <Button
              type="text"
              danger
              icon={<DeleteOutlined />}
              onClick={() => {
                setSelectedInvoice(record)
                setIsVoidModalVisible(true)
              }}
            />
          )}
        </Space>
      )
    }
  ]

  const InvoiceDetails = () =>
    selectedInvoice && (
      <Descriptions bordered column={2} size="small">
        <Descriptions.Item label="Invoice Number">
          {selectedInvoice.invoiceNumber}
        </Descriptions.Item>
        <Descriptions.Item label="Date">{formatDate(selectedInvoice.date)}</Descriptions.Item>
        <Descriptions.Item label="Vendor">{selectedInvoice.vendor.name}</Descriptions.Item>
        <Descriptions.Item label="Total Amount">
          {formatCurrency(selectedInvoice.totalAmount)}
        </Descriptions.Item>
        <Descriptions.Item label="Paid Amount">
          {formatCurrency(selectedInvoice.paidAmount)}
        </Descriptions.Item>
        <Descriptions.Item label="Status">
          <Tag color={selectedInvoice.status === 'ACTIVE' ? 'green' : 'red'}>
            {selectedInvoice.status}
          </Tag>
        </Descriptions.Item>
      </Descriptions>
    )

  return (
    <div>
      <Table
        columns={columns}
        dataSource={data}
        loading={loading}
        rowKey="id"
        sticky
        virtual
        size="small"
        pagination={{
          ...pagination,
          total,
          position: ['topRight'],
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`
        }}
        onChange={onTableChange}
      />

      <Modal
        title="Void Purchase Invoice"
        open={isVoidModalVisible}
        onOk={handleVoidInvoice}
        onCancel={() => {
          setIsVoidModalVisible(false)
          setSelectedInvoice(null)
          setVoidReason('')
        }}
        okButtonProps={{ danger: true }}
      >
        <Space direction="vertical" size="large" style={{ width: '100%' }}>
          <InvoiceDetails />
          <Input.TextArea
            placeholder="Reason for voiding invoice"
            value={voidReason}
            onChange={(e) => setVoidReason(e.target.value)}
            required
            rows={4}
          />
        </Space>
      </Modal>

      <PurchaseInvoiceDetailsModal
        visible={isDetailsModalVisible}
        onClose={() => {
          setIsDetailsModalVisible(false)
          setSelectedInvoice(null)
        }}
        purchaseInvoice={selectedInvoice}
      />
    </div>
  )
}
