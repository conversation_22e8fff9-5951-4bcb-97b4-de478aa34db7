import { Typography, Space, Button, Alert, Skeleton, Statistic, Row, Col } from 'antd'
import { useApi } from '@/renderer/hooks'
import { dashboardApi } from '@/renderer/services'
import { CashFlowOverview as CashFlowOverviewType } from '@/common/types/dashBoard'
import { MdAccountBalance, MdRefresh } from 'react-icons/md'
import { FaArrowDown, FaArrowUp, FaVault, FaWallet } from 'react-icons/fa6'
import { formatCurrency } from '@/renderer/utils'
import { useEffect } from 'react'
import { useTheme } from '@/renderer/contexts'

const { Title } = Typography

const CashFlowOverview = () => {
  const { isDarkMode } = useTheme()

  const {
    data,
    isLoading,
    error,
    request: fetchCashFlow
  } = useApi<CashFlowOverviewType, []>(dashboardApi.getCashFlowOverview)

  useEffect(() => {
    fetchCashFlow()
  }, [])

  if (isLoading) {
    return (
      <div className="flex flex-col gap-4">
        <Space className="flex justify-between">
          <Space>
            <MdAccountBalance className="text-2xl text-indigo-600" />
            <Title level={5} className="!mb-0">
              Cash Flow Overview
            </Title>
          </Space>
          <Button icon={<MdRefresh />} loading />
        </Space>
        <Skeleton active paragraph={{ rows: 4 }} />
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex flex-col gap-4">
        <Space className="flex justify-between">
          <Space>
            <MdAccountBalance className="text-2xl text-indigo-600" />
            <Title level={5} className="!mb-0">
              Cash Flow Overview
            </Title>
          </Space>
          <Button icon={<MdRefresh />} onClick={() => fetchCashFlow()} />
        </Space>
        <Alert message="Error" description="Failed to load cash flow data" type="error" showIcon />
      </div>
    )
  }

  if (!data) return null

  return (
    <div className="flex flex-col gap-4">
      <Space className="flex justify-between">
        <Space>
          <MdAccountBalance className="text-2xl text-indigo-600" />
          <Title level={5} className="!mb-0">
            Cash Flow Overview
          </Title>
        </Space>
        <Button icon={<MdRefresh />} onClick={() => fetchCashFlow()} size="small" />
      </Space>

      <Row gutter={[16, 16]}>
        <Col span={24}>
          <div
            className={`rounded-lg border shadow-md ${isDarkMode ? 'border-gray-800 bg-gray-900' : 'border-gray-200 bg-slate-50'} p-4`}
          >
            <Space className="mb-2">
              <MdAccountBalance className="text-xl text-blue-500" />
              <span className="text-sm font-medium">Bank Balance</span>
            </Space>
            <Statistic
              value={data.bank.totalBalance}
              formatter={(value) => formatCurrency(value as number)}
              valueStyle={{ fontSize: '1.25rem' }}
            />
            <div className="mt-2 text-sm">
              <Space>
                {data.bank.lastDayChange >= 0 ? (
                  <FaArrowUp className="text-green-500" />
                ) : (
                  <FaArrowDown className="text-red-500" />
                )}
                <span className={data.bank.lastDayChange >= 0 ? 'text-green-500' : 'text-red-500'}>
                  {formatCurrency(Math.abs(data.bank.lastDayChange))}
                </span>
                <span className="text-gray-500">today</span>
              </Space>
            </div>
          </div>
        </Col>
        <Col span={12}>
          <div
            className={`rounded-lg border shadow-md ${isDarkMode ? 'border-gray-800 bg-gray-900' : 'border-gray-200 bg-slate-50'} p-4`}
          >
            <Space className="mb-2">
              <FaVault className="text-xl text-purple-500" />
              <span className="text-sm font-medium">Vault</span>
            </Space>
            <Statistic
              value={data.vault.balance}
              formatter={(value) => formatCurrency(value as number)}
              valueStyle={{ fontSize: '1.25rem' }}
            />
            <div className="mt-2 text-sm">
              <Space>
                {data.vault.lastDayChange >= 0 ? (
                  <FaArrowUp className="text-green-500" />
                ) : (
                  <FaArrowDown className="text-red-500" />
                )}
                <span className={data.vault.lastDayChange >= 0 ? 'text-green-500' : 'text-red-500'}>
                  {formatCurrency(Math.abs(data.vault.lastDayChange))}
                </span>
                <span className="text-gray-500">today</span>
              </Space>
            </div>
          </div>
        </Col>
        <Col span={12}>
          <div
            className={`rounded-lg border shadow-md ${isDarkMode ? 'border-gray-800 bg-gray-900' : 'border-gray-200 bg-slate-50'} p-4`}
          >
            <Space className="mb-2">
              <FaWallet className="text-xl text-orange-500" />
              <span className="text-sm font-medium">Counter</span>
            </Space>
            <Statistic
              value={data.counter.balance}
              formatter={(value) => formatCurrency(value as number)}
              valueStyle={{ fontSize: '1.25rem' }}
            />
            <div className="mt-2 text-sm">
              <Space>
                {data.counter.lastDayChange >= 0 ? (
                  <FaArrowUp className="text-green-500" />
                ) : (
                  <FaArrowDown className="text-red-500" />
                )}
                <span
                  className={data.counter.lastDayChange >= 0 ? 'text-green-500' : 'text-red-500'}
                >
                  {formatCurrency(Math.abs(data.counter.lastDayChange))}
                </span>
                <span className="text-gray-500">today</span>
              </Space>
            </div>
          </div>
        </Col>
      </Row>
    </div>
  )
}

export default CashFlowOverview
