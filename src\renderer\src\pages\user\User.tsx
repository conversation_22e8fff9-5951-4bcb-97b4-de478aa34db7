import { Table } from '@/renderer/components'
import { Form, Input, Popover, Button, Checkbox, Modal } from 'antd'
import React, { useState } from 'react'
import { GoFilter } from 'react-icons/go'
import './User.scss'
import { RequiredRule } from '@/common/constants'

const User = () => {
  const [form] = Form.useForm()
  const [isModalVisible, setIsModalVisible] = useState(false)
  const [sortOrder, setSortOrder] = useState('')

  const toggleModal = () => {
    setIsModalVisible(!isModalVisible)
    form.resetFields()
  }
  const handleCheckboxChange = (e: any) => {
    console.log('==============', e)

    setSortOrder(e.target.value)
  }
  const handleSubmitForm = (values: any) => {
    console.log('Form value=========================', values)
    setIsModalVisible(false)
  }

  // Content for the popover
  const FilterContent = () => {
    return (
      <div className="p-2 w-500">
        <span className="text-lg mb-2">Sort:</span>
        <div className="flex">
          <Form.Item>
            <Checkbox value="asc" checked={sortOrder === 'asc'} onChange={handleCheckboxChange}>
              Ascending
            </Checkbox>
          </Form.Item>
          <Form.Item>
            <Checkbox value="desc" checked={sortOrder === 'desc'} onChange={handleCheckboxChange}>
              Descending
            </Checkbox>
          </Form.Item>
        </div>
        <div className="flex justify-end">
          <Button type="primary">Apply</Button>
        </div>
      </div>
    )
  }

  const FilterDropdown = () => {
    return (
      <Popover
        content={<FilterContent />}
        trigger="click"
        placement="bottomRight"
        overlayStyle={{ width: '370px' }}
      >
        <GoFilter size={24} />
      </Popover>
    )
  }
  return (
    <section>
      <div>
        <Form.Item layout="vertical" label="Search">
          <Input type="text" placeholder="Search..." addonAfter={<FilterDropdown />} size="large" />
        </Form.Item>
      </div>
      <div className="flex items-center justify-end">
        <Button type="primary" onClick={toggleModal}>
          Create
        </Button>
      </div>
      <div>
        <Modal title="Create User" open={isModalVisible} onCancel={toggleModal} footer={false}>
          <Form layout="vertical" form={form} onFinish={handleSubmitForm}>
            <Form.Item
              label="First Name"
              name="firstName"
              rules={[{ required: true, message: 'First name is required' }]}
            >
              <Input type="text" size="large" />
            </Form.Item>
            <Form.Item label="Last Name" name="lastName" rules={RequiredRule}>
              <Input type="text" size="large" />
            </Form.Item>
            <Form.Item label="Email" name="email" rules={RequiredRule}>
              <Input type="email" size="large" />
            </Form.Item>
            <Form.Item label="Password" name="password" rules={RequiredRule}>
              <Input.Password size="large" />
            </Form.Item>
            <div className="flex justify-end">
              <Button type="primary" htmlType="submit">
                Submit
              </Button>
            </div>
          </Form>
        </Modal>
      </div>
      <div className="pt-5">
        <Table />
      </div>
    </section>
  )
}

export default User
