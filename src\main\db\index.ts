
import { PrismaClient } from '@prisma/client'
import { app } from 'electron'
import path from 'path'
import fs from 'fs'




const loadConfig = () => {
  if (process.env.NODE_ENV === 'development') {
    return process.env
  }

  const configPath = path.join(process.resourcesPath, 'config/production.json')
  try {
    const config = JSON.parse(fs.readFileSync(configPath, 'utf8'))
    return { ...process.env, ...config }
  } catch (error) {
    console.error('Failed to load production config:', error)
    return process.env
  }
}

// Make config available globally
process.env = { ...process.env, ...loadConfig() }

console.log("Loaded DATABASE_URL:", process.env.DATABASE_URL);

// Create Prisma Client instance
const prisma = new PrismaClient({
  log: ['query', 'info', 'warn', 'error'],
  datasources: {
    db: {
      url: process.env.DATABASE_URL
    }
  }
})

// Initialize database connection
async function initDatabase() {
  try {
    await prisma.$connect()
    console.log('Database connection established successfully.')
  } catch (error) {
    console.error('Unable to connect to the database:', error)
  }
}

// Handle cleanup on app quit
app.on('before-quit', async () => {
  await prisma.$disconnect()
})

export { prisma, initDatabase }

