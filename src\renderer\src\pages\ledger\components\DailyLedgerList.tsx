import { DatePicker, Table, Typography, Space, Card, Tooltip } from 'antd'
import { ArrowUpOutlined, ArrowDownOutlined } from '@ant-design/icons'
import { formatDate, formatCurrency } from '@/renderer/utils'
import { LedgerEntry } from '@/common/types/ledger'
import { useApi } from '@/renderer/hooks'
import { ledgerApi } from '@/renderer/services'
import dayjs from 'dayjs'
import LedgerSummary from './LedgerSummary'
import { useState } from 'react'

const { Text } = Typography

export const DailyLedgerList = () => {
  const [selectedDate, setSelectedDate] = useState<dayjs.Dayjs | null>(null)
  const [isSummaryTransitioning, setIsSummaryTransitioning] = useState(false)

  const {
    data: ledgerData,
    isLoading,
    request: fetchLedger,
    clearData: clearLedgerData
  } = useApi<any, [string]>(ledgerApi.getDailyLedgerEntries)

  const handleDateChange = (date: dayjs.Dayjs | null) => {
    setSelectedDate(date)
    if (date) {
      setIsSummaryTransitioning(true)
      fetchLedger(date.format('YYYY-MM-DD')).then(() => {
        setTimeout(() => {
          setIsSummaryTransitioning(false)
        }, 50)
      })
    } else {
      setSelectedDate(null)
      clearLedgerData()
    }
  }

  const columns = [
    {
      title: 'S.No',
      dataIndex: 'index',
      key: 'index',
      render: (_, __, index) => index + 1
    },
    {
      title: 'Date',
      dataIndex: 'date',
      key: 'date',
      render: (date: Date) => (
        <Text className="text-gray-600">{formatDate(date.toISOString())}</Text>
      )
    },
    {
      title: 'Description',
      dataIndex: 'description',
      key: 'description',
      render: (text: string) => {
        if (text.length <= 30) {
          return <Text strong>{text}</Text>
        }

        return (
          <Tooltip title={text} mouseEnterDelay={0.3}>
            <Text strong>{text.substring(0, 30)}...</Text>
          </Tooltip>
        )
      }
    },
    {
      title: 'Credit',
      key: 'credit',
      render: (entry: LedgerEntry) =>
        entry.creditOrDebit === 'CREDIT' ? (
          <span style={{ color: '#52c41a', display: 'flex', alignItems: 'center', gap: '4px' }}>
            <ArrowUpOutlined />
            {formatCurrency(entry.amount)}
          </span>
        ) : (
          '-'
        )
    },
    {
      title: 'Debit',
      key: 'debit',
      render: (entry: LedgerEntry) =>
        entry.creditOrDebit === 'DEBIT' ? (
          <span style={{ color: '#f5222d', display: 'flex', alignItems: 'center', gap: '4px' }}>
            <ArrowDownOutlined />
            {formatCurrency(entry.amount)}
          </span>
        ) : (
          '-'
        )
    },
    {
      title: 'Balance',
      dataIndex: 'balance',
      key: 'balance',
      render: (balance: number) => (
        <span
          style={{
            color: balance >= 0 ? '#52c41a' : '#f5222d',
            display: 'flex',
            alignItems: 'center',
            gap: '4px'
          }}
        >
          {balance >= 0 ? <ArrowUpOutlined /> : <ArrowDownOutlined />}
          {formatCurrency(balance)}
        </span>
      )
    }
  ]

  const entries = ledgerData?.entries || []
  const totals = ledgerData?.totals || { credits: 0, debits: 0, net: 0 }

  return (
    <Space direction="vertical" size="large" className="w-full">
      <div className="mb-4">
        <DatePicker className="w-[300px]" allowClear onChange={handleDateChange} />
      </div>
      <Table
        columns={columns}
        dataSource={entries}
        loading={isLoading}
        pagination={false}
        rowKey="id"
      />
      <LedgerSummary
        credits={totals.credits}
        debits={totals.debits}
        net={totals.net}
        visible={ledgerData !== null && !isSummaryTransitioning}
      />
    </Space>
  )
}
