import { Space, Select, Radio, message, Card } from 'antd'
import { useProductContext, useTheme } from '@/renderer/contexts'
import { useEffect, useState } from 'react'
import { useApi } from '@/renderer/hooks'
import { productApi, stockApi } from '@/renderer/services'
import { AvailableStockTable } from './components/AvailableStockTable'
import { ProductStockTable } from './components/ProductStockTable'
import ProductDetailsCard from './components/ProductDetailsCard'

type StockStatus = 'IN_STOCK' | 'SOLD_OUT'

export interface StockEntry {
  id: string
  saleItem?: {
    saleInvoice: {
      invoiceNumber: string
      date: string
    }
  }
  WalkInSaleItem?: {
    saleInvoice: {
      invoiceNumber: string
      date: string
    }
  }
}

export interface Stock {
  id: string
  purchasePrice: number
  quantity: number
  status: 'IN_STOCK' | 'SOLD_OUT'
  createdAt: Date
  updatedAt: Date
  vendor: {
    id: string
    name: string
  }
  purchaseInvoice: {
    id: string
    invoiceNumber: string
    date: string
    status: string
  }
  StockEntry: StockEntry[]
}

export interface StockResponse {
  stocks: Stock[]
  total: number
  page: number
  totalPages: number
}

interface ProductDetials {
  name: string
  productId: string
  category: string
  nature?: string
  tag?: string
  quantityInStock: number
  minStockLevel?: number
  salePrice: number
}

const Stock = () => {
  const { products } = useProductContext()
  const [selectedProduct, setSelectedProduct] = useState<string | null>(null)
  const [selectedStatus, setSelectedStatus] = useState<StockStatus>('IN_STOCK')

  const [isCardTransitioning, setIsCardTransitioning] = useState(false)

  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10
  })

  const { isDarkMode } = useTheme()

  // For general available stock
  const {
    data: availableStockData,
    isLoading: availableLoading,
    error: availableError,
    errorMessage: availableErrorMessage,
    request: fetchAvailableStock
  } = useApi<StockResponse, [{ page: number; limit: number; status: StockStatus }]>(
    stockApi.getAvailableStock
  )

  // For product specific stock
  const {
    data: productStockData,
    isLoading: productLoading,
    error: productError,
    errorMessage: productErrorMessage,
    request: fetchProductStock
  } = useApi<StockResponse, [string, { page: number; limit: number; status: StockStatus }]>(
    stockApi.getStockByProduct
  )

  const { data: productData, request: fetchProduct } = useApi<ProductDetials, [string]>(
    productApi.getProduct
  )

  useEffect(() => {
    if (selectedProduct) {
      fetchProductStock(selectedProduct, {
        page: pagination.current,
        limit: pagination.pageSize,
        status: selectedStatus
      })
    } else {
      fetchAvailableStock({
        page: pagination.current,
        limit: pagination.pageSize,
        status: selectedStatus
      })
    }
  }, [selectedProduct, selectedStatus, pagination.current, pagination.pageSize])

  if (availableError) message.error(availableErrorMessage)
  if (productError) message.error(productErrorMessage)

  // useEffect(() => {
  //   console.log('availableStockData', availableStockData)
  //   console.log('productStockData', productStockData)
  // }, [availableStockData, productStockData])

  // Modify the Select onChange handler
  const handleProductChange = (value: string | null) => {
    if (value) {
      console.log(value)
      setIsCardTransitioning(true)
      setTimeout(() => {
        setSelectedProduct(value)
        fetchProduct(value).then(() => {
          console.log(productData)
          setTimeout(() => {
            setIsCardTransitioning(false)
          }, 50)
        })
      }, 500)
    } else {
      setIsCardTransitioning(true)
      setTimeout(() => {
        setSelectedProduct(null)
        setIsCardTransitioning(false)
      }, 500)
    }
  }

  return (
    <Card
      className={`m-6 bg-[length:200%_200%] bg-[center] shadow-lg ${
        isDarkMode
          ? 'bg-gradient-to-bl from-indigo-950 via-black to-indigo-900 shadow-indigo-950'
          : 'bg-gradient-to-bl from-indigo-200 via-white to-indigo-100 shadow-indigo-200'
      }`}
    >
      <Space className="mb-6">
        <Select
          allowClear
          showSearch
          style={{ width: 300 }}
          placeholder="Select Product"
          onChange={(value) => handleProductChange(value)}
          options={products}
          filterOption={(input, option) =>
            (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
          }
        />
        {selectedProduct && (
          <Radio.Group value={selectedStatus} onChange={(e) => setSelectedStatus(e.target.value)}>
            <Radio.Button value="IN_STOCK">In Stock</Radio.Button>
            <Radio.Button value="SOLD_OUT">Sold Out</Radio.Button>
          </Radio.Group>
        )}
      </Space>

      <ProductDetailsCard
        product={productData}
        visible={selectedProduct !== null && !isCardTransitioning}
      />

      {selectedProduct ? (
        <ProductStockTable
          data={productStockData?.stocks}
          loading={productLoading}
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: productStockData?.total,
            onChange: (page, pageSize) => setPagination({ current: page, pageSize })
          }}
        />
      ) : (
        <AvailableStockTable
          data={availableStockData?.stocks}
          loading={availableLoading}
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: availableStockData?.total,
            onChange: (page, pageSize) => setPagination({ current: page, pageSize })
          }}
        />
      )}
    </Card>
  )
}

export default Stock
