import { useState, useEffect } from 'react'
import {
  Table,
  Space,
  Input,
  DatePicker,
  Select,
  Tooltip,
  Tag,
  App,
  Typography,
  Button,
  Form,
  Divider
} from 'antd'

import { saleInvoiceApi } from '@/renderer/services'
import type { GetSaleInvoicesParams } from '@/common/types'
import dayjs from 'dayjs'
import { usePartyContext, useTheme } from '@/renderer/contexts'
import {
  InfoCircleOutlined,
  TagOutlined,
  ShopOutlined,
  UserOutlined,
  PrinterOutlined,
  DeleteOutlined,
  EyeOutlined,
  SearchOutlined
} from '@ant-design/icons'
import { useSelector } from 'react-redux'
import { IRootState } from '@/renderer/redux'
import VoidConfirmationModal from './components/VoidConfirmationModal'
import { generateInvoicePDF } from './utils'
import PrintSaveModal from './components/PrintSaveModal'
import RegisteredSaleInvoiceDetailsModal from './components/RegisteredSaleInvoiceDetailsModal'
import { useProductContext } from '@/renderer/contexts/ProductContext'

const { RangePicker } = DatePicker
const { Search } = Input
const { Text } = Typography

interface Props {
  refreshTrigger: number
}

const RegisteredSaleList = ({ refreshTrigger }: Props) => {
  const [loading, setLoading] = useState(false)
  const [data, setData] = useState<any[]>([])
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0
  })
  const [filters, setFilters] = useState<GetSaleInvoicesParams>({
    page: 1,
    limit: 10,
    status: 'ACTIVE',
    invoiceType: 'REGISTERED'
  })

  const [voidingInvoiceId, setVoidingInvoiceId] = useState<string | null>(null)
  const user = useSelector((state: IRootState) => state.user.data)
  const [voidForm] = Form.useForm()
  const [voidModalVisible, setVoidModalVisible] = useState(false)
  const [selectedInvoice, setSelectedInvoice] = useState<any>(null)
  const [printSaveModalVisible, setPrintSaveModalVisible] = useState(false)
  const [printSaveLoading, setPrintSaveLoading] = useState(false)
  const [isDetailsModalVisible, setIsDetailsModalVisible] = useState(false)

  const { customers } = usePartyContext()
  const { products } = useProductContext()
  const { message } = App.useApp()
  const { isDarkMode } = useTheme()

  const fetchData = async () => {
    setLoading(true)
    const response = await saleInvoiceApi.getSaleInvoices(filters)
    setLoading(false)

    if (response.error.error || response.data.error) {
      message.error(response.error.message || response.data.error.message)
      return
    }

    setData(response.data.data.invoices)
    setPagination({
      ...pagination,
      total: response.data.data.total
    })
  }

  useEffect(() => {
    fetchData()
  }, [filters, refreshTrigger])

  const handlePrintSaveClick = (record: any) => {
    setSelectedInvoice(record)
    setPrintSaveModalVisible(true)
  }

  const handlePrint = async () => {
    try {
      setPrintSaveLoading(true)
      const doc = await generateInvoicePDF(selectedInvoice)
      if (doc) {
        // Get PDF as base64 data
        const pdfData = doc.output('dataurlstring', { filename: 'invoice.pdf' })

        // Send to main process for printing
        const success = await window.electron.ipcRenderer.invoke('print-pdf', pdfData)

        if (success) {
          message.success('Invoice sent to printer')
          setPrintSaveModalVisible(false)
          setSelectedInvoice(null)
        }
        setPrintSaveLoading(false)
      } else {
        setPrintSaveLoading(false)
      }
    } catch (error) {
      console.error('Error printing invoice:', error)
      message.error('Failed to print invoice')
      setPrintSaveLoading(false)
    }
  }

  const handleSave = async () => {
    try {
      setPrintSaveLoading(true)
      const doc = await generateInvoicePDF(selectedInvoice, true)
      if (doc) {
        message.success('Invoice PDF generated successfully')
      }
      setPrintSaveLoading(false)
      setPrintSaveModalVisible(false)
      setSelectedInvoice(null)
    } catch (error) {
      console.error('Error generating PDF:', error)
      message.error('Failed to generate PDF')
      setPrintSaveLoading(false)
    }
  }

  const handlePrintSaveCancel = () => {
    setPrintSaveModalVisible(false)
    setSelectedInvoice(null)
    setPrintSaveLoading(false)
  }

  const showVoidConfirm = (record: any) => {
    setSelectedInvoice(record)
    setVoidModalVisible(true)
  }

  const handleVoidConfirm = async (reason: string) => {
    try {
      setLoading(true)
      const response = await saleInvoiceApi.voidRegisteredSale({
        id: selectedInvoice.id,
        adminId: user?.id || '',
        reason
      })
      setLoading(false)

      if (response.error.error || response.data.error) {
        message.error(response.error.message || response.data.error.message)
        return
      }

      message.success('Invoice voided successfully')
      setVoidModalVisible(false)
      setSelectedInvoice(null)
      fetchData()
    } catch (error: any) {
      message.error(error.message)
      setLoading(false)
    }
  }

  const handleVoidCancel = () => {
    setVoidModalVisible(false)
    setSelectedInvoice(null)
  }

  const columns = [
    {
      title: 'Sr. No.',
      dataIndex: 'id',
      key: 'id',
      render: (text: string, record: any, index: number) => (
        <Text strong>{index + 1 + (pagination.current - 1) * pagination.pageSize}</Text>
      )
    },
    {
      title: 'Invoice #',
      dataIndex: 'invoiceNumber',
      key: 'invoiceNumber',
      render: (text: string) => <Text strong>{text}</Text>
    },
    {
      title: 'Date',
      dataIndex: 'date',
      key: 'date',
      render: (date: Date) => (
        <Space>
          {/* <ClockCircleOutlined className="text-blue-500" /> */}
          {dayjs(date).format('DD/MM/YYYY HH:mm')}
        </Space>
      )
    },
    {
      title: 'Customer',
      dataIndex: 'customer',
      key: 'customer',
      render: (customer: any) => (
        <Space direction="vertical" size="small">
          <Space>
            <UserOutlined className="text-blue-500" />
            <Text strong>{customer.name}</Text>
          </Space>
        </Space>
      )
    },
    {
      title: 'Net Amount',
      key: 'netAmount',
      render: (_: any, record: any) => {
        const netAmount = record.totalAmount - record.discountAmount
        return (
          <Space>
            <Text strong className="text-lg">
              {netAmount.toLocaleString('en-US', { minimumFractionDigits: 2 })}
            </Text>
            <Tooltip
              overlayInnerStyle={{
                backgroundColor: isDarkMode ? '#000' : '#fff',
                color: isDarkMode ? '#fff' : '#000'
              }}
              title={
                <div className="space-y-2">
                  <div>
                    <Text style={{ color: isDarkMode ? '#a3a3a3' : '#666' }}>Total Amount:</Text>
                    <Text strong style={{ color: isDarkMode ? '#fff' : '#000' }} className="ml-2">
                      {record.totalAmount.toLocaleString('en-US', { minimumFractionDigits: 2 })}
                    </Text>
                  </div>
                  <div>
                    <Text style={{ color: isDarkMode ? '#a3a3a3' : '#666' }}>Discount:</Text>
                    <Text strong className="ml-2 text-red-500">
                      -{record.discountAmount.toLocaleString('en-US', { minimumFractionDigits: 2 })}
                    </Text>
                  </div>
                  <div>
                    <Text style={{ color: isDarkMode ? '#a3a3a3' : '#666' }}>Paid Amount:</Text>
                    <Text strong className="ml-2 text-green-500">
                      {record.paidAmount.toLocaleString('en-US', { minimumFractionDigits: 2 })}
                    </Text>
                  </div>
                  <div>
                    <Text style={{ color: isDarkMode ? '#a3a3a3' : '#666' }}>
                      Previous Balance:
                    </Text>
                    <Text
                      strong
                      className={`ml-2 ${
                        record.previousBalance >= 0 ? 'text-green-500' : 'text-red-500'
                      }`}
                    >
                      {record.previousBalance.toLocaleString('en-US', { minimumFractionDigits: 2 })}
                    </Text>
                  </div>
                  <div>
                    <Text style={{ color: isDarkMode ? '#a3a3a3' : '#666' }}>New Balance:</Text>
                    <Text
                      strong
                      className={`ml-2 ${record.newBalance >= 0 ? 'text-green-500' : 'text-red-500'}`}
                    >
                      {record.newBalance.toLocaleString('en-US', { minimumFractionDigits: 2 })}
                    </Text>
                  </div>
                  <div>
                    <Text style={{ color: isDarkMode ? '#a3a3a3' : '#666' }}>Profit:</Text>
                    <Text strong className="ml-2">
                      <Tag color={record.totalProfit >= 0 ? 'success' : 'error'}>
                        {record.totalProfit >= 0 ? 'Profit: ' : 'Loss: '}
                        {Math.abs(record.totalProfit).toLocaleString('en-US', {
                          minimumFractionDigits: 2
                        })}
                      </Tag>
                    </Text>
                  </div>
                </div>
              }
            >
              <InfoCircleOutlined className={isDarkMode ? 'text-gray-400' : 'text-gray-600'} />
            </Tooltip>
          </Space>
        )
      }
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status: string, record: any) => (
        <Space>
          <Tag color={status === 'ACTIVE' ? 'success' : 'error'}>{status}</Tag>
          <Tooltip
            overlayInnerStyle={{
              backgroundColor: isDarkMode ? '#000' : '#fff',
              color: isDarkMode ? '#fff' : '#000'
            }}
            title={
              <div className="space-y-2">
                <div>
                  <Text style={{ color: isDarkMode ? '#a3a3a3' : '#666' }}>Created by:</Text>
                  <Text strong style={{ color: isDarkMode ? '#fff' : '#000' }} className="ml-2">
                    {record.createdBy.name}
                  </Text>
                </div>
                <div>
                  <Text style={{ color: isDarkMode ? '#a3a3a3' : '#666' }}>Created at:</Text>
                  <Text strong style={{ color: isDarkMode ? '#fff' : '#000' }} className="ml-2">
                    {dayjs(record.createdAt).format('DD/MM/YYYY HH:mm')}
                  </Text>
                </div>
                {status === 'VOID' && (
                  <>
                    <div className="my-1 border-t border-gray-600" />
                    <div>
                      <Text style={{ color: isDarkMode ? '#a3a3a3' : '#666' }}>Voided by:</Text>
                      <Text strong className="ml-2 text-red-500">
                        {record.voidedBy.name}
                      </Text>
                    </div>
                    <div>
                      <Text style={{ color: isDarkMode ? '#a3a3a3' : '#666' }}>Voided at:</Text>
                      <Text strong className="ml-2 text-red-500">
                        {dayjs(record.voidedAt).format('DD/MM/YYYY HH:mm')}
                      </Text>
                    </div>
                    <div>
                      <Text style={{ color: isDarkMode ? '#a3a3a3' : '#666' }}>Reason:</Text>
                      <Text strong className="ml-2 text-red-500">
                        {record.voidingReason}
                      </Text>
                    </div>
                  </>
                )}
              </div>
            }
          >
            <InfoCircleOutlined className={isDarkMode ? 'text-gray-400' : 'text-gray-600'} />
          </Tooltip>
        </Space>
      )
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record) => (
        <Space>
          <Button
            icon={<EyeOutlined />}
            onClick={() => {
              setSelectedInvoice(record)
              setIsDetailsModalVisible(true)
            }}
            type="text"
          />
          <Button
            icon={<PrinterOutlined />}
            onClick={() => handlePrintSaveClick(record)}
            type="text"
          />
          {record.status === 'ACTIVE' && (
            <Button
              icon={<DeleteOutlined />}
              onClick={() => showVoidConfirm(record)}
              type="text"
              danger
              loading={loading && voidingInvoiceId === record.id}
            />
          )}
        </Space>
      )
    }
  ]

  return (
    <Space direction="vertical" size="middle" className="w-full">
      <Space wrap className="w-full justify-between">
        <Space wrap>
          <Select
            className="w-96"
            allowClear
            showSearch
            placeholder="Select customer"
            onChange={(value) => setFilters({ ...filters, partyId: value, page: 1 })}
            options={customers}
            filterOption={(input, option) =>
              (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
            }
          />

          <Select
            className="w-72"
            allowClear
            showSearch
            placeholder="Filter by product"
            onChange={(value) => setFilters({ ...filters, productId: value, page: 1 })}
            options={products}
            filterOption={(input, option) =>
              (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
            }
          />

          <Search
            placeholder="Search invoice # or customer"
            allowClear
            className="w-72"
            onSearch={(value) => setFilters({ ...filters, search: value, page: 1 })}
          />
          <RangePicker
            allowClear
            onChange={(dates) => {
              setFilters({
                ...filters,
                startDate: dates?.[0]?.toDate(),
                endDate: dates?.[1]?.toDate(),
                page: 1
              })
            }}
          />

          <Select
            className="w-32"
            value={filters.status}
            onChange={(value) => setFilters({ ...filters, status: value, page: 1 })}
            options={[
              { label: 'Active', value: 'ACTIVE' },
              { label: 'Void', value: 'VOID' },
              { label: 'All', value: 'ALL' }
            ]}
          />
        </Space>
      </Space>

      <Table
        size="small"
        rowKey="id"
        columns={columns}
        dataSource={data}
        loading={loading}
        sticky
        virtual
        pagination={{
          ...pagination,
          position: ['topRight'],
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`,
          onChange: (page, pageSize) => {
            setPagination({ ...pagination, current: page, pageSize })
            setFilters({ ...filters, page, limit: pageSize })
          }
        }}
      />

      <RegisteredSaleInvoiceDetailsModal
        visible={isDetailsModalVisible}
        onClose={() => {
          setIsDetailsModalVisible(false)
          setSelectedInvoice(null)
        }}
        invoice={selectedInvoice}
      />

      <VoidConfirmationModal
        open={voidModalVisible}
        loading={loading}
        onConfirm={handleVoidConfirm}
        onCancel={handleVoidCancel}
      />

      <PrintSaveModal
        open={printSaveModalVisible}
        loading={printSaveLoading}
        onSave={handleSave}
        onPrint={handlePrint}
        onCancel={handlePrintSaveCancel}
      />
    </Space>
  )
}

export default RegisteredSaleList
