import React from 'react'
import {
  MdDelete,
  MdOutlineDashboard,
  MdPeople,
  MdInventory2,
  MdOutlineReceiptLong,
  MdStorefront,
  MdPointOfSale,
  MdShoppingCart,
  MdPayment,
  MdAccountBalance
} from 'react-icons/md'
import { GiWallet } from 'react-icons/gi'
import { BsFillCartXFill } from "react-icons/bs";
import { TbReportAnalytics } from "react-icons/tb";
import {
  FaMoneyBillTransfer,
  FaUsersGear,
  FaVault,
  FaBoxOpen,
  FaMoneyBill,
  FaPenToSquare,
} from 'react-icons/fa6'
import { HiArchiveBoxXMark } from "react-icons/hi2"
import { LuBoxes } from "react-icons/lu"
import { Roles } from './roles'
import { App_Routes } from './routes'
import { IMenu } from '../types'

const iconProps = {
  size: 20
}

export const Menus: IMenu[] = [
  // Dashboard
  {
    key: App_Routes.DASHBOARD,
    label: 'Dashboard',
    roles: [Roles.ADMIN, Roles.USER],
    icon: React.createElement(MdOutlineDashboard, iconProps)
  },

  // Staff Management
  {
    key: App_Routes.STAFF,
    label: 'Staff',
    roles: [Roles.ADMIN],
    icon: React.createElement(FaUsersGear, iconProps)
  },

  // Products & Stock Management
  {
    key: App_Routes.PRODUCTS,
    label: 'Products',
    roles: [Roles.ADMIN],
    icon: React.createElement(MdInventory2, iconProps)
  },
  {
    key: App_Routes.STOCK,
    label: 'Stock',
    roles: [Roles.ADMIN],
    icon: React.createElement(LuBoxes, iconProps)
  },
  {
    key: App_Routes.OPENING_STOCK,
    label: 'Opening Stock',
    roles: [Roles.ADMIN],
    icon: React.createElement(FaBoxOpen, iconProps)
  },
  {
    key: App_Routes.STOCK_RETURN,
    label: 'Stock Return',
    roles: [Roles.ADMIN],
    icon: React.createElement(HiArchiveBoxXMark, iconProps)
  },
  {
    key: App_Routes.RETURN_TO_VENDOR,
    label: 'Return to Vendor',
    roles: [Roles.ADMIN],
    icon: React.createElement(BsFillCartXFill, iconProps)
  },

  // Party Management
  {
    key: App_Routes.VENDORS,
    label: 'Vendors',
    roles: [Roles.ADMIN],
    icon: React.createElement(MdStorefront, iconProps)
  },
  {
    key: App_Routes.CUSTOMERS,
    label: 'Customers',
    roles: [Roles.ADMIN],
    icon: React.createElement(MdPeople, iconProps)
  },
  {
    key: App_Routes.CREDITORS,
    label: 'Creditors',
    roles: [Roles.ADMIN],
    icon: React.createElement(FaMoneyBillTransfer, iconProps)
  },

  // Sales & Purchase
  {
    key: App_Routes.PURCHASE_INVOICE,
    label: 'Purchase Invoice',
    roles: [Roles.ADMIN],
    icon: React.createElement(MdShoppingCart, iconProps)
  },
  {
    key: App_Routes.SALE_INVOICE,
    label: 'Sale Invoice',
    roles: [Roles.ADMIN],
    icon: React.createElement(MdPointOfSale, iconProps)
  },

  // Finance & Cash Management
  {
    key: App_Routes.PAYMENTS,
    label: 'Payments',
    roles: [Roles.ADMIN],
    icon: React.createElement(MdPayment, iconProps)
  },
  {
    key: App_Routes.EXPENSE,
    label: 'Expense',
    roles: [Roles.ADMIN],
    icon: React.createElement(FaMoneyBill, iconProps)
  },
  {
    key: App_Routes.BANKS,
    label: 'Banks',
    roles: [Roles.ADMIN],
    icon: React.createElement(MdAccountBalance, iconProps)
  },
  {
    key: App_Routes.CASH_VAULT,
    label: 'Cash Vault',
    roles: [Roles.ADMIN],
    icon: React.createElement(FaVault, iconProps)
  },
  {
    key: App_Routes.SMALL_COUNTER,
    label: 'Small Counter',
    roles: [Roles.ADMIN],
    icon: React.createElement(GiWallet, iconProps)
  },
  {
    key: App_Routes.LEDGER,
    label: 'Ledger',
    roles: [Roles.ADMIN],
    icon: React.createElement(MdOutlineReceiptLong, iconProps)
  },
  {
    key: App_Routes.MANUAL_ENTRY,
    label: 'Manual Entry',
    roles: [Roles.ADMIN],
    icon: React.createElement(FaPenToSquare, iconProps)
  },
  {
    key: App_Routes.REPORTS,
    label: 'Reports',
    roles: [Roles.ADMIN],
    icon: React.createElement(TbReportAnalytics, iconProps)
  },

  // System
  {
    key: App_Routes.RESET,
    label: 'Reset',
    roles: [Roles.ADMIN],
    icon: React.createElement(MdDelete, iconProps)
  }
]
