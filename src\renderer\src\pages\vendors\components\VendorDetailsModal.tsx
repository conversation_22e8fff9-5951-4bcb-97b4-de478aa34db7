import { useEffect, useState } from 'react'
import { Modal, Form, Input, message, Spin, Row, Col, Statistic, Card, Button } from 'antd'
import { partyApi } from '@/renderer/services'
import { formatCurrency } from '@/renderer/utils'

interface VendorDetailsModalProps {
  vendorId: string | null
  open: boolean
  onClose: () => void
}

interface VendorDetails {
  id: string
  name: string
  contact?: string
  address?: string
  phoneNumber?: string
  currentBalance: number
  _count: {
    PurchaseInvoice: number
    Payments: number
  }
}

export const VendorDetailsModal = ({ vendorId, open, onClose }: VendorDetailsModalProps) => {
  const [form] = Form.useForm()
  const [loading, setLoading] = useState(false)
  const [editing, setEditing] = useState(false)
  const [vendorDetails, setVendorDetails] = useState<VendorDetails | null>(null)

  useEffect(() => {
    if (vendorId && open) {
      fetchVendorDetails()
    }
  }, [vendorId, open])

  const fetchVendorDetails = async () => {
    if (!vendorId) return
    setLoading(true)
    try {
      const response = await partyApi.getParty(vendorId)
      const vendorData = response.data.data
      setVendorDetails(vendorData)
      form.setFieldsValue({
        name: vendorData.name,
        contact: vendorData.contact,
        phoneNumber: vendorData.phoneNumber,
        address: vendorData.address
      })
    } catch (error) {
      message.error('Failed to fetch vendor details')
      onClose()
    } finally {
      setLoading(false)
    }
  }

  const handleUpdate = async (values: any) => {
    if (!vendorId) return
    try {
      await partyApi.updateParty(vendorId, values)
      message.success('Vendor updated successfully')
      setEditing(false)
      fetchVendorDetails()
    } catch (error) {
      message.error('Failed to update vendor')
    }
  }

  return (
    <Modal
      title="Vendor Details"
      open={open}
      onCancel={onClose}
      width={800}
      footer={
        editing
          ? [
              <Button key="cancel" onClick={() => setEditing(false)}>
                Cancel
              </Button>,
              <Button key="submit" type="primary" onClick={() => form.submit()}>
                Save Changes
              </Button>
            ]
          : [
              <Button key="edit" type="primary" onClick={() => setEditing(true)}>
                Edit
              </Button>
            ]
      }
    >
      {loading ? (
        <div className="loading-container">
          <Spin />
        </div>
      ) : (
        <>
          <Row gutter={[16, 16]}>
            <Col span={8}>
              <Card>
                <Statistic
                  title="Current Balance"
                  value={vendorDetails?.currentBalance || 0}
                  precision={2}
                  prefix="Rs."
                />
              </Card>
            </Col>
            <Col span={8}>
              <Card>
                <Statistic
                  title="Total Purchases"
                  value={vendorDetails?._count.PurchaseInvoice || 0}
                />
              </Card>
            </Col>
            <Col span={8}>
              <Card>
                <Statistic title="Total Payments" value={vendorDetails?._count.Payments || 0} />
              </Card>
            </Col>
          </Row>

          <Form
            form={form}
            layout="vertical"
            onFinish={handleUpdate}
            disabled={!editing}
            style={{ marginTop: 24 }}
          >
            <Form.Item
              name="name"
              label="Vendor Name"
              rules={[{ required: true, message: 'Please enter vendor name' }]}
            >
              <Input />
            </Form.Item>

            <Form.Item name="contact" label="Contact Person">
              <Input />
            </Form.Item>

            <Form.Item name="phoneNumber" label="Phone Number">
              <Input />
            </Form.Item>

            <Form.Item name="address" label="Address">
              <Input.TextArea rows={4} />
            </Form.Item>
          </Form>
        </>
      )}
    </Modal>
  )
}
