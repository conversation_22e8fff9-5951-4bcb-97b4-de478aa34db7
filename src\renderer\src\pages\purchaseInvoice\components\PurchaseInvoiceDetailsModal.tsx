import React from 'react'
import { Modal, Typography, Descriptions, Table, Tag, Tooltip, Divider, Space } from 'antd'
import {
  BarcodeOutlined,
  TagsOutlined,
  BoxPlotOutlined,
  InfoCircleOutlined
} from '@ant-design/icons'
import { formatCurrency, formatDate } from '@/renderer/utils'
import { PurchaseInvoiceData } from '@/common/types/purchaseInvoice'

const { Text, Title } = Typography

interface PurchaseInvoiceDetailsModalProps {
  visible: boolean
  onClose: () => void
  purchaseInvoice: PurchaseInvoiceData | null
}

const PurchaseInvoiceDetailsModal: React.FC<PurchaseInvoiceDetailsModalProps> = ({
  visible,
  onClose,
  purchaseInvoice
}) => {
  if (!purchaseInvoice) return null

  const itemColumns = [
    {
      title: 'Sr. No.',
      dataIndex: 'index',
      key: 'index',
      width: 70,
      render: (_, __, index) => index + 1
    },
    {
      title: 'Product Details',
      dataIndex: 'product',
      key: 'product',
      render: (product) => (
        <Space direction="vertical" size="small">
          <Space wrap>
            <Tag>{product.name}</Tag>
            <Tooltip title="Product ID">
              <Tag color="blue" icon={<BarcodeOutlined />}>
                {product.productId}
              </Tag>
            </Tooltip>
            <Tooltip title="Product Tag">
              <Tag color="cyan" icon={<TagsOutlined />}>
                {product.tag || 'N/A'}
              </Tag>
            </Tooltip>
            <Tooltip title="Product Nature">
              <Tag color="purple">{product.nature || 'N/A'}</Tag>
            </Tooltip>
            <Tooltip title="Product Category">
              <Tag color="gold">{product.category?.name || 'N/A'}</Tag>
            </Tooltip>
          </Space>
        </Space>
      )
    },
    {
      title: 'Quantity',
      dataIndex: 'quantity',
      key: 'quantity',
      width: 100,
      align: 'right' as const
    },
    {
      title: 'Purchase Price',
      dataIndex: 'purchasePrice',
      key: 'purchasePrice',
      width: 150,
      align: 'right' as const,
      render: (price: number) => formatCurrency(price)
    },
    {
      title: 'Total',
      dataIndex: 'total',
      key: 'total',
      width: 150,
      align: 'right' as const,
      render: (total: number) => formatCurrency(total)
    }
  ]

  return (
    <Modal
      open={visible}
      onCancel={onClose}
      footer={null}
      width={800}
      title={
        <Space align="center">
          <InfoCircleOutlined />
          <span>Purchase Invoice Details</span>
        </Space>
      }
    >
      <div className="mb-6">
        <Descriptions bordered column={2} size="small">
          <Descriptions.Item label="Invoice Number" span={1}>
            <Text strong>{purchaseInvoice.invoiceNumber}</Text>
          </Descriptions.Item>
          <Descriptions.Item label="Date" span={1}>
            {formatDate(purchaseInvoice.date.toString())}
          </Descriptions.Item>
          <Descriptions.Item label="Vendor" span={2}>
            <Text strong>{purchaseInvoice.vendor.name}</Text>
          </Descriptions.Item>
          {purchaseInvoice.createdBy && (
            <Descriptions.Item label="Created By" span={2}>
              {purchaseInvoice.createdBy.name}
            </Descriptions.Item>
          )}
          <Descriptions.Item label="Total Amount" span={1}>
            <Text strong className="text-lg text-indigo-600">
              {formatCurrency(purchaseInvoice.totalAmount)}
            </Text>
          </Descriptions.Item>
          <Descriptions.Item label="Paid Amount" span={1}>
            <Text strong className="text-lg text-green-600">
              {formatCurrency(purchaseInvoice.paidAmount)}
            </Text>
          </Descriptions.Item>
          <Descriptions.Item label="Balance" span={1}>
            <Text strong className="text-lg text-amber-600">
              {formatCurrency(purchaseInvoice.totalAmount - purchaseInvoice.paidAmount)}
            </Text>
          </Descriptions.Item>
          <Descriptions.Item label="Status" span={1}>
            <Tag color={purchaseInvoice.status === 'ACTIVE' ? 'green' : 'red'}>
              {purchaseInvoice.status}
            </Tag>
          </Descriptions.Item>
        </Descriptions>

        {purchaseInvoice.voidingReason && (
          <div className="mt-4">
            <Title level={5} className="text-red-500">
              Void Information:
            </Title>
            <Descriptions bordered size="small">
              {purchaseInvoice.voidedBy && (
                <Descriptions.Item label="Voided By" span={1}>
                  {purchaseInvoice.voidedBy.name}
                </Descriptions.Item>
              )}
              {purchaseInvoice.voidedAt && (
                <Descriptions.Item label="Voided At" span={purchaseInvoice.voidedBy ? 2 : 3}>
                  {formatDate(purchaseInvoice.voidedAt)}
                </Descriptions.Item>
              )}
              <Descriptions.Item label="Reason" span={3}>
                {purchaseInvoice.voidingReason}
              </Descriptions.Item>
            </Descriptions>
          </div>
        )}
      </div>

      <Divider orientation="left">Items ({purchaseInvoice.items.length})</Divider>

      <Table
        columns={itemColumns}
        dataSource={purchaseInvoice.items}
        pagination={false}
        rowKey="id"
        size="small"
        bordered
        summary={(pageData) => {
          const total = pageData.reduce((sum, item) => sum + item.total, 0)
          return (
            <Table.Summary.Row>
              <Table.Summary.Cell index={0} colSpan={4} align="right">
                <Text strong>Total:</Text>
              </Table.Summary.Cell>
              <Table.Summary.Cell index={1} align="right">
                <Text strong className="text-indigo-600">
                  {formatCurrency(total)}
                </Text>
              </Table.Summary.Cell>
            </Table.Summary.Row>
          )
        }}
      />
    </Modal>
  )
}

export default PurchaseInvoiceDetailsModal
