import { http } from "./http";
import { Channels } from "@/common/constants";
import { GetLedgerParams, PaginatedLedgerResponse, DailyLedgerResponse } from "@/common/types/ledger";

export const getLedgerEntriesByParty = async (partyId: string, params: GetLedgerParams) => {
    return await http.get(Channels.GET_BY_PARTY, {
        params: { partyId },
        query: params
    });
};

export const getDailyLedgerEntries = async (date: string) => {
    return await http.get(Channels.GET_DAILY, {
        query: { date }
    }
    )
}

export const getLedgerEntriesForPDF = async (partyId: string) => {
    return await http.get(Channels.GET_LEDGER_ENTRIES_FOR_PDF, {
        params: { partyId }
    });
}


