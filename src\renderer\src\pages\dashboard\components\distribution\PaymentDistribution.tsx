import { useEffect, useState } from 'react'
import { Typography, Space, Button, Alert, Skeleton, Radio, Row, Col } from 'antd'
import { useApi } from '@/renderer/hooks'
import { dashboardApi } from '@/renderer/services'
import { PaymentMethodDistribution, TimeRange } from '@/common/types/dashBoard'
import { MdPayments, MdRefresh } from 'react-icons/md'
import { formatCurrency } from '@/renderer/utils'
import { PieChart, Pie, Cell, ResponsiveContainer, Legend, Tooltip } from 'recharts'
import { useTheme } from '@/renderer/contexts'

const { Title } = Typography

const COLORS = ['#4F46E5', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6']

const PaymentDistribution = () => {
  const [timeRange, setTimeRange] = useState<TimeRange['days']>(7)
  const { isDarkMode } = useTheme()

  const {
    data,
    isLoading,
    error,
    request: fetchPaymentDistribution
  } = useApi<PaymentMethodDistribution[], [TimeRange]>(dashboardApi.getPaymentMethodDistribution)

  useEffect(() => {
    fetchPaymentDistribution({ days: timeRange })
  }, [])

  const handleTimeRangeChange = (days: TimeRange['days']) => {
    setTimeRange(days)
    fetchPaymentDistribution({ days })
  }

  if (isLoading) {
    return (
      <div className="flex flex-col gap-4">
        <Space className="flex justify-between">
          <Space>
            <MdPayments className="text-2xl text-indigo-600" />
            <Title level={5} className="!mb-0">
              Payment Methods
            </Title>
          </Space>
          <Button icon={<MdRefresh />} loading />
        </Space>
        <Skeleton active paragraph={{ rows: 4 }} />
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex flex-col gap-4">
        <Space className="flex justify-between">
          <Space>
            <MdPayments className="text-2xl text-indigo-600" />
            <Title level={5} className="!mb-0">
              Payment Methods
            </Title>
          </Space>
          <Button
            icon={<MdRefresh />}
            onClick={() => fetchPaymentDistribution({ days: timeRange })}
          />
        </Space>
        <Alert
          message="Error"
          description="Failed to load payment distribution data"
          type="error"
          showIcon
        />
      </div>
    )
  }

  if (!data) return null

  const totalAmount = data.reduce((sum, item) => sum + item.amount, 0)
  const totalCount = data.reduce((sum, item) => sum + item.count, 0)

  const chartData = data.map((item) => ({
    name: item.method.charAt(0) + item.method.slice(1).toLowerCase(),
    value: item.amount
  }))

  return (
    <div className="flex flex-col gap-4">
      <Space className="flex justify-between">
        <Space>
          <MdPayments className="text-2xl text-indigo-600" />
          <Title level={5} className="!mb-0">
            Payment Methods
          </Title>
        </Space>
        <Space>
          <Radio.Group
            value={timeRange}
            onChange={(e) => handleTimeRangeChange(e.target.value)}
            optionType="button"
            buttonStyle="solid"
            size="small"
          >
            <Radio.Button value={1}>1d</Radio.Button>
            <Radio.Button value={7}>7d</Radio.Button>
            <Radio.Button value={14}>14d</Radio.Button>
            <Radio.Button value={30}>30d</Radio.Button>
          </Radio.Group>
          <Button
            icon={<MdRefresh />}
            onClick={() => fetchPaymentDistribution({ days: timeRange })}
            size="small"
          />
        </Space>
      </Space>

      <Row gutter={[16, 16]}>
        <Col span={24}>
          <div className="h-[250px] w-full">
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={chartData}
                  cx="50%"
                  cy="50%"
                  innerRadius={60}
                  outerRadius={80}
                  paddingAngle={5}
                  dataKey="value"
                >
                  {chartData.map((_, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip
                  contentStyle={{
                    backgroundColor: isDarkMode ? '#1F2937' : '#FFFFFF',
                    border: `1px solid ${isDarkMode ? '#374151' : '#E5E7EB'}`,
                    borderRadius: '6px'
                  }}
                  formatter={(value: number) => formatCurrency(value)}
                />
                <Legend />
              </PieChart>
            </ResponsiveContainer>
          </div>
        </Col>

        <Col span={24}>
          <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-3">
            {data.map((item, index) => (
              <div
                key={item.method}
                className={`rounded-lg border shadow-md ${isDarkMode ? 'border-gray-800 bg-gray-900' : 'border-gray-200 bg-slate-50'} p-4`}
              >
                <Space>
                  <div
                    className="h-3 w-3 rounded-full"
                    style={{ backgroundColor: COLORS[index % COLORS.length] }}
                  />
                  <span className="text-sm font-medium">
                    {item.method.charAt(0) + item.method.slice(1).toLowerCase()}
                  </span>
                </Space>
                <div className="mt-2 text-lg font-semibold">{formatCurrency(item.amount)}</div>
                <div className="mt-1 text-xs text-gray-500">
                  {item.count} transactions ({((item.count / totalCount) * 100).toFixed(1)}%)
                </div>
              </div>
            ))}
          </div>
        </Col>
      </Row>
    </div>
  )
}

export default PaymentDistribution
