import { ReactNode } from 'react'

interface TransitionWrapperProps {
  children: ReactNode
  isVisible: boolean
  direction: 'left' | 'right' | 'top' | 'bottom'
}

const TransitionWrapper = ({ children, isVisible, direction }: TransitionWrapperProps) => {
  return (
    <div
      className={`absolute h-full w-full overflow-y-auto transition-all duration-500 ease-in-out ${
        isVisible
          ? 'translate-x-0 opacity-100'
          : `scale-0 opacity-0 ${
              direction === 'right'
                ? 'translate-x-full'
                : direction === 'left'
                  ? '-translate-x-full'
                  : direction === 'top'
                    ? 'translate-y-full'
                    : '-translate-y-full'
            }`
      }`}
    >
      {children}
    </div>
  )
}

export default TransitionWrapper
