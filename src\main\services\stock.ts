import { prisma } from '../db';
import { Prisma, StockStatus } from '@prisma/client';

interface GetStockParams {
    page: number;
    limit: number;
    search?: string;
    where?: Prisma.StockWhereInput;
}


// make suer the ui is aligned with this new approach
class StockService {
    // Helper method to handle common pagination and search logic
    private async getPaginatedStocks(where: Prisma.StockWhereInput, page: number, limit: number, includeOptions: any) {
        try {
            const [stocks, total] = await Promise.all([
                prisma.stock.findMany({
                    where,
                    include: includeOptions,
                    skip: (page - 1) * limit,
                    take: limit,
                    orderBy: { createdAt: 'desc' }
                }),
                prisma.stock.count({ where })
            ]);

            return { stocks, total, page, totalPages: Math.ceil(total / limit) };
        } catch (error) {
            console.error('Error fetching stocks:', error);
            throw new Error('Failed to fetch stocks');
        }
    }

    // Helper method to build search conditions
    private buildSearchCondition(search?: string): Prisma.StockWhereInput {
        return search ? {
            OR: [
                { product: { name: { contains: search, mode: 'insensitive' } } },
                { product: { productId: { contains: search, mode: 'insensitive' } } }
            ]
        } : {};
    }

    // Helper method to validate entity existence
    private async validateEntityExists(entityType: 'product' | 'vendor' | 'purchaseInvoice' | 'party', id: string): Promise<void> {
        const entity = await prisma[entityType].findUnique({ where: { id } });
        if (!entity) {
            throw new Error(`${entityType.charAt(0).toUpperCase() + entityType.slice(1)} not found`);
        }
    }

    async getAvailableStock({ page, limit, search, where = {} }: GetStockParams) {
        const finalWhere: Prisma.StockWhereInput = {
            ...where,
            status: StockStatus.IN_STOCK,
            ...this.buildSearchCondition(search)
        };

        return this.getPaginatedStocks(
            finalWhere,
            page,
            limit,
            {
                product: true,
                vendor: true,
                purchaseInvoice: true
            }
        );
    }

    async getSoldStock({ page, limit, search, where = {} }: GetStockParams) {
        const finalWhere: Prisma.StockWhereInput = {
            ...where,
            status: StockStatus.SOLD_OUT,
            ...this.buildSearchCondition(search)
        };

        return this.getPaginatedStocks(
            finalWhere,
            page,
            limit,
            {
                product: true,
                vendor: true,
                purchaseInvoice: true,
                StockEntry: {
                    include: {
                        saleItem: {
                            include: {
                                saleInvoice: true
                            }
                        },
                        WalkInSaleItem: {
                            include: {
                                saleInvoice: true
                            }
                        }
                    }
                }
            }
        );
    }

    async getStockByProduct(productId: string, { page, limit, status }: GetStockParams & { status?: StockStatus }) {
        // Validate product exists
        await this.validateEntityExists('product', productId);

        const where: Prisma.StockWhereInput = {
            productId,
            ...(status ? { status } : {})
        };

        return this.getPaginatedStocks(
            where,
            page,
            limit,
            {
                vendor: true,
                purchaseInvoice: true,
                StockEntry: {
                    include: {
                        saleItem: {
                            include: {
                                saleInvoice: true
                            }
                        },
                        WalkInSaleItem: {
                            include: {
                                saleInvoice: true
                            }
                        }
                    }
                }
            }
        );
    }

    async getStockByVendor(vendorId: string, { page, limit, status }: GetStockParams & { status?: StockStatus }) {
        // Validate vendor exists
        await this.validateEntityExists('party', vendorId);

        const where: Prisma.StockWhereInput = {
            vendorId,
            ...(status ? { status } : {})
        };

        return this.getPaginatedStocks(
            where,
            page,
            limit,
            {
                product: true,
                purchaseInvoice: true,
                StockEntry: status === StockStatus.SOLD_OUT ? {
                    include: {
                        saleItem: {
                            include: {
                                saleInvoice: true
                            }
                        },
                        WalkInSaleItem: {
                            include: {
                                saleInvoice: true
                            }
                        }
                    }
                } : false
            }
        );
    }

    async getStockByPurchaseInvoice(purchaseInvoiceId: string, { page, limit, status }: GetStockParams & { status?: StockStatus }) {
        // Validate purchase invoice exists
        await this.validateEntityExists('purchaseInvoice', purchaseInvoiceId);

        const where: Prisma.StockWhereInput = {
            purchaseInvoiceId,
            ...(status ? { status } : {})
        };

        return this.getPaginatedStocks(
            where,
            page,
            limit,
            {
                product: true,
                vendor: true,
                StockEntry: status === StockStatus.SOLD_OUT ? {
                    include: {
                        saleItem: {
                            include: {
                                saleInvoice: true
                            }
                        },
                        WalkInSaleItem: {
                            include: {
                                saleInvoice: true
                            }
                        }
                    }
                } : false
            }
        );
    }

    // Helper method to validate stock quantities
    validateStockQuantity(quantity: number): void {
        if (quantity <= 0) {
            throw new Error('Stock quantity must be greater than zero');
        }
    }
}

export const stockService = new StockService();