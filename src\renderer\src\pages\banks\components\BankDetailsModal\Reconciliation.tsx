// src/renderer/src/pages/banks/components/BankDetailsModal/Reconciliation.tsx
import { useEffect } from 'react'
import { Card, Statistic, Row, Col, Button, App } from 'antd'
import { CheckCircleOutlined, CloseCircleOutlined } from '@ant-design/icons'
import { useApi } from '@/renderer/hooks'
import { bankLedgerApi } from '@/renderer/services'
import { BankReconciliation } from '@/common/types'

interface ReconciliationProps {
  bankId: string
}

export const Reconciliation = ({ bankId }: ReconciliationProps) => {
  const { message } = App.useApp()
  const { request, data, isLoading } = useApi<BankReconciliation, [string]>(
    bankLedgerApi.reconcileBank
  )

  useEffect(() => {
    loadReconciliation()
  }, [bankId])

  const loadReconciliation = async () => {
    await request(bankId)
  }

  if (isLoading) {
    return <div>Loading...</div>
  }

  if (!data) {
    return <div>No reconciliation data available</div>
  }

  return (
    <Card>
      <Row gutter={16}>
        <Col span={8}>
          <Statistic title="Current Balance" value={data.currentBalance} prefix="Rs. " />
        </Col>
        <Col span={8}>
          <Statistic title="Calculated Balance" value={data.calculatedBalance} prefix="Rs. " />
        </Col>
        <Col span={8}>
          <Statistic
            title="Difference"
            value={data.difference}
            prefix="Rs. "
            valueStyle={{ color: data.isReconciled ? '#3f8600' : '#cf1322' }}
          />
        </Col>
      </Row>

      <div className="mt-4 text-center">
        {data.isReconciled ? (
          <div className="text-green-600">
            <CheckCircleOutlined className="mr-2" />
            Balances are reconciled
          </div>
        ) : (
          <div className="text-red-600">
            <CloseCircleOutlined className="mr-2" />
            Balances are not reconciled
          </div>
        )}
      </div>

      <div className="mt-4 text-center">
        <Button type="primary" onClick={loadReconciliation}>
          Refresh Reconciliation
        </Button>
      </div>
    </Card>
  )
}
