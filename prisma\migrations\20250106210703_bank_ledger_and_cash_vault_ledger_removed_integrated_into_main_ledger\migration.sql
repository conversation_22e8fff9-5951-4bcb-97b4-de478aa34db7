/*
  Warnings:

  - The values [Bank<PERSON>rans<PERSON>,CashTransfer] on the enum `LedgerType` will be removed. If these variants are still used in the database, this will fail.
  - You are about to drop the column `bankLedgerRef` on the `Ledger` table. All the data in the column will be lost.
  - You are about to drop the column `cashAccountId` on the `Ledger` table. All the data in the column will be lost.
  - You are about to drop the column `cashVaultId` on the `Ledger` table. All the data in the column will be lost.
  - You are about to drop the column `cashVaultLedgerRef` on the `Ledger` table. All the data in the column will be lost.
  - You are about to drop the `BankLedger` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `CashVaultLedger` table. If the table is not empty, all the data it contains will be lost.

*/
-- AlterEnum
BEGIN;
CREATE TYPE "LedgerType_new" AS ENUM ('PurchaseInvoice', 'SaleInvoice', 'Payment', 'OpeningBalance', 'Expense', 'BankDeposit', 'BankWithdrawal', 'VaultDeposit', 'VaultWithdrawal', 'Transfer');
ALTER TABLE "Ledger" ALTER COLUMN "referenceType" TYPE "LedgerType_new" USING ("referenceType"::text::"LedgerType_new");
ALTER TYPE "LedgerType" RENAME TO "LedgerType_old";
ALTER TYPE "LedgerType_new" RENAME TO "LedgerType";
DROP TYPE "LedgerType_old";
COMMIT;

-- DropForeignKey
ALTER TABLE "BankLedger" DROP CONSTRAINT "BankLedger_bankId_fkey";

-- DropForeignKey
ALTER TABLE "BankLedger" DROP CONSTRAINT "BankLedger_paymentRef_fkey";

-- DropForeignKey
ALTER TABLE "CashVaultLedger" DROP CONSTRAINT "CashVaultLedger_paymentRef_fkey";

-- DropForeignKey
ALTER TABLE "Ledger" DROP CONSTRAINT "Ledger_bankLedgerRef_fkey";

-- DropForeignKey
ALTER TABLE "Ledger" DROP CONSTRAINT "Ledger_cashAccountId_fkey";

-- DropForeignKey
ALTER TABLE "Ledger" DROP CONSTRAINT "Ledger_cashVaultId_fkey";

-- DropForeignKey
ALTER TABLE "Ledger" DROP CONSTRAINT "Ledger_cashVaultLedgerRef_fkey";

-- DropIndex
DROP INDEX "Ledger_bankLedgerRef_idx";

-- DropIndex
DROP INDEX "Ledger_bankLedgerRef_key";

-- DropIndex
DROP INDEX "Ledger_cashAccountId_idx";

-- DropIndex
DROP INDEX "Ledger_cashVaultId_idx";

-- DropIndex
DROP INDEX "Ledger_cashVaultLedgerRef_idx";

-- DropIndex
DROP INDEX "Ledger_cashVaultLedgerRef_key";

-- DropIndex
DROP INDEX "Ledger_expenseRef_idx";

-- DropIndex
DROP INDEX "Ledger_partyId_referenceType_idx";

-- DropIndex
DROP INDEX "Ledger_paymentRef_idx";

-- DropIndex
DROP INDEX "Ledger_purchaseRef_idx";

-- DropIndex
DROP INDEX "Ledger_saleRef_idx";

-- AlterTable
ALTER TABLE "Ledger" DROP COLUMN "bankLedgerRef",
DROP COLUMN "cashAccountId",
DROP COLUMN "cashVaultId",
DROP COLUMN "cashVaultLedgerRef";

-- DropTable
DROP TABLE "BankLedger";

-- DropTable
DROP TABLE "CashVaultLedger";

-- DropEnum
DROP TYPE "BankTransactionType";

-- DropEnum
DROP TYPE "CashTransactionType";

-- CreateIndex
CREATE INDEX "Ledger_partyId_idx" ON "Ledger"("partyId");
