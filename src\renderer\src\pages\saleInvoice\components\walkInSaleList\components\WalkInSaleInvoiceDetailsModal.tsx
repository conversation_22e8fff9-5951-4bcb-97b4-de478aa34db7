import React from 'react'
import { Modal, Typography, Descriptions, Table, Tag, Tooltip, Divider, Space } from 'antd'
import { TagOutlined, ShopOutlined, InfoCircleOutlined, UserOutlined } from '@ant-design/icons'
import dayjs from 'dayjs'

const { Text, Title } = Typography

interface SaleItem {
  id: string
  totalQuantity: number
  salePrice: number
  total: number
  totalProfit: number
  product: {
    name: string
    productId: string
    tag: string
    nature: string
    category: {
      name: string
    }
  }
}

interface WalkInSaleInvoice {
  id: string
  invoiceNumber: string
  date: string | Date
  customerName: string | null
  totalAmount: number
  discountAmount: number
  paidAmount: number
  totalProfit: number
  status: string
  createdAt: string | Date
  payment: {
    paymentMethod: string
  }
  items: SaleItem[]
  createdBy: {
    name: string
  }
  voidedBy?: {
    name: string
  }
  voidedAt?: string | Date
  voidingReason?: string
}

interface WalkInSaleInvoiceDetailsModalProps {
  visible: boolean
  onClose: () => void
  invoice: WalkInSaleInvoice | null
}

const WalkInSaleInvoiceDetailsModal: React.FC<WalkInSaleInvoiceDetailsModalProps> = ({
  visible,
  onClose,
  invoice
}) => {
  if (!invoice) return null

  const itemColumns = [
    {
      title: 'Sr. No.',
      dataIndex: 'index',
      key: 'index',
      width: 70,
      render: (_, __, index) => index + 1
    },
    {
      title: 'Product Details',
      dataIndex: 'product',
      key: 'product',
      render: (product) => (
        <Space direction="horizontal" size="small" wrap>
          <Tag>{product.name}</Tag>
          <Tag icon={<TagOutlined />} color="blue">
            {product.productId}
          </Tag>
          <Tag icon={<ShopOutlined />} color="cyan">
            {product.tag}
          </Tag>
          <Tag color="purple">{product.nature}</Tag>
        </Space>
      )
    },
    {
      title: 'Quantity',
      dataIndex: 'totalQuantity',
      key: 'totalQuantity',
      width: 100
    },
    {
      title: 'Sale Price',
      dataIndex: 'salePrice',
      key: 'salePrice',
      width: 120,
      render: (price: number) => price.toLocaleString('en-US', { minimumFractionDigits: 2 })
    },
    {
      title: 'Total',
      dataIndex: 'total',
      key: 'total',
      width: 150,
      render: (total: number) => (
        <Text strong>{total.toLocaleString('en-US', { minimumFractionDigits: 2 })}</Text>
      )
    },
    {
      title: 'Profit',
      dataIndex: 'totalProfit',
      key: 'totalProfit',
      width: 150,
      render: (profit: number) => (
        <Tag color={profit >= 0 ? 'success' : 'error'}>
          {profit >= 0 ? 'Profit: ' : 'Loss: '}
          {Math.abs(profit).toLocaleString('en-US', { minimumFractionDigits: 2 })}
        </Tag>
      )
    }
  ]

  const netAmount = invoice.totalAmount - invoice.discountAmount

  return (
    <Modal
      open={visible}
      onCancel={onClose}
      footer={null}
      width={1000}
      title={
        <Space align="center">
          <InfoCircleOutlined />
          <span>Walk-in Sales Invoice Details</span>
        </Space>
      }
      bodyStyle={{ maxHeight: '80vh', overflow: 'auto' }}
    >
      <div className="mb-6">
        <Descriptions bordered column={2} size="small">
          <Descriptions.Item label="Invoice Number" span={1}>
            <Text strong>{invoice.invoiceNumber}</Text>
          </Descriptions.Item>
          <Descriptions.Item label="Date" span={1}>
            {dayjs(invoice.date).format('DD/MM/YYYY HH:mm')}
          </Descriptions.Item>
          <Descriptions.Item label="Customer" span={2}>
            <Space>
              <UserOutlined className="text-purple-500" />
              <Text strong>{invoice.customerName || 'Walk-in Customer'}</Text>
              <Tag color="purple">WALK-IN</Tag>
            </Space>
          </Descriptions.Item>
          <Descriptions.Item label="Total Amount" span={1}>
            <Text strong className="text-lg">
              {invoice.totalAmount.toLocaleString('en-US', { minimumFractionDigits: 2 })}
            </Text>
          </Descriptions.Item>
          <Descriptions.Item label="Discount" span={1}>
            <Text type="danger">
              -{invoice.discountAmount.toLocaleString('en-US', { minimumFractionDigits: 2 })}
            </Text>
          </Descriptions.Item>
          <Descriptions.Item label="Net Amount" span={1}>
            <Text strong className="text-lg text-indigo-600">
              {netAmount.toLocaleString('en-US', { minimumFractionDigits: 2 })}
            </Text>
          </Descriptions.Item>
          <Descriptions.Item label="Paid Amount" span={1}>
            <Text strong className="text-lg text-green-600">
              {invoice.paidAmount.toLocaleString('en-US', { minimumFractionDigits: 2 })}
            </Text>
          </Descriptions.Item>
          <Descriptions.Item label="Payment Method" span={1}>
            <Tag color="blue">{invoice.payment.paymentMethod}</Tag>
          </Descriptions.Item>
          <Descriptions.Item label="Total Profit" span={1}>
            <Tag color={invoice.totalProfit >= 0 ? 'success' : 'error'}>
              {invoice.totalProfit >= 0 ? 'Profit: ' : 'Loss: '}
              {Math.abs(invoice.totalProfit).toLocaleString('en-US', { minimumFractionDigits: 2 })}
            </Tag>
          </Descriptions.Item>
          <Descriptions.Item label="Status" span={1}>
            <Tag color={invoice.status === 'ACTIVE' ? 'green' : 'red'}>{invoice.status}</Tag>
          </Descriptions.Item>
          <Descriptions.Item label="Created By" span={1}>
            {invoice.createdBy.name}
          </Descriptions.Item>
        </Descriptions>

        {invoice.voidingReason && (
          <div className="mt-4">
            <Title level={5} className="text-red-500">
              Void Information:
            </Title>
            <Descriptions bordered size="small">
              <Descriptions.Item label="Voided By" span={1}>
                {invoice.voidedBy?.name}
              </Descriptions.Item>
              <Descriptions.Item label="Voided At" span={2}>
                {invoice.voidedAt && dayjs(invoice.voidedAt).format('DD/MM/YYYY HH:mm')}
              </Descriptions.Item>
              <Descriptions.Item label="Reason" span={3}>
                {invoice.voidingReason}
              </Descriptions.Item>
            </Descriptions>
          </div>
        )}
      </div>

      <Divider orientation="left">Items ({invoice.items.length})</Divider>

      <div style={{ maxHeight: '300px', overflow: 'auto' }}>
        <Table
          columns={itemColumns}
          dataSource={invoice.items}
          pagination={false}
          rowKey="id"
          size="small"
          bordered
          sticky
          scroll={{ y: 240 }}
          summary={(pageData) => {
            const total = pageData.reduce((sum, item) => sum + item.total, 0)
            return (
              <Table.Summary.Row>
                <Table.Summary.Cell index={0} colSpan={4} align="right">
                  <Text strong>Total:</Text>
                </Table.Summary.Cell>
                <Table.Summary.Cell index={1} colSpan={2}>
                  <Text strong className="text-indigo-600">
                    {total.toLocaleString('en-US', { minimumFractionDigits: 2 })}
                  </Text>
                </Table.Summary.Cell>
              </Table.Summary.Row>
            )
          }}
        />
      </div>
    </Modal>
  )
}

export default WalkInSaleInvoiceDetailsModal
