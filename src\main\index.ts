import { app, shell, BrowserWindow, ipc<PERSON>ain, dialog } from 'electron'
import { join } from 'path'
import { electronApp, optimizer, is } from '@electron-toolkit/utils'
import icon from '../../resources/icon.png?asset'
import "./router";

import { initDatabase } from './db'
import { initializeApp, isSchemaInitialized, showSchemaErrorDialog } from './setup';

import fs from 'fs'
import path from 'path'
import { tmpdir } from 'os'

const isLinux = process.platform === 'linux'

let mainWindow: BrowserWindow | null = null;

// Prevent multiple instances of the app
const gotTheLock = app.requestSingleInstanceLock();

if (!gotTheLock) {
  app.quit();
} else {
  app.on('second-instance', () => {
    // Someone tried to run a second instance, we should focus our window.
    if (mainWindow) {
      if (mainWindow.isMinimized()) mainWindow.restore();
      mainWindow.focus();
    }
  });
}

function createWindow(): void {
  mainWindow = new BrowserWindow({
    width: 900,
    height: 670,
    show: false,
    autoHideMenuBar: true,
    ...(isLinux ? { icon } : {}),
    webPreferences: {
      preload: join(__dirname, '../preload/index.js'),
      sandbox: false
    }
  })

  mainWindow.on('ready-to-show', () => {
    mainWindow?.show()

    // Show dialog about schema initialization if needed
    if (!isSchemaInitialized && mainWindow) {
      showSchemaErrorDialog(mainWindow);
    }
  })

  mainWindow.on('close', (e) => {
    if (mainWindow) {
      e.preventDefault();
      mainWindow.webContents.send('app-closing');
      setTimeout(() => {
        mainWindow = null;
        app.quit();
      }, 100);
    }
  })

  mainWindow.webContents.setWindowOpenHandler((details) => {
    shell.openExternal(details.url)
    return { action: 'deny' }
  })

  if (is.dev && process.env['ELECTRON_RENDERER_URL']) {
    mainWindow.loadURL(process.env['ELECTRON_RENDERER_URL'])
  } else {
    mainWindow.loadFile(join(__dirname, '../renderer/index.html'))
  }
}

// Function to print PDF using hidden window and temporary file
async function printPDF(dataUri: string): Promise<boolean> {
  return new Promise(async (resolve) => {
    try {
      // Create a temporary file path
      const tempFilePath = path.join(tmpdir(), `temp-print-${Date.now()}.pdf`);

      // Extract base64 data from the data URI
      let base64Data = dataUri;
      if (dataUri.includes(';base64,')) {
        base64Data = dataUri.split(';base64,')[1];
      } else if (dataUri.includes(',')) {
        base64Data = dataUri.split(',')[1];
      }

      // Write the PDF data to the temporary file
      await fs.promises.writeFile(tempFilePath, Buffer.from(base64Data, 'base64') as unknown as Uint8Array);

      // Create a window to load the PDF file
      const pdfWindow = new BrowserWindow({
        width: 800,
        height: 900,
        show: false,
        webPreferences: {
          plugins: true,
          javascript: true
        }
      });

      let hasResolved = false;

      const cleanup = (success: boolean) => {
        if (!hasResolved) {
          hasResolved = true;

          // Clean up the temporary file
          fs.unlink(tempFilePath, (err) => {
            if (err) console.error('Error deleting temporary PDF file:', err);
          });

          resolve(success);
        }
      };

      // Handle load errors
      pdfWindow.webContents.on('did-fail-load', (error) => {
        console.error('Failed to load PDF:', error);
        if (!pdfWindow.isDestroyed()) {
          pdfWindow.close();
        }
        cleanup(false);
      });

      // Handle PDF loading
      pdfWindow.webContents.on('did-finish-load', () => {
        pdfWindow.show();
        cleanup(true);
      });

      // Handle window closing
      pdfWindow.on('closed', () => {
        cleanup(true);
      });

      // Load the PDF from the file path using file:// protocol
      const fileUrl = `file://${tempFilePath}`;
      pdfWindow.loadURL(fileUrl).catch((error) => {
        console.error('Error loading PDF:', error);
        if (!pdfWindow.isDestroyed()) {
          pdfWindow.close();
        }
        cleanup(false);
      });

      // Add a timeout in case loading fails
      setTimeout(() => {
        if (!hasResolved) {
          if (!pdfWindow.isDestroyed()) {
            pdfWindow.close();
          }
          cleanup(false);
        }
      }, 30000); // 30 second timeout
    } catch (error) {
      console.error('Error in printPDF:', error);
      resolve(false);
    }
  });
}


app.whenReady().then(async () => {
  try {
    // Initialize database
    await initDatabase()

    // Initialize app
    await initializeApp()

    electronApp.setAppUserModelId('com.sj-lace.app')
    app.on('browser-window-created', (_, window) => {
      optimizer.watchWindowShortcuts(window)
    })

    createWindow()

    // Add these handlers in your IPC setup section
    ipcMain.handle('show-save-dialog', async (_event, options) => {
      const result = await dialog.showSaveDialog(options)
      return result.filePath
    })

    ipcMain.handle('save-pdf', async (_event, { path, data }) => {
      try {
        await fs.promises.writeFile(path, Buffer.from(data) as unknown as Uint8Array)
        return true
      } catch (error) {
        console.error('Error saving PDF:', error)
        throw error
      }
    })

    // Add new handler for printing PDFs
    ipcMain.handle('print-pdf', async (_event, dataUri) => {
      return await printPDF(dataUri)
    })

    // Add handler to check schema initialization status
    ipcMain.handle('check-schema-initialized', () => {
      return isSchemaInitialized
    })

  } catch (error: any) {
    console.error('Failed to initialize application:', error)

    // Allow the application to start even with initialization errors
    // We'll show the error message on the login screen
    electronApp.setAppUserModelId('com.sj-lace.app')
    app.on('browser-window-created', (_, window) => {
      optimizer.watchWindowShortcuts(window)
    })

    createWindow()

    // Still register IPC handlers for backup functionality
    ipcMain.handle('show-save-dialog', async (_event, options) => {
      const result = await dialog.showSaveDialog(options)
      return result.filePath
    })

    ipcMain.handle('save-pdf', async (_event, { path, data }) => {
      try {
        await fs.promises.writeFile(path, Buffer.from(data) as unknown as Uint8Array)
        return true
      } catch (error) {
        console.error('Error saving PDF:', error)
        throw error
      }
    })

    ipcMain.handle('print-pdf', async (_event, dataUri) => {
      return await printPDF(dataUri)
    })

    ipcMain.handle('check-schema-initialized', () => {
      return false
    })
  }
})

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit()
  }
})

app.on('activate', () => {
  if (mainWindow === null) createWindow()
})
