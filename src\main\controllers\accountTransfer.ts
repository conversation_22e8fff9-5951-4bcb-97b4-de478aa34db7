import { IRequest } from '../../common';
import { accountTransferService } from '../services';
import {
    CreateAccountTransferData,
    GetAccountTransfersParams,
    VoidAccountTransferParams
} from '../../common/types/accountTransfer';

class AccountTransferController {
    async createAccountTransfer(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const data = req.body as CreateAccountTransferData;

        // Validation
        if (!data.amount || data.amount <= 0) {
            throw new Error('Amount is required and must be greater than zero');
        }

        if (!data.fromPartyId) {
            throw new Error('From party is required');
        }

        if (!data.toPartyId) {
            throw new Error('To party is required');
        }

        if (data.fromPartyId === data.toPartyId) {
            throw new Error('Cannot transfer to the same party');
        }

        if (!data.transferDate) {
            throw new Error('Transfer date is required');
        }

        if (!data.createdById) {
            throw new Error('Created by ID is required');
        }

        return await accountTransferService.createAccountTransfer({
            ...data,
            transferDate: new Date(data.transferDate)
        });
    }

    async getAccountTransfers(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const params = req.body as GetAccountTransfersParams;

        // Convert date strings to Date objects if provided
        const processedParams = {
            ...params,
            startDate: params.startDate ? new Date(params.startDate) : undefined,
            endDate: params.endDate ? new Date(params.endDate) : undefined
        };

        return await accountTransferService.getAccountTransfers(processedParams);
    }

    async getAccountTransferById(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { id } = req.params || {};

        if (!id) {
            throw new Error('Account transfer ID is required');
        }

        const transfer = await accountTransferService.getAccountTransferById(id);

        if (!transfer) {
            throw new Error('Account transfer not found');
        }

        return transfer;
    }

    async voidAccountTransfer(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const data = req.body as VoidAccountTransferParams;

        if (!data.id) {
            throw new Error('Account transfer ID is required');
        }

        if (!data.deletedById) {
            throw new Error('Deleted by ID is required');
        }

        if (!data.deletionReason || data.deletionReason.trim() === '') {
            throw new Error('Deletion reason is required');
        }

        return await accountTransferService.voidAccountTransfer(data);
    }
}

export const accountTransferController = new AccountTransferController();