import { useState, useEffect } from 'react'
import {
  Table,
  Space,
  Button,
  DatePicker,
  Select,
  Input,
  Form,
  Card,
  Typography,
  Tag,
  Tooltip,
  Modal,
  Spin,
  App
} from 'antd'
import {
  SearchOutlined,
  ReloadOutlined,
  EyeOutlined,
  PrinterOutlined,
  DeleteOutlined
} from '@ant-design/icons'
import { vendorReturnApi } from '@/renderer/services'
import { useApi } from '@/renderer/hooks'
import { usePartyContext } from '@/renderer/contexts'
import dayjs from 'dayjs'
import VoidConfirmationModal from './VoidConfirmationModal'
import { IRootState } from '@/renderer/redux'
import { useSelector } from 'react-redux'
import {
  VendorReturnsResponse,
  VendorReturnDisplay,
  VendorReturnItemDisplay,
  GetVendorReturnsParams,
  Status
} from '@/common/types'
import { BarcodeOutlined, TagsOutlined } from '@ant-design/icons'

const { Title, Text } = Typography
const { RangePicker } = DatePicker

export interface VendorReturnListProps {
  refreshTrigger: number
}

const VendorReturnList = ({ refreshTrigger }: VendorReturnListProps) => {
  const [form] = Form.useForm()
  const [dateRange, setDateRange] = useState<[dayjs.Dayjs, dayjs.Dayjs] | null>(null)
  const [vendorId, setVendorId] = useState<string | null>(null)
  const [status, setStatus] = useState<Status | 'ALL'>(Status.ACTIVE)
  const [pagination, setPagination] = useState({ current: 1, pageSize: 10 })
  const [selectedReturn, setSelectedReturn] = useState<VendorReturnDisplay | null>(null)
  const [isVoidModalOpen, setIsVoidModalOpen] = useState(false)
  const [isDetailsModalOpen, setIsDetailsModalOpen] = useState(false)
  const [isVoiding, setIsVoiding] = useState(false)

  const { vendors } = usePartyContext()

  const { message } = App.useApp()

  const user = useSelector((state: IRootState) => state.user.data)

  const statusOptions = [
    { label: 'Active', value: 'ACTIVE' },
    { label: 'Void', value: 'VOID' },
    { label: 'All', value: 'ALL' }
  ]

  const {
    data,
    isLoading,
    error,
    request: fetchVendorReturns
  } = useApi<VendorReturnsResponse, [GetVendorReturnsParams]>(vendorReturnApi.getVendorReturns)

  console.log(data)

  useEffect(() => {
    fetchData()
  }, [refreshTrigger, pagination.current, pagination.pageSize, status])

  const fetchData = async () => {
    await fetchVendorReturns({
      startDate: dateRange?.[0]?.toDate(),
      endDate: dateRange?.[1]?.toDate(),
      vendorId: vendorId || undefined,
      status: status === 'ALL' ? undefined : status,
      page: pagination.current,
      limit: pagination.pageSize
    })
  }

  const handleSearch = async () => {
    setPagination({ ...pagination, current: 1 })
    await fetchData()
  }

  const handleReset = () => {
    form.resetFields()
    setDateRange(null)
    setVendorId(null)
    setStatus(Status.ACTIVE)
    setPagination({ current: 1, pageSize: 10 })
    fetchData()
  }

  const handleTableChange = (pagination: any) => {
    setPagination({
      current: pagination.current,
      pageSize: pagination.pageSize
    })
  }

  const showVoidConfirm = (record: VendorReturnDisplay) => {
    setSelectedReturn(record)
    setIsVoidModalOpen(true)
  }

  const handleVoidConfirm = async (reason: string) => {
    if (!selectedReturn) return

    setIsVoiding(true)
    const response = await vendorReturnApi.voidVendorReturn({
      returnInvoiceId: selectedReturn.id,
      adminId: user?.id || '',
      reason
    })

    setIsVoiding(false)

    if (response.error.error || response.data.error) {
      message.error(response.error.message || response.data.error.message)
      return
    }

    setIsVoidModalOpen(false)
    fetchData()
    message.success('Vendor return voided successfully')
  }

  const handleVoidCancel = () => {
    setIsVoidModalOpen(false)
    setSelectedReturn(null)
  }

  const showDetailsModal = (record: VendorReturnDisplay) => {
    setSelectedReturn(record)
    setIsDetailsModalOpen(true)
  }

  const columns = [
    {
      title: 'Sr. No.',
      dataIndex: 'index',
      key: 'index',
      render: (_, __, index) => (pagination.current - 1) * pagination.pageSize + index + 1
    },
    {
      title: 'Invoice Number',
      dataIndex: 'invoiceNumber',
      key: 'invoiceNumber',
      render: (text: string) => <a>{text}</a>
    },
    {
      title: 'Date',
      dataIndex: 'date',
      key: 'date',
      render: (date: string) => dayjs(date).format('DD/MM/YYYY')
    },
    {
      title: 'Vendor',
      dataIndex: ['vendor', 'name'],
      key: 'vendor'
    },
    {
      title: 'Total Amount',
      dataIndex: 'totalAmount',
      key: 'totalAmount',
      render: (amount: number) => `$${amount.toFixed(2)}`
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status: Status) => (
        <Tag color={status === Status.ACTIVE ? 'green' : 'red'}>{status}</Tag>
      )
    },
    {
      title: 'Created By',
      dataIndex: ['createdBy', 'name'],
      key: 'createdBy'
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_: any, record: VendorReturnDisplay) => (
        <Space>
          <Tooltip title="View Details">
            <Button icon={<EyeOutlined />} onClick={() => showDetailsModal(record)} size="small" />
          </Tooltip>
          {record.status === Status.ACTIVE && (
            <Tooltip title="Void Return">
              <Button
                danger
                icon={<DeleteOutlined />}
                onClick={() => showVoidConfirm(record)}
                size="small"
              />
            </Tooltip>
          )}
        </Space>
      )
    }
  ]

  const expandedRowRender = (record: VendorReturnDisplay) => (
    <Table
      columns={[
        {
          title: 'Product Details',
          dataIndex: ['product'],
          key: 'product',
          render: (product) => (
            <Space direction="vertical" size="small">
              <Space wrap>
                <Tag>{product.name}</Tag>
                <Tooltip title="Product ID">
                  <Tag color="blue" icon={<BarcodeOutlined />}>
                    {product.productId}
                  </Tag>
                </Tooltip>
                <Tooltip title="Product Tag">
                  <Tag color="cyan" icon={<TagsOutlined />}>
                    {product.tag || 'N/A'}
                  </Tag>
                </Tooltip>
                <Tooltip title="Product Nature">
                  <Tag color="purple">{product.nature || 'N/A'}</Tag>
                </Tooltip>
                <Tooltip title="Product Category">
                  <Tag color="gold">{product.category.name || 'N/A'}</Tag>
                </Tooltip>
              </Space>
              <Space></Space>
            </Space>
          )
        },
        {
          title: 'Quantity',
          dataIndex: 'quantity',
          key: 'quantity'
        },
        {
          title: 'Purchase Price',
          dataIndex: 'purchasePrice',
          key: 'purchasePrice',
          render: (price: number) => `$${price.toFixed(2)}`
        },
        {
          title: 'Total',
          key: 'total',
          render: (_, record) => `$${(record.quantity * record.purchasePrice).toFixed(2)}`
        }
      ]}
      dataSource={record.items}
      pagination={false}
      rowKey="id"
    />
  )

  return (
    <div className="space-y-4">
      <Card>
        <Form form={form} layout="vertical" onFinish={handleSearch}>
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Form.Item label="Date Range">
              <RangePicker
                value={dateRange}
                onChange={(dates) => setDateRange(dates as [dayjs.Dayjs, dayjs.Dayjs])}
                className="w-full"
              />
            </Form.Item>
            <Form.Item label="Vendor">
              <Select
                allowClear
                placeholder="Select vendor"
                value={vendorId}
                onChange={setVendorId}
                options={vendors}
                className="w-full"
              />
            </Form.Item>
            <Form.Item label="Status">
              <Select
                value={status}
                onChange={setStatus}
                options={statusOptions}
                className="w-full"
              />
            </Form.Item>
            <Form.Item className="flex items-end">
              <Space>
                <Button type="primary" htmlType="submit" icon={<SearchOutlined />}>
                  Search
                </Button>
                <Button onClick={handleReset} icon={<ReloadOutlined />}>
                  Reset
                </Button>
              </Space>
            </Form.Item>
          </div>
        </Form>
      </Card>

      <Table
        columns={columns}
        dataSource={data?.returns}
        rowKey="id"
        loading={isLoading}
        pagination={{
          current: pagination.current,
          pageSize: pagination.pageSize,
          total: data?.total || 0,
          showSizeChanger: true
        }}
        onChange={handleTableChange}
        expandable={{
          expandedRowRender
        }}
      />

      <VoidConfirmationModal
        open={isVoidModalOpen}
        loading={isVoiding}
        onConfirm={handleVoidConfirm}
        onCancel={handleVoidCancel}
      />

      {selectedReturn && (
        <Modal
          title="Vendor Return Details"
          open={isDetailsModalOpen}
          onCancel={() => setIsDetailsModalOpen(false)}
          footer={null}
          width={800}
        >
          {isLoading ? (
            <div className="flex justify-center p-10">
              <Spin size="large" />
            </div>
          ) : (
            <div className="space-y-4">
              <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                <div>
                  <Text strong>Invoice Number:</Text> {selectedReturn.invoiceNumber}
                </div>
                <div>
                  <Text strong>Date:</Text> {dayjs(selectedReturn.date).format('DD/MM/YYYY')}
                </div>
                <div>
                  <Text strong>Vendor:</Text> {selectedReturn.vendor.name}
                </div>
                <div>
                  <Text strong>Total Amount:</Text> ${selectedReturn.totalAmount.toFixed(2)}
                </div>
                <div>
                  <Text strong>Status:</Text>{' '}
                  <Tag color={selectedReturn.status === Status.ACTIVE ? 'green' : 'red'}>
                    {selectedReturn.status}
                  </Tag>
                </div>
                <div>
                  <Text strong>Created By:</Text> {selectedReturn.createdBy.name}
                </div>
              </div>

              {selectedReturn.status === Status.VOID && (
                <div className="rounded-md bg-red-50 p-4">
                  <Text strong className="text-red-800">
                    Voiding Reason:
                  </Text>{' '}
                  <Text className="text-red-700">{selectedReturn.voidingReason}</Text>
                </div>
              )}

              <Title level={5}>Returned Items</Title>
              <Table
                columns={[
                  {
                    title: 'Product ID',
                    dataIndex: ['product', 'productId'],
                    key: 'productId'
                  },
                  {
                    title: 'Product Name',
                    dataIndex: ['product', 'name'],
                    key: 'productName'
                  },
                  {
                    title: 'Quantity',
                    dataIndex: 'quantity',
                    key: 'quantity'
                  },
                  {
                    title: 'Purchase Price',
                    dataIndex: 'purchasePrice',
                    key: 'purchasePrice',
                    render: (price: number) => `$${price.toFixed(2)}`
                  },
                  {
                    title: 'Total',
                    key: 'total',
                    render: (_, record) => `$${(record.quantity * record.purchasePrice).toFixed(2)}`
                  }
                ]}
                dataSource={selectedReturn.items}
                pagination={false}
                rowKey="id"
              />
            </div>
          )}
        </Modal>
      )}
    </div>
  )
}

export default VendorReturnList
