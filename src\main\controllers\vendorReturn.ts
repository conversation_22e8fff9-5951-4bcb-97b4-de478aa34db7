import { IRequest } from '@/common/types';
import { vendorReturnService } from '../services';
import {
    ProcessVendorReturnParams,
    VoidVendorReturnParams,
    GetVendorReturnsParams
} from '@/common/types/vendorReturn';

class VendorReturnController {
    async processVendorReturn(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { vendorId, items, adminId, paymentDetails, description } = req.body ?? {};
        const params: ProcessVendorReturnParams = { vendorId, items, adminId, paymentDetails, description };
        const vendorReturn = await vendorReturnService.processVendorReturn(params);
        return vendorReturn;
    }

    async voidVendorReturn(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { returnInvoiceId, adminId, reason } = req.body ?? {};
        const params: VoidVendorReturnParams = { returnInvoiceId, adminId, reason };
        const vendorReturn = await vendorReturnService.voidVendorReturn(params);
        return vendorReturn;
    }

    async getVendorReturns(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { startDate, endDate, vendorId, productId, status, page, limit } = req.query ?? {};
        const params: GetVendorReturnsParams = {
            startDate: startDate ? new Date(startDate) : undefined,
            endDate: endDate ? new Date(endDate) : undefined,
            vendorId,
            productId,
            status,
            page,
            limit
        };
        const vendorReturns = await vendorReturnService.getVendorReturns(params);
        return vendorReturns;
    }
}

export const vendorReturnController = new VendorReturnController(); 