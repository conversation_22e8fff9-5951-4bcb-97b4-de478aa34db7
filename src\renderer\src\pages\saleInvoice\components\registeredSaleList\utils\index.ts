import jsPDF from 'jspdf'
import 'jspdf-autotable'
import dayjs from 'dayjs'

interface InvoiceItem {
    product: {
        name: string
        productId: string
        tag: string
        nature: string
        category: {
            name: string
        }
    }
    salePrice: number
    total: number
    totalQuantity: number
}

interface InvoiceData {
    invoiceNumber: string
    date: Date
    customer: {
        name: string
    }
    items: InvoiceItem[]
    totalAmount: number
    discountAmount: number
    paidAmount: number
    previousBalance: number
    newBalance: number
}

export const generateInvoicePDF = async (invoice: InvoiceData, shouldSave: boolean = false): Promise<jsPDF | void> => {
    try {
        // Create PDF document (A5 size)
        const doc = new jsPDF({
            format: 'a5',
            unit: 'mm'
        })

        // Add header only on the first page
        // Add header
        doc.setFontSize(16)
        doc.setFont('helvetica', 'bold')
        doc.text('SJ LACE', doc.internal.pageSize.width / 2, 8, { align: 'center' })

        // Add address and phone numbers
        doc.setFontSize(8)
        doc.text('<PERSON><PERSON> Bazar', doc.internal.pageSize.width / 2, 13, { align: 'center' })
        doc.text('Phone: 03138698812, 03200817395', doc.internal.pageSize.width / 2, 18, { align: 'center' })

        // Add line and border
        doc.line(10, 20, doc.internal.pageSize.width - 8, 20) // Top horizontal line
        doc.line(10, 20, 10, 43) // Left vertical line
        doc.line(doc.internal.pageSize.width - 8, 20, doc.internal.pageSize.width - 8, 43) // Right vertical line
        doc.line(10, 43, doc.internal.pageSize.width - 8, 43) // Bottom horizontal line

        // Add invoice details
        doc.setFontSize(8)
        doc.text(`Invoice #: ${invoice.invoiceNumber}`, 12, 26)
        doc.text(`Date: ${dayjs(invoice.date).format('DD/MM/YYYY hh:mm A')}`, 12, 31)
        doc.text(`Customer: ${invoice.customer.name}`, 12, 36)

        // Add page number to first page
        doc.setFontSize(8)
        doc.text(`Page 1`, doc.internal.pageSize.width - 15, 8)

        // Add items table
        const tableColumns = [
            { header: 'Sr.', dataKey: 'serialNumber' },
            { header: 'Item', dataKey: 'product' },
            { header: 'ID', dataKey: 'productId' },
            { header: 'Qty', dataKey: 'quantity' },
            { header: 'Price', dataKey: 'price' },
            { header: 'Total', dataKey: 'total' }
        ]

        const tableRows = invoice.items.map((item, index) => ({
            serialNumber: index + 1,
            product: `${item.product.name} ${item.product.tag ? `(${item.product.tag})` : ''} ${item.product.category.name ? `[${item.product.category.name}]` : ''}`,
            productId: item.product.productId,
            quantity: item.totalQuantity.toString() + ' ' + (item.product.nature ? `[${item.product.nature}]` : ''),
            price: item.salePrice.toLocaleString('en-US', { minimumFractionDigits: 0, maximumFractionDigits: 2 }),
            total: item.total.toLocaleString('en-US', { minimumFractionDigits: 0, maximumFractionDigits: 2 })
        }))

        // Variable to track if we need to add a note page
        let addedNotePage = false;

        // @ts-ignore (jspdf-autotable types are not properly recognized)
        doc.autoTable({
            columns: tableColumns,
            body: tableRows,
            startY: 46,
            margin: { left: 10, right: 10 },
            theme: 'grid',
            styles: {
                fontSize: 8,
                cellPadding: 1,
                lineColor: [0, 0, 0],
                lineWidth: 0.1,
                fontStyle: 'bold'
            },
            headStyles: {
                fillColor: [0, 0, 0],
                textColor: [255, 255, 255],
                fontStyle: 'bold',
                lineColor: [0, 0, 0],
                lineWidth: 0.1
            },
            columnStyles: {
                serialNumber: { cellWidth: 10, halign: 'center', textColor: [0, 0, 0] },
                product: { cellWidth: 43, textColor: [0, 0, 0] },
                productId: { cellWidth: 15, textColor: [0, 0, 0] },
                quantity: { cellWidth: 20, halign: 'right', textColor: [0, 0, 0] },
                price: { cellWidth: 22, halign: 'right', textColor: [0, 0, 0] },
                total: { cellWidth: 20, halign: 'right', textColor: [0, 0, 0] }
            },
            footStyles: {
                fillColor: [255, 255, 255],
                textColor: [0, 0, 0],
                fontStyle: 'bold',
                lineWidth: 0.1,
                lineColor: [0, 0, 0]
            },
            // Only show the footer on the last page
            showFoot: 'lastPage',
            foot: [
                [
                    { content: 'Total Amount:', colSpan: 5, styles: { halign: 'right', fontStyle: 'bold', lineWidth: 0 } },
                    { content: invoice.totalAmount.toLocaleString('en-US', { minimumFractionDigits: 0, maximumFractionDigits: 2 }), styles: { halign: 'right', lineWidth: 0.1, lineColor: [0, 0, 0] } }
                ],
                [
                    { content: 'Discount:', colSpan: 5, styles: { halign: 'right', fontStyle: 'bold', lineWidth: 0 } },
                    { content: invoice.discountAmount.toLocaleString('en-US', { minimumFractionDigits: 0, maximumFractionDigits: 2 }), styles: { halign: 'right', lineWidth: 0.1, lineColor: [0, 0, 0] } }
                ],
                [
                    { content: 'Net Amount:', colSpan: 5, styles: { halign: 'right', fontStyle: 'bold', lineWidth: 0 } },
                    {
                        content: (invoice.totalAmount - invoice.discountAmount).toLocaleString('en-US', {
                            minimumFractionDigits: 0,
                            maximumFractionDigits: 2
                        }),
                        styles: { halign: 'right', lineWidth: 0.1, lineColor: [0, 0, 0] }
                    }
                ],
                [
                    { content: 'Paid Amount:', colSpan: 5, styles: { halign: 'right', fontStyle: 'bold', lineWidth: 0 } },
                    { content: invoice.paidAmount.toLocaleString('en-US', { minimumFractionDigits: 0, maximumFractionDigits: 2 }), styles: { halign: 'right', lineWidth: 0.1, lineColor: [0, 0, 0] } }
                ],
                [
                    { content: 'Previous Balance:', colSpan: 5, styles: { halign: 'right', fontStyle: 'bold', lineWidth: 0 } },
                    { content: invoice.previousBalance.toLocaleString('en-US', { minimumFractionDigits: 0, maximumFractionDigits: 2 }), styles: { halign: 'right', lineWidth: 0.1, lineColor: [0, 0, 0] } }
                ],
                [
                    { content: 'New Balance:', colSpan: 5, styles: { halign: 'right', fontStyle: 'bold', lineWidth: 0 } },
                    { content: invoice.newBalance.toLocaleString('en-US', { minimumFractionDigits: 0, maximumFractionDigits: 2 }), styles: { halign: 'right', lineWidth: 0.1, lineColor: [0, 0, 0] } }
                ]
            ],
            didDrawPage: function (data) {
                // Add page number to each page (except first page which already has it)
                if (data.pageNumber > 1) {
                    doc.setFontSize(8)
                    doc.text(`Page ${data.pageNumber}`, doc.internal.pageSize.width - 15, 8)
                }
            },
            didParseCell: function (data) {
                // This is needed to ensure proper calculation of table height
            },
            willDrawCell: function (data) {
                // This is needed to ensure proper calculation of table height
            },
            didDrawCell: function (data) {
                // This is needed to ensure proper calculation of table height
            }
        });

        // Get the final Y position after the table
        const finalY = (doc as any).lastAutoTable.finalY || 0;

        // Check if there's enough space for the note
        const pageHeight = doc.internal.pageSize.height;
        const noteHeight = 25; // Height needed for the note
        const availableHeight = pageHeight - finalY - noteHeight;

        // If not enough space, add a new page for the note
        if (availableHeight < 0) {
            doc.addPage();
            addedNotePage = true;

            // Add page number to the new page
            const newPageNumber = doc.getNumberOfPages();
            doc.setFontSize(8);
            doc.text(`Page ${newPageNumber}`, doc.internal.pageSize.width - 15, 8);

            // Add Note label
            doc.setFontSize(8);
            doc.setFont('helvetica', 'bold');
            doc.text('Note:', 10, 20);

            // Add Urdu text using canvas
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            if (ctx) {
                // Set canvas dimensions - much larger for higher resolution
                canvas.width = 1200; // 3x larger
                canvas.height = 120; // 3x larger

                // Set background color
                ctx.fillStyle = 'white';
                ctx.fillRect(0, 0, canvas.width, canvas.height);

                // Set font and draw text in two lines with larger font size
                ctx.font = 'bold 36px Arial'; // 3x larger font
                ctx.fillStyle = 'black';
                ctx.textAlign = 'right';
                ctx.direction = 'rtl'; // Right-to-left for Urdu

                // First line
                ctx.fillText('تین دن بعد مآل کي واپسی یا تبدیلی نہیں ہو گی،', 1170, 45);

                // Second line
                ctx.fillText('اور بل نہ ہونے کی صورت میں مآل کي واپسی نہیں ہو گی  شکریہ', 1170, 90);

                // Convert canvas to image and add to PDF
                const imgData = canvas.toDataURL('image/png');
                doc.addImage(imgData, 'PNG', 10, 25, 130, 15);
            }
        } else {
            // Add Note label
            doc.setFontSize(8);
            doc.setFont('helvetica', 'bold');
            doc.text('Note:', 10, finalY + 10);

            // Add Urdu text using canvas
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            if (ctx) {
                // Set canvas dimensions - much larger for higher resolution
                canvas.width = 1200; // 3x larger
                canvas.height = 120; // 3x larger

                // Set background color
                ctx.fillStyle = 'white';
                ctx.fillRect(0, 0, canvas.width, canvas.height);

                // Set font and draw text in two lines with larger font size
                ctx.font = 'bold 36px Arial'; // 3x larger font
                ctx.fillStyle = 'black';
                ctx.textAlign = 'right';
                ctx.direction = 'rtl'; // Right-to-left for Urdu

                // First line
                ctx.fillText('تین دن بعد مآل کي واپسی یا تبدیلی نہیں ہو گی،', 1170, 45);

                // Second line
                ctx.fillText('اور بل نہ ہونے کی صورت میں مآل کي واپسی نہیں ہو گی  شکریہ', 1170, 90);

                // Convert canvas to image and add to PDF
                const imgData = canvas.toDataURL('image/png');
                doc.addImage(imgData, 'PNG', 10, finalY + 15, 130, 15);
            }
        }

        if (shouldSave) {
            // Show save dialog
            const path = await window.electron.ipcRenderer.invoke('show-save-dialog', {
                title: 'Save Invoice PDF',
                defaultPath: `${invoice.invoiceNumber}.pdf`,
                filters: [{ name: 'PDF Files', extensions: ['pdf'] }]
            })

            // If user cancels the save dialog
            if (!path) {
                return
            }

            // Save the PDF
            await window.electron.ipcRenderer.invoke('save-pdf', {
                path,
                data: doc.output('arraybuffer')
            })
        }

        return doc
    } catch (error) {
        console.error('Error generating PDF:', error)
        throw error
    }
}
