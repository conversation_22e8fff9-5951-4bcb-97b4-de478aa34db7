import { useState } from 'react'
import {
  Card,
  DatePicker,
  Table,
  Button,
  Space,
  Typography,
  message,
  Tooltip,
  Modal,
  Select
} from 'antd'
import { useApi } from '@/renderer/hooks'
import { cashVaultApi } from '@/renderer/services'
import { formatDate, formatCurrency } from '@/renderer/utils'
import type {
  CashVaultStatement as CashVaultStatementType,
  CashVaultStatementEntry,
  GetCashVaultStatementParams
} from '@/common/types'
import dayjs from 'dayjs'
import { FiDownload, FiPrinter } from 'react-icons/fi'
import { ColumnsType } from 'antd/es/table'
import { handleCashVaultStatementPDF } from '../utils/generateCashVaultStatementPDF'

const { RangePicker } = DatePicker
const { Title, Text } = Typography
const { Option } = Select

type TableSize = 'small' | 'middle' | 'large'

export const CashVaultStatement = () => {
  const [dateRange, setDateRange] = useState<[Date, Date]>([
    dayjs().subtract(30, 'days').toDate(),
    dayjs().toDate()
  ])
  const [page, setPage] = useState(1)
  const [pageSize, setPageSize] = useState(100)
  const [tableSize, setTableSize] = useState<TableSize>('small')
  const [isPdfGenerating, setIsPdfGenerating] = useState(false)

  const {
    data: statement,
    isLoading,
    request: fetchStatement
  } = useApi<CashVaultStatementType, [GetCashVaultStatementParams]>(
    cashVaultApi.generateCashVaultStatement
  )

  const loadStatement = async () => {
    await fetchStatement({
      startDate: dateRange[0],
      endDate: dateRange[1],
      page,
      pageSize
    })
  }

  const handleSearch = () => {
    setPage(1) // Reset to first page
    loadStatement()
  }

  const [isPdfModalOpen, setIsPdfModalOpen] = useState(false)
  const [pdfAction, setPdfAction] = useState<'save' | 'print'>('save')

  const handleGeneratePdf = (action: 'save' | 'print') => {
    setPdfAction(action)
    setIsPdfModalOpen(true)
  }

  const handlePdfConfirm = async () => {
    try {
      setIsPdfGenerating(true)

      await handleCashVaultStatementPDF(dateRange[0], dateRange[1], pdfAction)

      message.success(`PDF ${pdfAction === 'save' ? 'saved' : 'printed'} successfully`)
    } catch (error) {
      console.error('Failed to generate PDF:', error)
      message.error(
        `Failed to ${pdfAction} PDF. Please try again: ${
          error instanceof Error ? error.message : 'Unknown error'
        }`
      )
    } finally {
      setIsPdfGenerating(false)
      setIsPdfModalOpen(false)
    }
  }

  const columns: ColumnsType<CashVaultStatementEntry> = [
    {
      title: 'S.No',
      key: 'serialNumber',
      width: 70,
      render: (_, __, index) => (page - 1) * pageSize + index + 1
    },
    {
      title: 'Date',
      dataIndex: 'date',
      key: 'date',
      width: 120,
      render: (date) => formatDate(date)
    },
    {
      title: 'Description',
      dataIndex: 'description',
      key: 'description',
      render: (description, record) => (
        <div>
          <div>{description}</div>
          <div className="text-xs text-gray-500">Created by: {record.createdBy.name}</div>
        </div>
      )
    },
    {
      title: 'Credit',
      dataIndex: 'credit',
      key: 'credit',
      width: 120,
      align: 'right',
      render: (credit) =>
        credit ? (
          <span className="font-medium text-green-600">{formatCurrency(credit)}</span>
        ) : (
          <span>-</span>
        )
    },
    {
      title: 'Debit',
      dataIndex: 'debit',
      key: 'debit',
      width: 120,
      align: 'right',
      render: (debit) =>
        debit ? (
          <span className="font-medium text-red-600">{formatCurrency(debit)}</span>
        ) : (
          <span>-</span>
        )
    },
    {
      title: 'Running Balance',
      dataIndex: 'runningBalance',
      key: 'runningBalance',
      width: 150,
      align: 'right',
      render: (balance) => (
        <span className={`font-medium ${balance >= 0 ? 'text-green-600' : 'text-red-600'}`}>
          {formatCurrency(balance)}
        </span>
      )
    }
  ]

  const renderSummary = () => {
    if (!statement) return null

    return (
      <div className="mb-6 grid grid-cols-1 gap-4 md:grid-cols-4">
        <Card size="small">
          <Tooltip title="Balance at the start of the selected period">
            <Text type="secondary">Opening Balance</Text>
            <div className="text-lg font-medium">{formatCurrency(statement.openingBalance)}</div>
          </Tooltip>
        </Card>
        <Card size="small">
          <Tooltip title="Total credits during the period">
            <Text type="secondary">Total Credits</Text>
            <div className="text-lg font-medium text-green-600">
              {formatCurrency(statement.summary.totalCredits)}
            </div>
          </Tooltip>
        </Card>
        <Card size="small">
          <Tooltip title="Total debits during the period">
            <Text type="secondary">Total Debits</Text>
            <div className="text-lg font-medium text-red-600">
              {formatCurrency(statement.summary.totalDebits)}
            </div>
          </Tooltip>
        </Card>
        <Card size="small">
          <Tooltip title="Balance at the end of the selected period">
            <Text type="secondary">Closing Balance</Text>
            <div className="text-lg font-medium">{formatCurrency(statement.closingBalance)}</div>
          </Tooltip>
        </Card>
      </div>
    )
  }

  return (
    <Card className="h-full">
      <div className="mb-6">
        <Title level={4}>Cash Vault Statement</Title>
      </div>

      <div className="mb-6">
        <Space>
          <RangePicker
            value={[dayjs(dateRange[0]), dayjs(dateRange[1])]}
            onChange={(dates) => {
              if (dates && dates[0] && dates[1]) {
                setDateRange([dates[0].toDate(), dates[1].toDate()])
              }
            }}
          />
          <Select value={tableSize} onChange={setTableSize} style={{ width: 120 }}>
            <Option value="small">Small</Option>
            <Option value="middle">Medium</Option>
            <Option value="large">Large</Option>
          </Select>
          <Button type="primary" onClick={handleSearch}>
            Search
          </Button>
          <Button
            icon={<FiDownload />}
            onClick={() => handleGeneratePdf('save')}
            loading={isPdfGenerating && pdfAction === 'save'}
          >
            Save PDF
          </Button>
          <Button
            icon={<FiPrinter />}
            onClick={() => handleGeneratePdf('print')}
            loading={isPdfGenerating && pdfAction === 'print'}
          >
            Print
          </Button>
        </Space>
      </div>

      {statement && renderSummary()}

      <Table
        columns={columns}
        dataSource={statement?.entries}
        loading={isLoading}
        rowKey="id"
        size={tableSize}
        pagination={{
          current: page,
          pageSize,
          total: statement?.pagination.total,
          onChange: (page, pageSize) => {
            setPage(page)
            setPageSize(pageSize as number)
            loadStatement()
          },
          showSizeChanger: true,
          pageSizeOptions: ['50', '100', '500', '1000'],
          showTotal: (total) => `Total ${total} entries`
        }}
        // scroll={{ x: 'max-content', y: 'calc(100vh - 400px)' }}
        sticky
        virtual
        bordered
      />

      {/* PDF Confirmation Modal */}
      <Modal
        title={`Confirm ${pdfAction === 'save' ? 'Save' : 'Print'} PDF`}
        open={isPdfModalOpen}
        onOk={handlePdfConfirm}
        onCancel={() => setIsPdfModalOpen(false)}
        confirmLoading={isPdfGenerating}
      >
        <p>
          This will generate a PDF with all transactions in the selected date range.
          {pdfAction === 'save'
            ? ' You will be prompted to save the file.'
            : ' The PDF will open in your default PDF viewer for printing.'}
        </p>
        <p>
          Date range: {dateRange[0].toLocaleDateString()} - {dateRange[1].toLocaleDateString()}
        </p>
      </Modal>
    </Card>
  )
}
