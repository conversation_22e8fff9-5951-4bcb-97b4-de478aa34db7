import jsPDF from 'jspdf'
import 'jspdf-autotable'
import dayjs from 'dayjs'

interface StockReturnItem {
    id: string
    quantity: number
    purchasePrice: number
    product: {
        name: string
        productId: string
        tag?: string
        nature?: string
        category: {
            name: string
        }
    }
}

interface StockReturn {
    id: string
    invoiceNumber: string
    originalInvoiceNumber: string
    date: string | Date
    status: string
    totalAmount: number
    customer: {
        id: string
        name: string
        phoneNumber: string | null
    } | null
    customerType: string
    items: StockReturnItem[]
    createdBy: {
        name: string
    }
    voidingReason?: string
    voidedBy?: {
        name: string
    }
    voidedAt?: string | Date
    type?: string // To identify if it's a legacy return
}

export const generateStockReturnPDF = async (stockReturn: StockReturn, shouldSave: boolean = false): Promise<jsPDF | void> => {
    try {
        // Create PDF document (A5 size)
        const doc = new jsPDF({
            format: 'a5',
            unit: 'mm'
        })

        // Add header
        doc.setFontSize(16)
        doc.setFont('helvetica', 'bold')
        doc.text('SJ LACE', doc.internal.pageSize.width / 2, 8, { align: 'center' })

        // Add address and phone numbers
        doc.setFontSize(8)
        doc.text('Sarafa Bazar', doc.internal.pageSize.width / 2, 13, { align: 'center' })
        doc.text('Phone: 03138698812, 03200817395', doc.internal.pageSize.width / 2, 18, { align: 'center' })

        // Add line and border
        doc.line(10, 20, doc.internal.pageSize.width - 8, 20) // Top horizontal line
        doc.line(10, 20, 10, 43) // Left vertical line
        doc.line(doc.internal.pageSize.width - 8, 20, doc.internal.pageSize.width - 8, 43) // Right vertical line
        doc.line(10, 43, doc.internal.pageSize.width - 8, 43) // Bottom horizontal line

        // Check if it's a legacy return
        const isLegacyReturn = stockReturn.type === 'LEGACY_STOCK_RETURN';

        // Add invoice details
        doc.setFontSize(8)

        // Add special header for legacy returns
        if (isLegacyReturn) {
            doc.setTextColor(255, 0, 0)
            doc.setFontSize(10)
            doc.text(`LEGACY STOCK RETURN RECEIPT`, doc.internal.pageSize.width / 2, 25, { align: 'center' })
            doc.setTextColor(0, 0, 0)
            doc.setFontSize(8)
        } else {
            doc.text(`STOCK RETURN RECEIPT`, doc.internal.pageSize.width / 2, 25, { align: 'center' })
        }

        doc.text(`Return Invoice #: ${stockReturn.invoiceNumber}`, 12, 29)

        // For legacy returns, the original invoice might not exist
        if (stockReturn.originalInvoiceNumber) {
            doc.text(`Original Invoice #: ${stockReturn.originalInvoiceNumber}`, 12, 33)
        } else if (isLegacyReturn) {
            doc.text(`Pre-system Purchase Return`, 12, 33)
        }

        doc.text(`Date: ${dayjs(stockReturn.date).format('DD/MM/YYYY hh:mm A')}`, 12, 37)
        doc.text(`Customer: ${stockReturn.customer ? stockReturn.customer.name : 'Walk-in Customer'}`, 12, 41)

        // Add page number to first page
        doc.setFontSize(8)
        doc.text(`Page 1`, doc.internal.pageSize.width - 15, 8)

        // Add items table
        const tableColumns = [
            { header: 'Sr.', dataKey: 'serialNumber' },
            { header: 'Item', dataKey: 'product' },
            { header: 'ID', dataKey: 'productId' },
            { header: 'Qty', dataKey: 'quantity' },
            { header: 'Price', dataKey: 'price' },
            { header: 'Total', dataKey: 'total' }
        ]

        const tableRows = stockReturn.items.map((item, index) => ({
            serialNumber: index + 1,
            product: `${item.product.name} ${item.product.tag ? `(${item.product.tag})` : ''} ${item.product.category.name ? `[${item.product.category.name}]` : ''}`,
            productId: item.product.productId,
            quantity: item.quantity.toString() + ' ' + (item.product.nature ? `[${item.product.nature}]` : ''),
            price: item.purchasePrice.toLocaleString('en-US', { minimumFractionDigits: 0, maximumFractionDigits: 2 }),
            total: (item.quantity * item.purchasePrice).toLocaleString('en-US', { minimumFractionDigits: 0, maximumFractionDigits: 2 })
        }))

        // @ts-ignore (jspdf-autotable types are not properly recognized)
        doc.autoTable({
            columns: tableColumns,
            body: tableRows,
            startY: 46,
            margin: { left: 10, right: 10 },
            theme: 'grid',
            styles: {
                fontSize: 8,
                cellPadding: 1,
                lineColor: [0, 0, 0],
                lineWidth: 0.1,
                fontStyle: 'bold'
            },
            headStyles: {
                fillColor: [0, 0, 0],
                textColor: [255, 255, 255],
                fontStyle: 'bold',
                lineColor: [0, 0, 0],
                lineWidth: 0.1
            },
            columnStyles: {
                serialNumber: { cellWidth: 10, halign: 'center', textColor: [0, 0, 0] },
                product: { cellWidth: 43, textColor: [0, 0, 0] },
                productId: { cellWidth: 15, textColor: [0, 0, 0] },
                quantity: { cellWidth: 20, halign: 'right', textColor: [0, 0, 0] },
                price: { cellWidth: 22, halign: 'right', textColor: [0, 0, 0] },
                total: { cellWidth: 20, halign: 'right', textColor: [0, 0, 0] }
            },
            footStyles: {
                fillColor: [255, 255, 255],
                textColor: [0, 0, 0],
                fontStyle: 'bold',
                lineWidth: 0.1,
                lineColor: [0, 0, 0]
            },
            // Only show the footer on the last page
            showFoot: 'lastPage',
            foot: [
                [
                    { content: 'Total Return Amount:', colSpan: 5, styles: { halign: 'right', fontStyle: 'bold', lineWidth: 0 } },
                    { content: stockReturn.totalAmount.toLocaleString('en-US', { minimumFractionDigits: 0, maximumFractionDigits: 2 }), styles: { halign: 'right', lineWidth: 0.1, lineColor: [0, 0, 0] } }
                ],
                // [
                //     { content: 'Status:', colSpan: 5, styles: { halign: 'right', fontStyle: 'bold', lineWidth: 0 } },
                //     { content: stockReturn.status, styles: { halign: 'right', lineWidth: 0.1, lineColor: [0, 0, 0] } }
                // ],
                [
                    { content: 'Customer Type:', colSpan: 5, styles: { halign: 'right', fontStyle: 'bold', lineWidth: 0 } },
                    { content: stockReturn.customerType, styles: { halign: 'right', lineWidth: 0.1, lineColor: [0, 0, 0] } }
                ],
                [
                    { content: 'Created By:', colSpan: 5, styles: { halign: 'right', fontStyle: 'bold', lineWidth: 0 } },
                    { content: stockReturn.createdBy.name, styles: { halign: 'right', lineWidth: 0.1, lineColor: [0, 0, 0] } }
                ]
            ],
            didDrawPage: function (data) {
                // Add page number to each page (except first page which already has it)
                if (data.pageNumber > 1) {
                    doc.setFontSize(8)
                    doc.text(`Page ${data.pageNumber}`, doc.internal.pageSize.width - 15, 8)
                }
            }
        })

        // Get the final Y position after the table
        const finalY = (doc as any).lastAutoTable.finalY || 0

        // Add signature fields
        doc.setFontSize(8)
        doc.setFont('helvetica', 'normal')

        // Check if there's enough space on the current page for signatures
        if (finalY + 30 > doc.internal.pageSize.height - 10) {
            doc.addPage()
            doc.setFontSize(8)
            doc.text(`Page ${doc.getNumberOfPages()}`, doc.internal.pageSize.width - 15, 8)
        }

        const signatureY = finalY + 15

        // Add signature fields
        doc.line(20, signatureY, 70, signatureY)
        doc.line(80, signatureY, 130, signatureY)

        doc.text('Customer Signature', 20, signatureY + 5)
        doc.text('Staff Signature', 80, signatureY + 5)

        // Add legacy return note if applicable
        if (isLegacyReturn) {
            const legacyY = signatureY + 15;

            // Check if need to add a new page for legacy info
            if (legacyY + 20 > doc.internal.pageSize.height - 10) {
                doc.addPage();
                doc.setFontSize(8);
                doc.text(`Page ${doc.getNumberOfPages()}`, doc.internal.pageSize.width - 15, 8);
            }

            doc.setFontSize(10);
            doc.setFont('helvetica', 'bold');
            doc.setTextColor(0, 0, 255);
            doc.text('LEGACY RETURN INFORMATION', 10, legacyY);

            doc.setFontSize(8);
            doc.setTextColor(0, 0, 0);
            doc.setFont('helvetica', 'normal');
            doc.text('This is a return for a purchase made before the system was implemented.', 10, legacyY + 5);
            doc.text('No original invoice exists in the system for this return.', 10, legacyY + 10);
        }

        // Add void information if the return is voided
        if (stockReturn.status === 'VOID' && stockReturn.voidingReason) {
            const voidY = isLegacyReturn ? signatureY + 30 : signatureY + 15;

            // Check if need to add a new page for void info
            if (voidY + 30 > doc.internal.pageSize.height - 10) {
                doc.addPage()
                doc.setFontSize(8)
                doc.text(`Page ${doc.getNumberOfPages()}`, doc.internal.pageSize.width - 15, 8)
            }

            doc.setFontSize(10)
            doc.setFont('helvetica', 'bold')
            doc.setTextColor(255, 0, 0)
            doc.text('VOID INFORMATION', 10, voidY)

            doc.setFontSize(8)
            doc.setTextColor(0, 0, 0)

            if (stockReturn.voidedBy) {
                doc.text(`Voided By: ${stockReturn.voidedBy.name}`, 10, voidY + 5)
            }

            if (stockReturn.voidedAt) {
                doc.text(`Voided At: ${dayjs(stockReturn.voidedAt).format('DD/MM/YYYY hh:mm A')}`, 10, voidY + 10)
            }

            doc.text('Reason:', 10, voidY + 15)

            doc.setFont('helvetica', 'normal')
            // Split reason text into multiple lines if needed
            const textLines = doc.splitTextToSize(stockReturn.voidingReason, 130)
            doc.text(textLines, 10, voidY + 20)
        }

        // Save or return the PDF
        if (shouldSave) {
            // Show save dialog
            const path = await window.electron.ipcRenderer.invoke('show-save-dialog', {
                title: 'Save Stock Return PDF',
                defaultPath: `Stock_Return_${stockReturn.invoiceNumber}.pdf`,
                filters: [{ name: 'PDF Files', extensions: ['pdf'] }]
            })

            // If user cancels the save dialog
            if (!path) {
                return
            }

            // Save the PDF
            await window.electron.ipcRenderer.invoke('save-pdf', {
                path,
                data: doc.output('arraybuffer')
            })
        }

        return doc
    } catch (error) {
        console.error('Error generating stock return PDF:', error)
        throw error
    }
}
