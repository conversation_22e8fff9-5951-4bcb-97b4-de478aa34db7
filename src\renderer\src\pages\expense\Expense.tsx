import { useState } from 'react'
import { <PERSON><PERSON>, Card, Space, App } from 'antd'
import { PlusOutlined } from '@ant-design/icons'
import { useSelector } from 'react-redux'
import { IRootState } from '@/renderer/redux'
import { useTheme } from '@/renderer/contexts'
import { ExpenseList } from './components/ExpenseList'
import { CreateExpenseDrawer } from './components/CreateExpenseDrawer'
import { ExpenseDetailsModal } from './components/ExpenseDetailsModal'
import { VoidExpenseModal } from './components/VoidExpenseModal'

const Expense = () => {
  const { message } = App.useApp()
  const { isDarkMode } = useTheme()
  const user = useSelector((state: IRootState) => state.user.data)

  // State for UI controls
  const [isCreateDrawerOpen, setIsCreateDrawerOpen] = useState(false)
  const [selectedExpenseId, setSelectedExpenseId] = useState<string | null>(null)
  const [expenseToVoid, setExpenseToVoid] = useState<string | null>(null)
  const [refreshTrigger, setRefreshTrigger] = useState(0)

  // Handlers
  const handleCreateExpense = () => {
    setIsCreateDrawerOpen(true)
  }

  const handleExpenseClick = (expenseId: string) => {
    setSelectedExpenseId(expenseId)
  }

  const handleVoidExpense = (expenseId: string) => {
    setExpenseToVoid(expenseId)
  }

  const handleExpenseCreated = () => {
    setRefreshTrigger((prev) => prev + 1)
    setIsCreateDrawerOpen(false)
    message.success('Expense created successfully')
  }

  const handleExpenseVoided = () => {
    setRefreshTrigger((prev) => prev + 1)
    setExpenseToVoid(null)
    message.success('Expense voided successfully')
  }

  return (
    <Card
      className={`m-6 bg-[length:200%_200%] bg-[center] shadow-lg ${
        isDarkMode
          ? 'bg-gradient-to-bl from-indigo-950 via-black to-indigo-900 shadow-indigo-950'
          : 'bg-gradient-to-bl from-indigo-200 via-white to-indigo-100 shadow-indigo-200'
      }`}
    >
      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        <div className="flex justify-end">
          <Button type="primary" icon={<PlusOutlined />} onClick={handleCreateExpense}>
            Add Expense
          </Button>
        </div>

        <ExpenseList
          onExpenseClick={handleExpenseClick}
          onVoidExpense={handleVoidExpense}
          refreshTrigger={refreshTrigger}
        />
      </Space>

      {/* Modals and Drawers */}
      <CreateExpenseDrawer
        open={isCreateDrawerOpen}
        onClose={() => setIsCreateDrawerOpen(false)}
        onExpenseCreated={handleExpenseCreated}
      />

      <ExpenseDetailsModal
        expenseId={selectedExpenseId}
        open={!!selectedExpenseId}
        onClose={() => setSelectedExpenseId(null)}
      />

      <VoidExpenseModal
        expenseId={expenseToVoid}
        open={!!expenseToVoid}
        onClose={() => setExpenseToVoid(null)}
        onVoidSuccess={handleExpenseVoided}
      />
    </Card>
  )
}

export default Expense
