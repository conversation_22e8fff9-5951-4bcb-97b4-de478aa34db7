import { prisma } from '../db';
import { Prisma, StockStatus } from '@prisma/client';
import { UpdateProductData } from '@/common/types';

interface GetProductsParams {
    page: number;
    limit: number;
    search?: string;
}

interface CreateProductData {
    name: string;
    categoryId: string;
    productId: string;
    nature?: string;
    tag?: string;
    minStockLevel?: number;
    salePrice: number;
}

interface ProductQuantityReportParams {
    categoryId?: string;
    sortBy?: string;
    sortOrder?: string;
}

class ProductService {
    async createProduct(data: CreateProductData) {
        // Validate sale price is positive
        if (data.salePrice <= 0) {
            throw new Error('Sale price must be greater than zero');
        }

        const existingProduct = await prisma.product.findFirst({
            where: {
                productId: data.productId
            }
        });

        if (existingProduct) {
            throw new Error('Product with same ID or name already exists');
        }

        // Use transaction to ensure both operations succeed or fail together
        return await prisma.$transaction(async (tx) => {
            // Create the product
            const product = await tx.product.create({
                data
            });

            // Update category counters only after successful product creation
            await tx.category.update({
                where: { id: data.categoryId },
                data: {
                    sequence: { increment: 1 },
                    productCount: { increment: 1 }
                }
            });

            return product;
        });
    }

    async getProductById(id: string) {
        const [product, stockValue] = await Promise.all([
            prisma.product.findUnique({
                where: { id },
                select: {
                    id: true,
                    name: true,
                    productId: true,
                    tag: true,
                    nature: true,
                    minStockLevel: true,
                    salePrice: true,
                    quantityInStock: true,
                    category: {
                        select: {
                            name: true,
                            prefix: true
                        }
                    }
                }
            }),
            this.getProductStockValue(id)
        ]);

        if (!product) {
            throw new Error('Product not found');
        }

        return {
            ...product,
            stockValue
        };
    }

    async deleteProduct(id: string) {
        try {
            // Use transaction to ensure atomicity when updating category counts
            return await prisma.$transaction(async (tx) => {
                // Get the product with its category
                const product = await tx.product.findUnique({
                    where: { id },
                    include: { category: true }
                });

                if (!product) {
                    throw new Error('Product not found');
                }

                // Delete the product
                await tx.product.delete({
                    where: { id }
                });

                // Decrement the category product count
                await tx.category.update({
                    where: { id: product.categoryId },
                    data: {
                        productCount: { decrement: 1 }
                    }
                });

                return product;
            });
        } catch (error) {
            if (error instanceof Prisma.PrismaClientKnownRequestError) {
                // P2003 is the Prisma error code for foreign key constraint violations
                if (error.code === 'P2003') {
                    throw new Error('Cannot delete product because it is being used in stocks or transactions');
                }
            }
            throw error; // Re-throw other errors
        }
    }

    async getProducts({ page, limit, search }: GetProductsParams) {
        const where: Prisma.ProductWhereInput = search ? {
            OR: [
                { name: { contains: search, mode: 'insensitive' } },
                { productId: { contains: search, mode: 'insensitive' } },
                { category: { name: { contains: search, mode: 'insensitive' } } }
            ]
        } : {};

        // Using transaction for atomic operations
        return await prisma.$transaction(async (tx) => {
            const [products, total] = await Promise.all([
                tx.product.findMany({
                    where,
                    skip: (page - 1) * limit,
                    take: limit,
                    orderBy: { createdAt: 'desc' },
                    include: {
                        category: {
                            select: {
                                name: true,
                                prefix: true
                            }
                        }
                    }
                }),

                tx.product.count({ where })
            ]);

            return { products, total, page, totalPages: Math.ceil(total / limit) };
        });
    }

    async getProductsForSelect() {
        const products = await prisma.product.findMany({
            select: {
                id: true,
                name: true,
                category: {
                    select: {
                        name: true,
                        prefix: true
                    }
                },
                productId: true,
                tag: true
            },
            orderBy: { productId: 'asc' }
        });

        return products.map(product => ({
            value: product.id,
            label: `${product.name} - ${product.category.name} (${product.productId})${product.tag ? ` [${product.tag}]` : ''}`
        }));
    }

    async updateProduct(id: string, data: UpdateProductData) {
        // Validate sale price if it's being updated
        if (data.salePrice !== undefined && data.salePrice <= 0) {
            throw new Error('Sale price must be greater than zero');
        }

        const existingProduct = await prisma.product.findUnique({
            where: { id }
        });

        if (!existingProduct) {
            throw new Error('Product not found');
        }

        return await prisma.product.update({
            where: { id },
            data,
            select: {
                id: true,
                name: true,
                productId: true,
                tag: true,
                nature: true,
                minStockLevel: true,
                salePrice: true,
                category: {
                    select: {
                        name: true,
                        prefix: true
                    }
                }
            }
        });
    }

    private async getProductStockValue(id: string) {
        const stockEntries = await prisma.stock.findMany({
            where: {
                status: StockStatus.IN_STOCK,
                productId: id
            },
            select: {
                purchasePrice: true,
                quantity: true
            }
        });

        const totalQuantity = stockEntries.reduce((sum, entry) => sum + entry.quantity, 0);
        const totalPurchaseValue = stockEntries.reduce((sum, entry) => sum + (entry.purchasePrice * entry.quantity), 0);
        const averagePurchasePrice = totalQuantity > 0 ? totalPurchaseValue / totalQuantity : 0;

        return {
            totalPurchaseValue,
            averagePurchasePrice,
            totalQuantity
        };
    }

    async getProductQuantityReport(params: ProductQuantityReportParams) {
        try {
            // Build the where clause based on params
            const where: any = {}

            if (params.categoryId) {
                where.categoryId = params.categoryId
            }

            // Get all products matching the criteria
            const products = await prisma.product.findMany({
                where,
                include: {
                    category: {
                        select: {
                            name: true
                        }
                    }
                },
                orderBy: this.getSortOrder(params.sortBy, params.sortOrder)
            })

            // Calculate summary statistics
            const totalProducts = products.length
            let totalValue = 0
            let lowStockItems = 0
            let outOfStockItems = 0

            // Get stock values for each product
            const productsWithValues = await Promise.all(
                products.map(async (product) => {
                    const stockValue = await this.getProductStockValue(product.id)

                    // Update summary statistics
                    const purchaseValue = stockValue.totalPurchaseValue || 0
                    totalValue += purchaseValue

                    if (product.quantityInStock === 0) {
                        outOfStockItems++
                    } else if (
                        product.minStockLevel &&
                        product.quantityInStock < product.minStockLevel
                    ) {
                        lowStockItems++
                    }

                    return {
                        id: product.id,
                        name: product.name,
                        productId: product.productId,
                        tag: product.tag,
                        nature: product.nature,
                        category: {
                            name: product.category.name
                        },
                        quantityInStock: product.quantityInStock,
                        minStockLevel: product.minStockLevel,
                        salePrice: product.salePrice,
                        purchaseValue: purchaseValue,
                        averagePurchasePrice: stockValue.averagePurchasePrice || 0
                    }
                })
            )

            return {
                products: productsWithValues,
                summary: {
                    totalProducts,
                    totalValue,
                    lowStockItems,
                    outOfStockItems
                }
            }
        } catch (error) {
            throw error
        }
    }

    private getSortOrder(sortBy?: string, sortOrder?: string) {
        const direction = sortOrder === 'desc' ? 'desc' : 'asc'

        switch (sortBy) {
            case 'quantity':
                return { quantityInStock: direction as Prisma.SortOrder }
            case 'name':
                return { name: direction as Prisma.SortOrder }
            default:
                return { name: 'asc' as Prisma.SortOrder }
        }
    }
}

export const productService = new ProductService();