import { Card, Typography, Row, Col } from 'antd'
import { ArrowUpOutlined, ArrowDownOutlined, DollarOutlined } from '@ant-design/icons'
import { formatCurrency } from '@/renderer/utils'
import { useTheme } from '@/renderer/contexts/ThemeContext'

const { Text } = Typography

interface LedgerSummaryProps {
  credits: number
  debits: number
  net: number
  overall?: {
    credits: number
    debits: number
    net: number
    balance?: number
  }
  visible: boolean
}

const LedgerSummary = ({ credits, debits, net, overall, visible }: LedgerSummaryProps) => {
  const { isDarkMode } = useTheme()

  const getGradient = () => {
    if (isDarkMode) {
      if (net > 0) {
        return 'bg-gradient-to-br from-green-950 via-slate-900 to-green-950'
      }
      if (net < 0) {
        return 'bg-gradient-to-br from-red-950 via-slate-900 to-red-950'
      }
      return 'bg-gradient-to-br from-slate-950 via-slate-900 to-slate-950'
    } else {
      if (net > 0) {
        return 'bg-gradient-to-br from-green-200 via-white to-green-200'
      }
      if (net < 0) {
        return 'bg-gradient-to-br from-red-200 via-white to-red-200'
      }
      return 'bg-gradient-to-br from-gray-300 via-white to-gray-300'
    }
  }

  return (
    <Card
      className={`overflow-hidden rounded-lg bg-[length:200%_200%] bg-[center] shadow-lg transition-all duration-500 ease-in-out ${
        visible ? 'mt-4 max-h-[100px] opacity-100' : 'mt-0 max-h-0 opacity-0'
      } ${getGradient()}`}
      size="small"
      bodyStyle={{ padding: '8px 12px' }}
    >
      <Row gutter={[16, 0]}>
        <Col span={12}>
          <Text strong className="flex items-center gap-1 text-xs">
            <DollarOutlined /> Current Page
          </Text>
          <Row gutter={[24, 0]} align="middle" className="mt-1">
            <Col span={8}>
              <div className="flex items-center gap-1">
                <ArrowUpOutlined style={{ color: '#52c41a', fontSize: '10px' }} />
                <Text className="text-xs text-green-500" strong>
                  {formatCurrency(credits)}
                </Text>
              </div>
              <Text type="secondary" className="text-[10px]">
                Credits
              </Text>
            </Col>
            <Col span={8}>
              <div className="flex items-center gap-1">
                <ArrowDownOutlined style={{ color: '#f5222d', fontSize: '10px' }} />
                <Text className="text-xs text-red-500" strong>
                  {formatCurrency(debits)}
                </Text>
              </div>
              <Text type="secondary" className="text-[10px]">
                Debits
              </Text>
            </Col>
            <Col span={8}>
              <div className="flex items-center gap-1">
                {net >= 0 ? (
                  <ArrowUpOutlined style={{ color: '#52c41a', fontSize: '10px' }} />
                ) : (
                  <ArrowDownOutlined style={{ color: '#f5222d', fontSize: '10px' }} />
                )}
                <Text
                  className={`text-xs font-bold ${
                    net > 0 ? 'text-green-500' : net < 0 ? 'text-red-500' : 'text-gray-500'
                  }`}
                >
                  {formatCurrency(net)}
                </Text>
              </div>
              <Text type="secondary" className="text-[10px]">
                Balance
              </Text>
            </Col>
          </Row>
        </Col>

        {overall && (
          <Col span={12}>
            <Text strong className="flex items-center gap-1 text-xs">
              <DollarOutlined /> Overall Account
            </Text>
            <Row gutter={[24, 0]} align="middle" className="mt-1">
              <Col span={6}>
                <div className="flex items-center gap-1">
                  <ArrowUpOutlined style={{ color: '#52c41a', fontSize: '10px' }} />
                  <Text className="text-xs text-green-500" strong>
                    {formatCurrency(overall.credits)}
                  </Text>
                </div>
                <Text type="secondary" className="text-[10px]">
                  Credits
                </Text>
              </Col>
              <Col span={6}>
                <div className="flex items-center gap-1">
                  <ArrowDownOutlined style={{ color: '#f5222d', fontSize: '10px' }} />
                  <Text className="text-xs text-red-500" strong>
                    {formatCurrency(overall.debits)}
                  </Text>
                </div>
                <Text type="secondary" className="text-[10px]">
                  Debits
                </Text>
              </Col>
              <Col span={6}>
                <div className="flex items-center gap-1">
                  {overall.net >= 0 ? (
                    <ArrowUpOutlined style={{ color: '#52c41a', fontSize: '10px' }} />
                  ) : (
                    <ArrowDownOutlined style={{ color: '#f5222d', fontSize: '10px' }} />
                  )}
                  <Text
                    className={`text-xs font-bold ${
                      overall.net > 0
                        ? 'text-green-500'
                        : overall.net < 0
                          ? 'text-red-500'
                          : 'text-gray-500'
                    }`}
                  >
                    {formatCurrency(overall.net)}
                  </Text>
                </div>
                <Text type="secondary" className="text-[10px]">
                  Net
                </Text>
              </Col>
              <Col span={6}>
                <div className="flex items-center gap-1">
                  {(overall.balance || 0) >= 0 ? (
                    <ArrowUpOutlined style={{ color: '#52c41a', fontSize: '10px' }} />
                  ) : (
                    <ArrowDownOutlined style={{ color: '#f5222d', fontSize: '10px' }} />
                  )}
                  <Text
                    className={`text-xs font-bold ${
                      (overall.balance || 0) > 0
                        ? 'text-green-500'
                        : (overall.balance || 0) < 0
                          ? 'text-red-500'
                          : 'text-gray-500'
                    }`}
                  >
                    {formatCurrency(overall.balance || 0)}
                  </Text>
                </div>
                <Text type="secondary" className="text-[10px]">
                  Total Balance
                </Text>
              </Col>
            </Row>
          </Col>
        )}
      </Row>
    </Card>
  )
}

export default LedgerSummary
