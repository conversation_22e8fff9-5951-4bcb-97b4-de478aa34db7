import { http } from './http';
import { Channels } from '@/common/constants';
import { SmallCounterTransactionFilters, SmallCounterTransactionsResponse, SmallCounterReconcileResponse, InitializeSmallCounterData, IServerResponse, GetSmallCounterStatementParams } from '@/common/types';

export const initializeSmallCounter = async (data: InitializeSmallCounterData) => {
    return await http.post(Channels.INITIALIZE_SMALL_COUNTER, {
        body: data
    });
};

export const getCurrentBalance = async (): Promise<IServerResponse> => {
    return await http.get(Channels.GET_SMALL_COUNTER_BALANCE);
};

export const transferToVault = async (amount: number): Promise<IServerResponse> => {
    return await http.post(Channels.TRANSFER_TO_VAULT, {
        body: { amount }
    });
};

export const reconcileSmallCounter = async () => {
    return await http.get(Channels.RECONCILE_SMALL_COUNTER);
};

export const getAllTransactions = async (filters: SmallCounterTransactionFilters) => {
    return await http.get(Channels.GET_SMALL_COUNTER_TRANSACTIONS, {
        query: filters
    });
};

export const generateSmallCounterStatement = async (params: GetSmallCounterStatementParams) => {
    return await http.get(Channels.GENERATE_SMALL_COUNTER_STATEMENT, {
        query: params
    });
};