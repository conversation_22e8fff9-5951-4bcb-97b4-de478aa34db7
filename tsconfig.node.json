{
  "extends": "@electron-toolkit/tsconfig/tsconfig.node.json",
  "include": [
    "electron.vite.config.*",
    "src/main/**/*",
    "src/preload/**/*",
    "src/common/**/*"
  ],
  "compilerOptions": {
    "composite": true,
    "baseUrl": ".",
    "paths": {
      "@/main/*": [
        "src/main/*"
      ],
      "@/common/*": [
        "src/common/*"
      ],

    },
    "types": [
      "electron-vite/node"
    ]
  }
}