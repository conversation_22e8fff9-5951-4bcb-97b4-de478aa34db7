import { useEffect, useState } from 'react'
import { Typography, Space, Button, Alert, Radio, Skeleton } from 'antd'
import { useApi } from '@/renderer/hooks'
import { dashboardApi } from '@/renderer/services'
import { TopEntity, TimeRange } from '@/common/types/dashBoard'
import { MdLeaderboard, MdRefresh } from 'react-icons/md'
import { formatCurrency } from '@/renderer/utils'
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Legend
} from 'recharts'
import { useTheme } from '@/renderer/contexts'

const { Title } = Typography

const TopProducts = () => {
  const [timeRange, setTimeRange] = useState<TimeRange['days']>(7)
  const { isDarkMode } = useTheme()

  const {
    data,
    isLoading,
    error,
    request: fetchTopProducts
  } = useApi<TopEntity[], [TimeRange]>(dashboardApi.getTopProducts)

  useEffect(() => {
    fetchTopProducts({ days: timeRange })
  }, [])

  const handleTimeRangeChange = (days: TimeRange['days']) => {
    setTimeRange(days)
    fetchTopProducts({ days })
  }

  if (isLoading) {
    return (
      <div className="flex flex-col gap-4">
        <Space className="flex justify-between">
          <Space>
            <MdLeaderboard className="text-2xl text-indigo-600" />
            <Title level={5} className="!mb-0">
              Top Products
            </Title>
          </Space>
          <Button icon={<MdRefresh />} loading />
        </Space>
        <Skeleton active paragraph={{ rows: 4 }} />
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex flex-col gap-4">
        <Space className="flex justify-between">
          <Space>
            <MdLeaderboard className="text-2xl text-indigo-600" />
            <Title level={5} className="!mb-0">
              Top Products
            </Title>
          </Space>
          <Button icon={<MdRefresh />} onClick={() => fetchTopProducts({ days: timeRange })} />
        </Space>
        <Alert
          message="Error"
          description="Failed to load top products data"
          type="error"
          showIcon
        />
      </div>
    )
  }

  if (!data) return null

  return (
    <div className="flex flex-col gap-4">
      <Space className="flex justify-between">
        <Space>
          <MdLeaderboard className="text-2xl text-indigo-600" />
          <Title level={5} className="!mb-0">
            Top Products
          </Title>
        </Space>
        <Space>
          <Radio.Group
            value={timeRange}
            onChange={(e) => handleTimeRangeChange(e.target.value)}
            optionType="button"
            buttonStyle="solid"
            size="small"
          >
            <Radio.Button value={1}>1d</Radio.Button>
            <Radio.Button value={7}>7d</Radio.Button>
            <Radio.Button value={14}>14d</Radio.Button>
            <Radio.Button value={30}>30d</Radio.Button>
          </Radio.Group>
          <Button
            icon={<MdRefresh />}
            onClick={() => fetchTopProducts({ days: timeRange })}
            size="small"
          />
        </Space>
      </Space>

      <div className="h-[300px] w-full">
        <ResponsiveContainer width="100%" height="100%">
          <BarChart
            data={data}
            margin={{
              top: 5,
              right: 30,
              left: 20,
              bottom: 5
            }}
            layout="vertical"
          >
            <CartesianGrid strokeDasharray="3 3" stroke={isDarkMode ? '#374151' : '#E5E7EB'} />
            <XAxis
              type="number"
              stroke={isDarkMode ? '#9CA3AF' : '#4B5563'}
              tick={{ fontSize: 12 }}
              tickFormatter={(value) => formatCurrency(value)}
            />
            <YAxis
              type="category"
              dataKey="name"
              stroke={isDarkMode ? '#9CA3AF' : '#4B5563'}
              tick={{ fontSize: 12 }}
              width={150}
            />
            <Tooltip
              contentStyle={{
                backgroundColor: isDarkMode ? '#1F2937' : '#FFFFFF',
                border: `1px solid ${isDarkMode ? '#374151' : '#E5E7EB'}`,
                borderRadius: '6px'
              }}
              labelStyle={{ color: isDarkMode ? '#9CA3AF' : '#4B5563' }}
              formatter={(value: number) => [formatCurrency(value), 'Sales']}
            />
            <Legend />
            <Bar dataKey="amount" name="Sales" fill="#4F46E5" radius={[0, 4, 4, 0]} />
          </BarChart>
        </ResponsiveContainer>
      </div>
    </div>
  )
}

export default TopProducts
