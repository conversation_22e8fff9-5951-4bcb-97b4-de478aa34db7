import { useEffect, useState } from 'react'
import { useSelector } from 'react-redux'
import { Outlet, useLocation, useNavigate } from 'react-router'
import {
  Ava<PERSON>,
  Badge,
  Button,
  Divider,
  Dropdown,
  Layout,
  Menu,
  MenuProps,
  Modal,
  SiderProps,
  Switch,
  theme
} from 'antd'
import { App_Routes, Menus } from '@/common'
import { Link } from 'react-router-dom'
import { FaBars } from 'react-icons/fa6'
import { IoNotificationsOutline, IoSettingsOutline } from 'react-icons/io5'
import { AiOutlineLogout } from 'react-icons/ai'
import { GoChevronDown } from 'react-icons/go'
import { useTheme } from '@/renderer/contexts/ThemeContext'
import './app-layout.scss'
import { BsMoonStars } from 'react-icons/bs'
import { MdSunny } from 'react-icons/md'
import { IRootState } from '../redux'

const { Sider, Content, Header } = Layout

const AppLayout = () => {
  const navigate = useNavigate()
  const location = useLocation()

  const token = theme.useToken().token
  const user = useSelector((state: IRootState) => state.user.data)
  const [key, setSelectedKey] = useState('')
  const [collapsed, setCollapsed] = useState(false)
  const [isLogoutModalOpen, setIsLogoutModalOpen] = useState(false)

  const { isDarkMode, toggleTheme } = useTheme()

  // useEffect(() => {
  //   if (!user) {
  //     navigate(App_Routes.LOGIN);
  //   }
  // }, [user]);

  useEffect(() => {
    setSelectedKey(location.pathname)
  }, [location.pathname])

  // Force scrollbar visibility in dark mode
  useEffect(() => {
    const style = document.createElement('style')
    style.id = 'dark-scrollbar-fix'

    if (isDarkMode) {
      style.textContent = `
        .app-content::-webkit-scrollbar-thumb,
        .app-sider::-webkit-scrollbar-thumb {
          background: #ffffff !important;
          border: 1px solid #cccccc !important;
          opacity: 1 !important;
        }
        .app-content::-webkit-scrollbar-track,
        .app-sider::-webkit-scrollbar-track {
          background: rgba(255, 255, 255, 0.1) !important;
        }
        .app-content::-webkit-scrollbar,
        .app-sider::-webkit-scrollbar {
          width: 6px !important;
        }
      `
    } else {
      style.textContent = `
        .app-content::-webkit-scrollbar-thumb,
        .app-sider::-webkit-scrollbar-thumb {
          background: rgba(0, 0, 0, 0.3) !important;
          border: 1px solid rgba(0, 0, 0, 0.1) !important;
        }
        .app-content::-webkit-scrollbar-track,
        .app-sider::-webkit-scrollbar-track {
          background: transparent !important;
        }
      `
    }

    document.head.appendChild(style)

    return () => {
      const existingStyle = document.getElementById('dark-scrollbar-fix')
      if (existingStyle) {
        existingStyle.remove()
      }
    }
  }, [isDarkMode])

  const handleKeyChange: MenuProps['onSelect'] = (info) => {
    setSelectedKey(info.key)
    navigate(info.key)
  }
  const handleMenuCollapsed = () => {
    setCollapsed(!collapsed)
  }
  const handleOnCollapsed: SiderProps['onCollapse'] = (collapsed) => {
    setCollapsed(collapsed)
  }

  const handleDropdownClick: MenuProps['onClick'] = (info) => {
    if (info.key === App_Routes.LOGOUT) {
      setIsLogoutModalOpen(true)
    } else {
      navigate(info.key)
    }
  }

  const handleLogout = () => {
    setIsLogoutModalOpen(false)
    navigate(App_Routes.LOGOUT)
  }

  // if (!user) {
  //   return
  // }

  const { colorBgContainer, borderRadiusLG } = token

  // const MenuItems = Menus.filter((el) => el.roles.includes(user.role))
  const MenuItems = Menus

  const dropdownItems: MenuProps['items'] = [
    // {
    //   key: App_Routes.PROFILE,
    //   icon: <TiUserOutline size={18} />,
    //   label: "Profile",
    // },
    // {
    //   key: App_Routes.SETTING,
    //   icon: <IoSettingsOutline size={18} />,
    //   label: 'Setting'
    // },
    {
      key: App_Routes.LOGOUT,
      icon: <AiOutlineLogout className="transition-all duration-0" size={18} />,
      label: 'Logout',
      danger: true
    }
  ]
  return (
    <Layout className="app-layout">
      <Header className="app-header shadow-lg" style={{ background: colorBgContainer }}>
        <Button type="text" onClick={handleMenuCollapsed} className="sider-control-btn">
          <FaBars />
        </Button>
        <div className="app-inner-header flex h-full w-full justify-between">
          <Link to={App_Routes.DASHBOARD} className="logo">
            <img src="/logo.png" alt="" />
            <h3>SJ Lace</h3>
          </Link>
          <div className="user-area flex items-center gap-[5px]">
            {/* <Badge count={4} offset={[-15, 15]}>
              <Button
                type="text"
                className="rounded-full h-[50px] w-[50px] p-[0px] flex items-center justify-center"
              >
                <IoNotificationsOutline className="cursor-pointer" size={20} />
              </Button>
            </Badge> */}

            <Switch
              checked={isDarkMode}
              onChange={toggleTheme}
              checkedChildren={<BsMoonStars className="text-lg" />}
              unCheckedChildren={<MdSunny className="text-lg text-yellow-300" />}
              className={
                isDarkMode
                  ? '!bg-gradient-to-t from-slate-500 to-slate-800 p-0.5 shadow-lg shadow-slate-600'
                  : '!bg-opacity-50 !bg-gradient-to-t from-blue-100 to-blue-400 shadow-lg shadow-blue-300'
              }
            />

            <Divider type="vertical" />
            <div className="flex items-center gap-[30px]">
              <h4 className="cursor-default">Hi, {user?.name || 'User'}</h4>
              <Dropdown
                arrow
                placement="bottomLeft"
                trigger={['click']}
                menu={{ items: dropdownItems, onClick: handleDropdownClick }}
                rootClassName="user-dropdown"
              >
                <div className="flex cursor-pointer items-center gap-[8px]">
                  <Avatar size={40}>{user?.name?.[0] || 'U'}</Avatar>
                  <GoChevronDown size={18} />
                </div>
              </Dropdown>
            </div>
          </div>
        </div>
      </Header>

      <Layout>
        <Sider
          className={`app-sider ${isDarkMode ? '!bg-indigo-950' : '!bg-sky-700'} `}
          collapsed={collapsed}
          collapsedWidth={50}
          breakpoint="lg"
          onCollapse={handleOnCollapsed}
          style={{}}
        >
          <div className="sider-content h-full">
            <Menu
              className={`app-menu h-full transition-colors duration-500 ${isDarkMode ? 'bg-indigo-950' : 'bg-sky-700'}`}
              mode="inline"
              selectedKeys={[key]}
              onSelect={handleKeyChange}
              items={MenuItems}
              theme="dark"
            />
          </div>
        </Sider>
        <Content className="app-content">
          <Outlet />
        </Content>
      </Layout>

      <Modal
        title="Confirm Logout"
        open={isLogoutModalOpen}
        onCancel={() => setIsLogoutModalOpen(false)}
        footer={[
          <Button key="cancel" onClick={() => setIsLogoutModalOpen(false)}>
            No
          </Button>,
          <Button key="submit" type="primary" danger onClick={handleLogout}>
            Yes
          </Button>
        ]}
      >
        <p>Are you sure you want to logout?</p>
      </Modal>
    </Layout>
  )
}

export default AppLayout
