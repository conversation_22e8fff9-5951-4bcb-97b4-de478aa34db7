import { IRequest, IServerResponse } from "@/common";
const ipcRenderer = window.electron.ipcRenderer;
const catchHttp = async (fn: Promise<IServerResponse>) => {
    const result: IServerResponse = {
        data: null,
        error: {
            message: "",
            error: null,
        }
    }
    await Promise.resolve(fn)
        .then(res => {
            result.data = res;
        })
        .catch(err => {
            result.error = {
                message: err.message,
                error: err,
            };
        })
    return result;
}

const ipcInvoker = async (path: string, data: IRequest = {}) => {
    const payload: IRequest = {
        params: data.params || {},
        query: data.query || {},
        body: data.body || {},
    }
    return catchHttp(ipcRenderer.invoke(path, payload));
}

export const http = {
    get: async (path: string, data: IRequest = {}) => ipcInvoker(`GET:${path}`, data),
    post: async (path: string, data: IRequest = {}) => ipcInvoker(`POST:${path}`, data),
    put: async (path: string, data: IRequest = {}) => ipcInvoker(`PUT:${path}`, data),
    delete: async (path: string, data: IRequest = {}) => ipcInvoker(`DELETE:${path}`, data)
}