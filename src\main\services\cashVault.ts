import { prisma } from '../db';
import { CashVaultTransactionFilters, CashVaultTransactionsResponse, InitializeVaultData, CashVaultReconcileResponse, VaultTransactionData, GetCashVaultStatementParams, CashVaultStatement, CashVaultStatementEntry, CashVaultStatementSummary } from '@/common/types';
import { processDateRange } from '../utils/helperFunctions';

class CashVaultService {
    private async ensureVaultExists() {
        const vault = await prisma.cashVault.findFirst();
        if (!vault) {
            throw new Error('Cash vault not initialized');
        }
        return vault;
    }

    async isInitialized(): Promise<boolean> {
        const vault = await prisma.cashVault.findFirst();
        return !!vault;
    }

    async initializeVault(data: InitializeVaultData): Promise<void> {
        // Validate amount is positive
        if (data.amount < 0) {
            throw new Error('Amount cannot be negative');
        }

        const vault = await prisma.cashVault.findFirst();
        if (vault) {
            throw new Error('Cash vault already initialized');
        }

        return await prisma.$transaction(async (tx) => {
            await tx.cashVault.create({
                data: { balance: data.amount }
            });

            await tx.ledger.create({
                data: {
                    amount: data.amount,
                    description: 'Cash vault Opening Balance',
                    creditOrDebit: 'CREDIT',
                    referenceType: 'OpeningBalance',
                    date: new Date(),
                    status: 'ACTIVE',
                    cashDestination: 'CASH_VAULT',
                    createdById: data.adminId
                }
            });
        });
    }

    async getCurrentBalance(): Promise<number> {
        const vault = await this.ensureVaultExists();
        return vault.balance;
    }

    async depositToVault(data: VaultTransactionData): Promise<void> {
        // Validate amount is positive
        if (data.amount <= 0) {
            throw new Error('Deposit amount must be positive');
        }

        await this.ensureVaultExists();

        return await prisma.$transaction(async (tx) => {
            // Add to vault balance
            await tx.cashVault.updateMany({
                data: {
                    balance: { increment: data.amount }
                }
            });

            // Create ledger entry
            await tx.ledger.create({
                data: {
                    amount: data.amount,
                    description: data.description,
                    creditOrDebit: 'CREDIT',
                    referenceType: 'Vault',
                    date: new Date(),
                    status: 'ACTIVE',
                    cashDestination: 'CASH_VAULT',
                    createdById: data.adminId
                }
            });
        });
    }

    async withdrawFromVault(data: VaultTransactionData): Promise<void> {
        // Validate amount is positive
        if (data.amount <= 0) {
            throw new Error('Withdrawal amount must be positive');
        }

        const vault = await this.ensureVaultExists();
        if (vault.balance < data.amount) {
            throw new Error('Insufficient balance in vault');
        }

        return await prisma.$transaction(async (tx) => {
            // Remove from vault balance
            await tx.cashVault.updateMany({
                data: {
                    balance: { decrement: data.amount }
                }
            });

            // Create ledger entry
            await tx.ledger.create({
                data: {
                    amount: data.amount,
                    description: data.description,
                    creditOrDebit: 'DEBIT',
                    referenceType: 'Vault',
                    date: new Date(),
                    status: 'ACTIVE',
                    cashSource: 'CASH_VAULT',
                    createdById: data.adminId
                }
            });
        });
    }

    async getAllTransactions(filters: CashVaultTransactionFilters): Promise<CashVaultTransactionsResponse> {
        const { page = 1, pageSize = 20, startDate, endDate, includeDeleted = false } = filters;
        const skip = (page - 1) * pageSize;

        // Use helper function from utils for date filtering
        const dateFilter = processDateRange(startDate, endDate);

        const [transactions, total] = await Promise.all([
            prisma.ledger.findMany({
                where: {
                    OR: [
                        { cashSource: 'CASH_VAULT' },
                        { cashDestination: 'CASH_VAULT' }
                    ],
                    status: includeDeleted ? undefined : 'ACTIVE',
                    ...(dateFilter && { date: dateFilter })
                },
                include: {
                    createdBy: { select: { name: true } }
                },
                orderBy: { date: 'desc' },
                skip,
                take: pageSize
            }),
            prisma.ledger.count({
                where: {
                    OR: [
                        { cashSource: 'CASH_VAULT' },
                        { cashDestination: 'CASH_VAULT' }
                    ],
                    status: includeDeleted ? undefined : 'ACTIVE',
                    ...(dateFilter && { date: dateFilter })
                }
            })
        ]);

        return {
            transactions,
            pagination: {
                total,
                pages: Math.ceil(total / pageSize),
                currentPage: page,
                pageSize
            }
        };
    }

    async reconcileVault(): Promise<CashVaultReconcileResponse> {
        const vault = await this.ensureVaultExists();

        return await prisma.$transaction(async (tx) => {
            const [credits, debits] = await Promise.all([
                tx.ledger.aggregate({
                    where: {
                        status: 'ACTIVE',
                        creditOrDebit: 'CREDIT',
                        OR: [
                            { cashDestination: 'CASH_VAULT' },
                            { cashSource: 'CASH_VAULT' }
                        ]
                    },
                    _sum: {
                        amount: true
                    }
                }),
                tx.ledger.aggregate({
                    where: {
                        status: 'ACTIVE',
                        creditOrDebit: 'DEBIT',
                        OR: [
                            { cashSource: 'CASH_VAULT' },
                            { cashDestination: 'CASH_VAULT' }
                        ]
                    },
                    _sum: { amount: true }
                })
            ]);

            // Calculate the net balance
            const calculatedBalance = (credits._sum.amount ?? 0) - (debits._sum.amount ?? 0);

            // Get current balance from the vault
            const currentBalance = vault.balance;
            const isReconciled = calculatedBalance === currentBalance;

            return {
                isReconciled,
                currentBalance,
                calculatedBalance,
                difference: currentBalance - calculatedBalance
            };
        });
    }

    async generateCashVaultStatement(params: GetCashVaultStatementParams): Promise<CashVaultStatement> {
        const { startDate, endDate, page = 1, pageSize = 100 } = params;

        // Validate input
        if (!startDate || !endDate) {
            throw new Error('Start date and end date are required');
        }

        // Helper function to process date range for queries
        const processDateRange = (start: Date, end: Date) => {
            if (start.toDateString() === end.toDateString()) {
                // Same day - filter for entire day
                const startOfDay = new Date(start);
                startOfDay.setHours(0, 0, 0, 0);
                const endOfDay = new Date(start);
                endOfDay.setHours(23, 59, 59, 999);
                return {
                    gte: startOfDay,
                    lte: endOfDay
                };
            } else {
                return {
                    gte: start,
                    lte: end
                };
            }
        };

        // Calculate opening balance (sum of all transactions before start date)
        const creditsBefore = await prisma.ledger.aggregate({
            where: {
                cashDestination: 'CASH_VAULT',
                date: { lt: startDate },
                status: 'ACTIVE'
            },
            _sum: { amount: true }
        });

        const debitsBefore = await prisma.ledger.aggregate({
            where: {
                cashSource: 'CASH_VAULT',
                date: { lt: startDate },
                status: 'ACTIVE'
            },
            _sum: { amount: true }
        });

        // Calculate opening balance
        const openingBalance = (creditsBefore._sum.amount || 0) - (debitsBefore._sum.amount || 0);

        // Get total count for pagination
        const total = await prisma.ledger.count({
            where: {
                OR: [
                    { cashDestination: 'CASH_VAULT' },
                    { cashSource: 'CASH_VAULT' }
                ],
                date: processDateRange(startDate, endDate),
                status: 'ACTIVE'
            }
        });

        // Fetch transactions for the current page
        const transactions = await prisma.ledger.findMany({
            where: {
                OR: [
                    { cashDestination: 'CASH_VAULT' },
                    { cashSource: 'CASH_VAULT' }
                ],
                date: processDateRange(startDate, endDate),
                status: 'ACTIVE'
            },
            include: {
                createdBy: { select: { name: true } }
            },
            orderBy: { date: 'asc' },
            skip: (page - 1) * pageSize,
            take: pageSize
        });

        // Calculate running balance and prepare statement entries
        let runningBalance = openingBalance;
        let totalCredits = 0;
        let totalDebits = 0;

        const statementEntries: CashVaultStatementEntry[] = transactions.map(tx => {
            // Determine if this is a credit or debit for the cash vault
            const isCashVaultCredit = tx.cashDestination === 'CASH_VAULT';

            // Update running balance based on transaction type
            if (isCashVaultCredit) {
                runningBalance += tx.amount;
                totalCredits += tx.amount;
            } else {
                runningBalance -= tx.amount;
                totalDebits += tx.amount;
            }

            // Create statement entry with credit/debit columns
            return {
                id: tx.id,
                date: tx.date,
                description: tx.description || '',
                credit: isCashVaultCredit ? tx.amount : null,
                debit: !isCashVaultCredit ? tx.amount : null,
                runningBalance,
                createdBy: tx.createdBy
            };
        });

        // Calculate summary
        const summary: CashVaultStatementSummary = {
            totalCredits,
            totalDebits,
            net: totalCredits - totalDebits
        };

        return {
            startDate,
            endDate,
            entries: statementEntries,
            openingBalance,
            closingBalance: runningBalance,
            summary,
            pagination: {
                total,
                pages: Math.ceil(total / pageSize),
                currentPage: page,
                pageSize
            }
        };
    }
}

export const cashVaultService = new CashVaultService();