import { prisma } from '../db';
import { CreatePaymentData, GetPaymentsParams, PaymentDateRangeParams, LocationTransferData, PartyTransferData, GetTransfersParams, GetTransfersResponse, VoidTransferParams } from '../../common/types';
import { Prisma, CashLocation, LedgerType } from '@prisma/client';
import { processDateRange } from '../utils/helperFunctions';


class PaymentService {
    async createPayment(data: CreatePaymentData) {
        // Validate amount is positive
        if (data.amount <= 0) {
            throw new Error('Payment amount must be positive');
        }

        return await prisma.$transaction(async (tx) => {
            // Validate party exists if partyId is provided
            if (data.partyId) {
                const party = await tx.party.findUnique({
                    where: { id: data.partyId }
                });
                if (!party) throw new Error('Party not found');
            }

            if (data.type === 'PAID') {
                // Handle source balance check and update
                switch (data.source) {
                    case 'SMALL_COUNTER':
                        const cashBalance = await tx.smallCounter.findFirst();
                        if (!cashBalance || cashBalance.cashInShop < data.amount) {
                            throw new Error('Insufficient cash in counter');
                        }
                        await tx.smallCounter.updateMany({
                            data: { cashInShop: { decrement: data.amount } }
                        });
                        break;

                    case 'CASH_VAULT':
                        const vault = await tx.cashVault.findFirst();
                        if (!vault || vault.balance < data.amount) {
                            throw new Error('Insufficient funds in vault');
                        }
                        await tx.cashVault.updateMany({
                            data: { balance: { decrement: data.amount } }
                        });
                        break;

                    case 'BANK':
                        if (!data.locationId) throw new Error('Bank ID is required');
                        const bank = await tx.banks.findUnique({
                            where: { id: data.locationId }
                        });
                        if (!bank || bank.balance < data.amount) {
                            throw new Error('Insufficient funds in bank');
                        }
                        await tx.banks.update({
                            where: { id: data.locationId },
                            data: { balance: { decrement: data.amount } }
                        });
                        break;
                }
            } else {
                // Handle destination balance update (increment)
                switch (data.source) {
                    case 'SMALL_COUNTER':
                        const cashAccount = await tx.smallCounter.findFirst();
                        if (!cashAccount) throw new Error('Cash account not initialized');
                        await tx.smallCounter.update({
                            where: { id: cashAccount.id },
                            data: { cashInShop: { increment: data.amount } }
                        });
                        break;

                    case 'CASH_VAULT':
                        const vault = await tx.cashVault.findFirst();
                        if (!vault) throw new Error('Vault not found');
                        await tx.cashVault.update({
                            where: { id: vault.id },
                            data: { balance: { increment: data.amount } }
                        });
                        break;

                    case 'BANK':
                        if (!data.locationId) throw new Error('Bank ID is required');
                        await tx.banks.update({
                            where: { id: data.locationId },
                            data: { balance: { increment: data.amount } }
                        });
                        break;
                }
            }

            // Create payment record
            const payment = await tx.payments.create({
                data: {
                    amount: data.amount,
                    date: data.date,
                    type: data.type,
                    partyId: data.partyId,
                    description: data.description || '',
                    createdById: data.createdById,
                    sourceLocation: data.type === 'PAID' ? data.source : null,
                    destinationLocation: data.type === 'RECEIVED' ? data.source : null,
                    locationId: data.source === 'BANK' ? data.locationId : null,
                    status: 'ACTIVE',
                    paymentMethod: data.paymentMethod
                }
            });

            // Create single ledger entry
            await tx.ledger.create({
                data: {
                    amount: data.amount,
                    date: data.date,
                    description: data.description || '',
                    creditOrDebit: data.type === 'RECEIVED' ? 'CREDIT' : 'DEBIT',
                    referenceType: 'Payment',
                    paymentRef: payment.id,
                    status: 'ACTIVE',
                    partyId: data.partyId,
                    bankId: data.source === 'BANK' ? data.locationId : null,
                    cashSource: data.type === 'PAID' ? data.source : null,
                    cashDestination: data.type === 'RECEIVED' ? data.source : null,
                    createdById: data.createdById
                }
            });

            // Update party balance if party exists
            if (data.partyId) {
                await tx.party.update({
                    where: { id: data.partyId },
                    data: {
                        currentBalance: {
                            [data.type === 'RECEIVED' ? 'increment' : 'decrement']: data.amount
                        }
                    }
                });
            }

            return payment;
        });
    }

    async voidPayment(id: string, adminId: string, reason: string) {
        return await prisma.$transaction(async (tx) => {
            const payment = await tx.payments.findUnique({
                where: { id },
                include: {
                    party: true,
                    Ledger: true,
                    WalkInSaleInvoice: true,
                    PurchaseInvoice: true,
                    SaleInvoice: true
                }
            });

            if (!payment) throw new Error('Payment not found');
            if (payment.status === 'VOID') throw new Error('Payment already voided');

            // Check if payment is linked to a sale or purchase
            if (payment.WalkInSaleInvoice || payment.PurchaseInvoice || payment.SaleInvoice) {
                throw new Error('Cannot void payment directly. Please void the associated invoice.');
            }

            // Restore balances based on payment type
            if (payment.type === 'PAID') {
                // Restore source balance
                switch (payment.sourceLocation) {
                    case 'SMALL_COUNTER':
                        await tx.smallCounter.updateMany({
                            data: { cashInShop: { increment: payment.amount } }
                        });
                        break;

                    case 'CASH_VAULT':
                        await tx.cashVault.updateMany({
                            data: { balance: { increment: payment.amount } }
                        });
                        break;

                    case 'BANK':
                        if (!payment.locationId) throw new Error('Bank ID not found');
                        await tx.banks.update({
                            where: { id: payment.locationId },
                            data: { balance: { increment: payment.amount } }
                        });
                        break;
                }
            } else {
                // Handle RECEIVED payment void
                switch (payment.destinationLocation) {
                    case 'SMALL_COUNTER':
                        await tx.smallCounter.updateMany({
                            data: { cashInShop: { decrement: payment.amount } }
                        });
                        break;

                    case 'CASH_VAULT':
                        await tx.cashVault.updateMany({
                            data: { balance: { decrement: payment.amount } }
                        });
                        break;

                    case 'BANK':
                        if (!payment.locationId) throw new Error('Bank ID not found');
                        await tx.banks.update({
                            where: { id: payment.locationId },
                            data: { balance: { decrement: payment.amount } }
                        });
                        break;
                }
            }

            // Update payment status
            await tx.payments.update({
                where: { id },
                data: {
                    status: 'VOID',
                    voidedById: adminId,
                    voidedAt: new Date(),
                    voidingReason: reason
                }
            });

            // Void all ledger entries
            await tx.ledger.updateMany({
                where: { paymentRef: id },
                data: { status: 'VOID' }
            });

            // Restore party balance if party exists
            if (payment.partyId) {
                await tx.party.update({
                    where: { id: payment.partyId },
                    data: {
                        currentBalance: {
                            [payment.type === 'RECEIVED' ? 'decrement' : 'increment']: payment.amount
                        }
                    }
                });
            }

            return payment;
        });
    }

    // Query functions with proper pagination
    async getPaymentById(id: string) {
        return await prisma.payments.findUnique({
            where: { id },
            include: {
                party: true,
                Ledger: true,
                createdBy: true,
                voidedBy: true
            }
        });
    }

    async getPayments({ startDate, endDate, page = 1, limit = 10, type, status, partyId }: GetPaymentsParams) {
        const skip = (page - 1) * limit;
        const where: any = {};

        const dateRange = processDateRange(startDate, endDate);
        if (dateRange) {
            where.date = dateRange;
        }

        if (type) where.type = type;
        if (status && status !== 'ALL') where.status = status;
        if (partyId) where.partyId = partyId;

        const [payments, total] = await Promise.all([
            prisma.payments.findMany({
                skip,
                take: limit,
                where,
                orderBy: { date: 'desc' },
                select: {
                    id: true,
                    date: true,
                    amount: true,
                    type: true,
                    status: true,
                    paymentMethod: true,
                    description: true,
                    party: {
                        select: {
                            name: true
                        }
                    },
                    sourceLocation: true,
                    destinationLocation: true,
                    createdBy: {
                        select: {
                            name: true
                        }
                    },
                    voidedBy: {
                        select: {
                            name: true
                        }
                    },
                    voidedAt: true,
                    voidingReason: true
                }
            }),
            prisma.payments.count({ where })
        ]);

        return {
            payments,
            total,
            page,
            totalPages: Math.ceil(total / limit)
        };
    }

    // TODO: remove this get payments by date range function it is useless and wont be used
    async getPaymentsByDateRange({ startDate, endDate, page = 1, limit = 10 }: PaymentDateRangeParams) {


        const filterDateRange = processDateRange(startDate, endDate);

        const [payments, total] = await Promise.all([
            prisma.payments.findMany({
                where: {
                    status: 'ACTIVE',
                    ...(startDate || endDate ? {
                        date: filterDateRange
                    } : {})
                },
                orderBy: { date: 'desc' },
                skip: (page - 1) * limit,
                take: limit,
                include: {
                    party: true,
                    Ledger: true,
                    createdBy: true,
                    voidedBy: true
                }
            }),
            prisma.payments.count({
                where: {
                    status: 'ACTIVE',
                    ...(startDate || endDate ? {
                        date: filterDateRange
                    } : {})
                }
            })
        ]);

        return {
            payments,
            total,
            page,
            totalPages: Math.ceil(total / limit)
        };
    }

    private async validateLocation(tx: Prisma.TransactionClient, location: CashLocation, locationId?: string) {
        switch (location) {
            case 'SMALL_COUNTER':
                const smallCounter = await tx.smallCounter.findFirst();
                if (!smallCounter) throw new Error('Small counter not found');
                return smallCounter;
            case 'CASH_VAULT':
                const vault = await tx.cashVault.findFirst();
                if (!vault) throw new Error('Vault not found');
                return vault;
            case 'BANK':
                if (!locationId) throw new Error('Bank ID is required');
                const bank = await tx.banks.findUnique({ where: { id: locationId } });
                if (!bank) throw new Error('Bank not found');
                return bank;
            default:
                throw new Error('Invalid location');
        }
    }

    private async validateLocationBalance(location: any, amount: number, source: CashLocation) {
        switch (source) {
            case 'SMALL_COUNTER':
                if (location.cashInShop < amount) throw new Error('Insufficient cash in counter');
                break;
            case 'CASH_VAULT':
                if (location.balance < amount) throw new Error('Insufficient funds in vault');
                break;
            case 'BANK':
                if (location.balance < amount) throw new Error('Insufficient funds in bank');
                break;
        }
    }

    private async updateLocationBalance(
        tx: Prisma.TransactionClient,
        location: CashLocation,
        locationId: string | undefined,
        amount: number,
        isIncrement: boolean
    ) {
        const operation = isIncrement ? 'increment' : 'decrement';

        switch (location) {
            case 'SMALL_COUNTER':
                const counter = await this.validateLocation(tx, 'SMALL_COUNTER');
                if (!counter) throw new Error('Small counter not found');
                await tx.smallCounter.update({
                    where: { id: counter.id },
                    data: { cashInShop: { [operation]: amount } }
                });
                break;

            case 'CASH_VAULT':
                const vault = await this.validateLocation(tx, 'CASH_VAULT');
                if (!vault) throw new Error('Cash vault not found');
                await tx.cashVault.update({
                    where: { id: vault.id },
                    data: { balance: { [operation]: amount } }
                });
                break;

            case 'BANK':
                if (!locationId) throw new Error('Bank ID is required');
                await tx.banks.update({
                    where: { id: locationId },
                    data: { balance: { [operation]: amount } }
                });
                break;
        }
    }

    async transferBetweenLocations(data: LocationTransferData) {
        // Validate amount is positive
        if (data.amount <= 0) {
            throw new Error('Transfer amount must be positive');
        }

        return await prisma.$transaction(async (tx) => {
            // Validate same location transfer
            if (data.fromLocation === data.toLocation) {
                if (data.fromLocation === 'BANK' && data.fromLocationId !== data.toLocationId) {
                    // Allow transfer between different bank accounts
                } else {
                    throw new Error('Cannot transfer to the same location');
                }
            }

            // Validate locations exist and check balances
            const sourceLocation = await this.validateLocation(tx, data.fromLocation, data.fromLocationId);
            await this.validateLocationBalance(sourceLocation, data.amount, data.fromLocation);
            await this.validateLocation(tx, data.toLocation, data.toLocationId);

            // Create ledger entries for the transfer
            const transferRef = await tx.ledger.create({
                data: {
                    amount: data.amount,
                    date: data.date,
                    description: data.description,
                    creditOrDebit: 'DEBIT',
                    referenceType: 'Transfer' as LedgerType,
                    status: 'ACTIVE',
                    bankId: data.fromLocation === 'BANK' ? data.fromLocationId : null,
                    cashSource: data.fromLocation,
                    createdById: data.createdById
                }
            });

            await tx.ledger.create({
                data: {
                    amount: data.amount,
                    date: data.date,
                    description: data.description,
                    creditOrDebit: 'CREDIT',
                    referenceType: 'Transfer' as LedgerType,
                    status: 'ACTIVE',
                    bankId: data.toLocation === 'BANK' ? data.toLocationId : null,
                    cashDestination: data.toLocation,
                    createdById: data.createdById
                }
            });

            // Update balances
            await this.updateLocationBalance(tx, data.fromLocation, data.fromLocationId, data.amount, false);
            await this.updateLocationBalance(tx, data.toLocation, data.toLocationId, data.amount, true);

            // Verify final balances
            const finalSourceLocation = await this.validateLocation(tx, data.fromLocation, data.fromLocationId);
            const finalDestLocation = await this.validateLocation(tx, data.toLocation, data.toLocationId);

            // Additional balance verification
            if (data.fromLocation === data.toLocation && data.fromLocation === 'BANK') {
                const sourceBalance = (finalSourceLocation as any).balance;
                const destBalance = (finalDestLocation as any).balance;
                if (sourceBalance < 0 || destBalance < 0) {
                    throw new Error('Transfer would result in negative balance');
                }
            }

            return transferRef;
        });
    }

    async transferBetweenParties(data: PartyTransferData) {
        // Validate amount is positive
        if (data.amount <= 0) {
            throw new Error('Transfer amount must be positive');
        }

        return await prisma.$transaction(async (tx) => {
            // Validate parties exist
            const [fromParty, toParty] = await Promise.all([
                tx.party.findUnique({ where: { id: data.fromPartyId } }),
                tx.party.findUnique({ where: { id: data.toPartyId } })
            ]);

            if (!fromParty) throw new Error('Source party not found');
            if (!toParty) throw new Error('Destination party not found');
            if (fromParty.id === toParty.id) throw new Error('Cannot transfer to the same party');

            // Create PAID payment for source party
            const sourcePayment = await tx.payments.create({
                data: {
                    amount: data.amount,
                    date: data.date,
                    type: 'PAID',
                    description: `${data.description} (Transfer to ${toParty.name})`,
                    createdById: data.createdById,
                    partyId: data.fromPartyId,
                    status: 'ACTIVE',
                    paymentMethod: data.paymentMethod,
                }
            });

            // Create RECEIVED payment for destination party
            const destPayment = await tx.payments.create({
                data: {
                    amount: data.amount,
                    date: data.date,
                    type: 'RECEIVED',
                    description: `${data.description} (Transfer from ${fromParty.name})`,
                    createdById: data.createdById,
                    partyId: data.toPartyId,
                    status: 'ACTIVE',
                    paymentMethod: data.paymentMethod,
                }
            });

            // Create ledger entries
            await tx.ledger.create({
                data: {
                    amount: data.amount,
                    date: data.date,
                    description: `${data.description} (Debit from ${fromParty.name})`,
                    creditOrDebit: 'DEBIT',
                    referenceType: 'Account_Transfer' as LedgerType,
                    paymentRef: sourcePayment.id,
                    status: 'ACTIVE',
                    partyId: data.fromPartyId,
                    createdById: data.createdById
                }
            });

            await tx.ledger.create({
                data: {
                    amount: data.amount,
                    date: data.date,
                    description: `${data.description} (Credit to ${toParty.name})`,
                    creditOrDebit: 'CREDIT',
                    referenceType: 'Account_Transfer' as LedgerType,
                    paymentRef: destPayment.id,
                    status: 'ACTIVE',
                    partyId: data.toPartyId,
                    createdById: data.createdById
                }
            });

            // Update party balances
            await Promise.all([
                tx.party.update({
                    where: { id: data.fromPartyId },
                    data: { currentBalance: { decrement: data.amount } }
                }),
                tx.party.update({
                    where: { id: data.toPartyId },
                    data: { currentBalance: { increment: data.amount } }
                })
            ]);

            // Verify final balances
            const [finalSourceParty, finalDestParty] = await Promise.all([
                tx.party.findUnique({ where: { id: data.fromPartyId } }),
                tx.party.findUnique({ where: { id: data.toPartyId } })
            ]);

            if (!finalSourceParty || !finalDestParty) {
                throw new Error('Failed to verify final balances');
            }

            return { sourcePayment, destPayment };
        });
    }

    async getTransfers({
        page = 1,
        limit = 10,
        startDate,
        endDate,
        fromLocation,
        toLocation,
        status,
    }: GetTransfersParams): Promise<GetTransfersResponse> {
        const where: Prisma.LedgerWhereInput = {
            referenceType: 'Transfer',
            ...(status && { status }),
            ...(startDate && { date: { gte: startDate } }),
            ...(endDate && { date: { lte: endDate } }),
            ...(fromLocation && { cashSource: fromLocation }),
            ...(toLocation && { cashDestination: toLocation })
        };

        const [transfers, total] = await Promise.all([
            prisma.ledger.findMany({
                where,
                select: {
                    id: true,
                    date: true,
                    amount: true,
                    description: true,
                    creditOrDebit: true,
                    status: true,
                    cashSource: true,
                    cashDestination: true,
                    bankId: true,
                    bank: {
                        select: {
                            name: true,
                            accountNo: true
                        }
                    },
                    createdBy: {
                        select: {
                            name: true
                        }
                    },
                    voidedBy: {
                        select: {
                            name: true
                        }
                    }
                },
                orderBy: { date: 'desc' },
                skip: (page - 1) * limit,
                take: limit
            }),
            prisma.ledger.count({ where })
        ]);

        return {
            transfers: transfers.map(transfer => ({
                ...transfer,
                cashSource: transfer.cashSource || undefined,
                cashDestination: transfer.cashDestination || undefined,
                bankId: transfer.bankId || undefined,
                bank: transfer.bank || undefined,
                voidedBy: transfer.voidedBy || undefined
            })),
            total,
            page,
            totalPages: Math.ceil(total / limit)
        };
    }

    async voidTransfer({ id, adminId, reason }: VoidTransferParams) {
        return await prisma.$transaction(async (tx) => {
            // Get the transfer entry
            const transfer = await tx.ledger.findUnique({
                where: { id },
                select: {
                    amount: true,
                    creditOrDebit: true,
                    cashSource: true,
                    cashDestination: true,
                    bankId: true
                }
            });

            if (!transfer) throw new Error('Transfer not found');
            if (transfer.creditOrDebit === 'DEBIT') {
                // If this is a debit entry, we need to increment the source balance
                if (transfer.cashSource) {
                    await this.updateLocationBalance(
                        tx,
                        transfer.cashSource,
                        transfer.bankId || undefined,
                        transfer.amount,
                        true
                    );
                }
            } else {
                // If this is a credit entry, we need to decrement the destination balance
                if (transfer.cashDestination) {
                    await this.updateLocationBalance(
                        tx,
                        transfer.cashDestination,
                        transfer.bankId || undefined,
                        transfer.amount,
                        false
                    );
                }
            }

            // Update the transfer status
            return await tx.ledger.update({
                where: { id },
                data: {
                    status: 'VOID',
                    voidedById: adminId
                }
            });
        });
    }
}




export const paymentService = new PaymentService();