import { prisma } from '../db';
import { InitializeSmallCounterData, SmallCounterReconcileResponse, SmallCounterTransactionFilters, SmallCounterTransactionsResponse, SmallCounterStatementEntry, SmallCounterStatementSummary, SmallCounterStatement, GetSmallCounterStatementParams } from '../../common/types/smallCounter';

class SmallCounterService {

    private async ensureSmallCounterExists() {
        const smallCounter = await prisma.smallCounter.findFirst();
        if (!smallCounter) {
            throw new Error('Small counter not initialized');
        }
        return smallCounter;
    }

    async initializeSmallCounter(data: InitializeSmallCounterData): Promise<void> {
        // Validate amount is positive
        if (data.amount < 0) {
            throw new Error('Amount cannot be negative');
        }

        const smallCounter = await prisma.smallCounter.findFirst();
        if (smallCounter) {
            throw new Error('Small counter already initialized');
        }

        return await prisma.$transaction(async (tx) => {
            await tx.smallCounter.create({
                data: { cashInShop: data.amount }
            });

            await tx.ledger.create({
                data: {
                    amount: data.amount,
                    description: 'Small counter Opening Balance',
                    creditOrDebit: 'CREDIT',
                    referenceType: 'OpeningBalance',
                    date: new Date(),
                    status: 'ACTIVE',
                    cashDestination: 'SMALL_COUNTER',
                    createdById: data.adminId
                }
            });
        });
    }


    async getCurrentBalance(): Promise<number> {
        const smallCounter = await this.ensureSmallCounterExists();
        return smallCounter.cashInShop;
    }


    async transferToVault(amount: number, adminId: string): Promise<void> {
        // Validate amount is positive
        if (amount <= 0) {
            throw new Error('Transfer amount must be positive');
        }

        return await prisma.$transaction(async (tx) => {
            const currentBalance = await this.getCurrentBalance();
            if (currentBalance < amount) {
                throw new Error('Insufficient cash in counter');
            }

            // Remove from cash account
            await tx.smallCounter.updateMany({
                data: {
                    cashInShop: { decrement: amount }
                }
            });

            // Add to vault
            await tx.cashVault.updateMany({
                data: {
                    balance: { increment: amount }
                }
            });

            // Create ledger entries
            await tx.ledger.create({
                data: {
                    amount,
                    description: 'Daily cash transfer from counter',
                    creditOrDebit: 'DEBIT',
                    referenceType: 'SmallCounter',
                    date: new Date(),
                    status: 'ACTIVE',
                    cashSource: 'SMALL_COUNTER',
                    createdById: adminId
                }
            });

            await tx.ledger.create({
                data: {
                    amount,
                    description: 'Daily cash transfer from counter',
                    creditOrDebit: 'CREDIT',
                    referenceType: 'Vault',
                    date: new Date(),
                    status: 'ACTIVE',
                    cashDestination: 'CASH_VAULT',
                    createdById: adminId
                }
            });

        });
    }

    async reconcileSmallCounter(): Promise<SmallCounterReconcileResponse> {
        const smallCounter = await this.ensureSmallCounterExists();

        return await prisma.$transaction(async (tx) => {
            const [credits, debits] = await Promise.all([
                tx.ledger.aggregate({
                    where: {
                        status: 'ACTIVE',
                        creditOrDebit: 'CREDIT',
                        OR: [
                            { cashDestination: 'SMALL_COUNTER' },
                            { cashSource: 'SMALL_COUNTER' }
                        ]
                    },
                    _sum: {
                        amount: true
                    }

                }),
                tx.ledger.aggregate({
                    where: {
                        status: 'ACTIVE',
                        creditOrDebit: 'DEBIT',
                        OR: [
                            { cashSource: 'SMALL_COUNTER' },
                            { cashDestination: 'SMALL_COUNTER' }
                        ]
                    },
                    _sum: { amount: true }
                })
            ]);

            // Calculate the net balance
            const calculatedBalance = (credits._sum.amount ?? 0) - (debits._sum.amount ?? 0);

            // Get current balance from the counter
            const currentBalance = smallCounter.cashInShop;
            const isReconciled = calculatedBalance === currentBalance;

            return {
                isReconciled,
                currentBalance,
                calculatedBalance,
                difference: currentBalance - calculatedBalance
            };
        });
    }

    async getAllTransactions(filters: SmallCounterTransactionFilters): Promise<SmallCounterTransactionsResponse> {
        const { page = 1, pageSize = 20, startDate, endDate, includeDeleted = false } = filters;
        const skip = (page - 1) * pageSize;

        // Use helper function from utils for date filtering
        let dateFilter = {};
        if (startDate && endDate) {
            if (startDate.toDateString() === endDate.toDateString()) {
                // Same day - filter for entire day
                const start = new Date(startDate);
                start.setHours(0, 0, 0, 0);
                const end = new Date(startDate);
                end.setHours(23, 59, 59, 999);
                dateFilter = {
                    date: {
                        gte: start,
                        lte: end
                    }
                };
            } else {
                // Date range
                dateFilter = {
                    date: {
                        gte: startDate,
                        lte: endDate
                    }
                };
            }
        }

        const [transactions, total] = await Promise.all([
            prisma.ledger.findMany({
                where: {
                    OR: [
                        { cashSource: 'SMALL_COUNTER' },
                        { cashDestination: 'SMALL_COUNTER' }
                    ],
                    status: includeDeleted ? undefined : 'ACTIVE',
                    ...dateFilter
                },
                include: {
                    createdBy: { select: { name: true } }
                },
                orderBy: { date: 'desc' },
                skip,
                take: pageSize
            }),
            prisma.ledger.count({
                where: {
                    OR: [
                        { cashSource: 'SMALL_COUNTER' },
                        { cashDestination: 'SMALL_COUNTER' }
                    ],
                    status: includeDeleted ? undefined : 'ACTIVE',
                    ...dateFilter
                }
            })
        ]);

        return {
            transactions,
            pagination: {
                total,
                pages: Math.ceil(total / pageSize),
                currentPage: page,
                pageSize
            }
        };
    }

    async generateSmallCounterStatement(params: GetSmallCounterStatementParams): Promise<SmallCounterStatement> {
        const { startDate, endDate, page = 1, pageSize = 100 } = params;

        // Validate input
        if (!startDate || !endDate) {
            throw new Error('Start date and end date are required');
        }

        // Helper function to process date range for queries
        const processDateRange = (start: Date, end: Date) => {
            if (start.toDateString() === end.toDateString()) {
                // Same day - filter for entire day
                const startOfDay = new Date(start);
                startOfDay.setHours(0, 0, 0, 0);
                const endOfDay = new Date(start);
                endOfDay.setHours(23, 59, 59, 999);
                return {
                    gte: startOfDay,
                    lte: endOfDay
                };
            } else {
                return {
                    gte: start,
                    lte: end
                };
            }
        };

        // Calculate opening balance (sum of all transactions before start date)
        const creditsBefore = await prisma.ledger.aggregate({
            where: {
                cashDestination: 'SMALL_COUNTER',
                date: { lt: startDate },
                status: 'ACTIVE'
            },
            _sum: { amount: true }
        });

        const debitsBefore = await prisma.ledger.aggregate({
            where: {
                cashSource: 'SMALL_COUNTER',
                date: { lt: startDate },
                status: 'ACTIVE'
            },
            _sum: { amount: true }
        });

        // Calculate opening balance
        const openingBalance = (creditsBefore._sum.amount || 0) - (debitsBefore._sum.amount || 0);

        // Get total count for pagination
        const total = await prisma.ledger.count({
            where: {
                OR: [
                    { cashDestination: 'SMALL_COUNTER' },
                    { cashSource: 'SMALL_COUNTER' }
                ],
                date: processDateRange(startDate, endDate),
                status: 'ACTIVE'
            }
        });

        // Fetch transactions for the current page
        const transactions = await prisma.ledger.findMany({
            where: {
                OR: [
                    { cashDestination: 'SMALL_COUNTER' },
                    { cashSource: 'SMALL_COUNTER' }
                ],
                date: processDateRange(startDate, endDate),
                status: 'ACTIVE'
            },
            include: {
                createdBy: { select: { name: true } }
            },
            orderBy: { date: 'asc' },
            skip: (page - 1) * pageSize,
            take: pageSize
        });

        // Calculate running balance and prepare statement entries
        let runningBalance = openingBalance;
        let totalCredits = 0;
        let totalDebits = 0;

        const statementEntries: SmallCounterStatementEntry[] = transactions.map(tx => {
            // Determine if this is a credit or debit for the small counter
            const isSmallCounterCredit = tx.cashDestination === 'SMALL_COUNTER';

            // Update running balance based on transaction type
            if (isSmallCounterCredit) {
                runningBalance += tx.amount;
                totalCredits += tx.amount;
            } else {
                runningBalance -= tx.amount;
                totalDebits += tx.amount;
            }

            // Create statement entry with credit/debit columns
            return {
                id: tx.id,
                date: tx.date,
                description: tx.description || '',
                credit: isSmallCounterCredit ? tx.amount : null,
                debit: !isSmallCounterCredit ? tx.amount : null,
                runningBalance,
                createdBy: tx.createdBy
            };
        });

        // Calculate summary
        const summary: SmallCounterStatementSummary = {
            totalCredits,
            totalDebits,
            net: totalCredits - totalDebits
        };

        return {
            startDate,
            endDate,
            entries: statementEntries,
            openingBalance,
            closingBalance: runningBalance,
            summary,
            pagination: {
                total,
                pages: Math.ceil(total / pageSize),
                currentPage: page,
                pageSize
            }
        };
    }
}

export const smallCounterService = new SmallCounterService();