import { useState, useEffect, useRef } from 'react'
import { Card, Space, Button, App } from 'antd'
import { ArrowLeftOutlined } from '@ant-design/icons'
import { useTheme } from '@/renderer/contexts'
import {
  InvoiceForm,
  ItemsTable,
  Summary,
  ProductDetails,
  CustomerDetailsCard,
  DraftActions
} from './components'
import type { SaleInvoiceComponentProps, SaleInvoiceFormData } from '@/common/types'
import { useSelector } from 'react-redux'
import { IRootState } from '@/renderer/redux'
import { cancelInvoiceNumber, generateInvoiceNumber } from './utils/invoiceNumber'
import { submitInvoice } from './utils/invoiceSubmission'
import { fetchProductDetails } from './utils/productFetching'
import { ConfirmationModal } from './components/ConfirmationModal'

const CreateSaleInvoice = ({ onBack, onSuccess, type, createMode }: SaleInvoiceComponentProps) => {
  const [loading, setLoading] = useState(false)
  const [formData, setFormData] = useState<SaleInvoiceFormData>({
    createdById: '',
    date: new Date(),
    items: [],
    discountAmount: 0,
    paidAmount: 0,
    invoiceNumber: '',
    paymentMethod: 'CASH',
    bankId: '',
    source: 'SMALL_COUNTER'
  })
  const [selectedProductId, setSelectedProductId] = useState('')
  const [productDetails, setProductDetails] = useState<any>(null)
  const [generatingInvoiceNumber, setGeneratingInvoiceNumber] = useState(false)
  const [showConfirmModal, setShowConfirmModal] = useState(false)
  const invoiceToCancel = useRef<string>('')

  const { isDarkMode } = useTheme()
  const { message } = App.useApp()
  const user = useSelector((state: IRootState) => state.user.data)

  // Function to reset form state
  const resetFormState = () => {
    setFormData({
      createdById: user?.id || '',
      date: new Date(),
      items: [],
      discountAmount: 0,
      paidAmount: 0,
      invoiceNumber: '',
      paymentMethod: 'CASH',
      bankId: '',
      source: 'SMALL_COUNTER'
    })
    setSelectedProductId('')
    setProductDetails(null)
  }

  useEffect(() => {
    if (createMode === 'REGISTERED' || createMode === 'WALK_IN') {
      handleGenerateInvoiceNumber()
    }
  }, [createMode])

  // Cleanup effect
  useEffect(() => {
    return () => {
      console.log('CreateSaleInvoice component unmounted - resetting state')
      if (invoiceToCancel.current) {
        console.log('Cleanup: Canceling invoice number', invoiceToCancel.current)
        void cancelInvoiceNumber(invoiceToCancel.current)
      }
      resetFormState()
      setShowConfirmModal(false)
    }
  }, [])

  // Fetch product details
  useEffect(() => {
    const getProductDetails = async () => {
      if (selectedProductId) {
        setTimeout(async () => {
          const details = await fetchProductDetails(selectedProductId, message)
          setProductDetails(details)
        }, 400)
      } else {
        setProductDetails(null)
      }
    }
    getProductDetails()
  }, [selectedProductId])

  const handleGenerateInvoiceNumber = async () => {
    setGeneratingInvoiceNumber(true)
    const invoiceNumber = await generateInvoiceNumber(createMode, user?.id || '', message)
    setGeneratingInvoiceNumber(false)

    if (invoiceNumber) {
      setFormData((prev) => ({ ...prev, invoiceNumber }))
      invoiceToCancel.current = invoiceNumber
    }
  }

  const handleSubmit = async () => {
    setLoading(true)
    const success = await submitInvoice(
      formData,
      (newData) => {
        setFormData(newData)
        // Clear the invoice to cancel since it was used successfully
        invoiceToCancel.current = ''
      },
      type,
      user?.id || '',
      message
    )
    setLoading(false)

    if (success) {
      resetFormState() // Reset form state after successful submission
      onSuccess()
    }
  }

  return (
    <div className="h-full w-full space-y-6 p-6">
      <Space className="w-full justify-between">
        <Button icon={<ArrowLeftOutlined />} onClick={() => setShowConfirmModal(true)}>
          Back
        </Button>
        <DraftActions
          type={type}
          formData={formData}
          onRestore={(data) => {
            // we need to restore the form data with the current invoice number
            // in case the draft was created with a different invoice number
            // and that invoice number was already used, we need to restore the form data
            // with the current invoice number, always.

            // make a deep copy of the data object as a new object
            const newData = structuredClone(data)

            // replace the old invoice number with the current one fetched from the database
            newData.invoiceNumber = formData.invoiceNumber
            setFormData(newData)
          }}
          disabled={loading || generatingInvoiceNumber}
        />
      </Space>

      <Space direction="vertical" size="middle" className="w-full">
        <Card
          className={`shadow-sm ${
            isDarkMode
              ? 'bg-gradient-to-br from-indigo-950 via-black to-indigo-900 shadow-lg shadow-indigo-900'
              : 'bg-gradient-to-br from-indigo-200 via-white to-indigo-100 shadow-lg shadow-indigo-100'
          }`}
          style={{
            backgroundSize: '200% 200%',
            backgroundPosition: 'center'
          }}
        >
          <InvoiceForm
            formData={formData}
            onChange={(data) => setFormData({ ...formData, ...data })}
            type={type}
            loading={generatingInvoiceNumber}
          />
        </Card>

        {type === 'REGISTERED' && (
          <div className="flex w-full flex-col lg:flex-row lg:gap-4">
            <div className="w-full max-lg:mb-4 lg:w-1/2">
              <CustomerDetailsCard formData={formData} />
            </div>

            <div className="w-full lg:w-1/2">
              <ProductDetails
                selectedProductId={selectedProductId}
                productDetails={productDetails}
                items={formData.items}
              />
            </div>
          </div>
        )}

        {type === 'WALK_IN' && (
          <div className="w-full">
            <ProductDetails
              selectedProductId={selectedProductId}
              productDetails={productDetails}
              items={formData.items}
            />
          </div>
        )}

        <Card
          className={`shadow-sm ${
            isDarkMode
              ? 'bg-gradient-to-br from-indigo-950 via-black to-indigo-900 shadow-lg shadow-indigo-900'
              : 'bg-gradient-to-br from-indigo-200 via-white to-indigo-100 shadow-lg shadow-indigo-100'
          }`}
          style={{
            backgroundSize: '200% 200%',
            backgroundPosition: 'center'
          }}
        >
          <ItemsTable
            items={formData.items}
            onItemsChange={(items) => setFormData({ ...formData, items })}
            selectedProductId={selectedProductId}
            setSelectedProductId={setSelectedProductId}
            productDetails={productDetails}
          />
        </Card>

        <Card
          className={`shadow-sm ${
            isDarkMode
              ? 'bg-gradient-to-br from-indigo-950 via-black to-indigo-900 shadow-lg shadow-indigo-900'
              : 'bg-gradient-to-br from-indigo-200 via-white to-indigo-100 shadow-lg shadow-indigo-100'
          }`}
          style={{
            backgroundSize: '200% 200%',
            backgroundPosition: 'center'
          }}
        >
          <Summary
            items={formData.items}
            formData={formData}
            onChange={(data) => setFormData({ ...formData, ...data })}
            onSubmit={handleSubmit}
            loading={loading}
            type={type}
          />
        </Card>
      </Space>

      <ConfirmationModal
        open={showConfirmModal}
        onConfirm={() => {
          resetFormState() // Use the reset function here too
          onBack()

          setShowConfirmModal(false)
        }}
        onCancel={() => setShowConfirmModal(false)}
        invoiceNumber={formData.invoiceNumber}
      />
    </div>
  )
}

export default CreateSaleInvoice
