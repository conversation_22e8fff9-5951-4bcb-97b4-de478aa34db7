import {
  Table,
  Tag,
  Card,
  Space,
  DatePicker,
  Select,
  Form,
  Button,
  Modal,
  Input,
  App,
  InputNumber
} from 'antd'
import { useEffect, useState } from 'react'
import { useApi } from '@/renderer/hooks'
import {
  getAccountTransfers,
  voidAccountTransfer,
  AccountTransferStatus,
  AccountTransferSortOrder
} from '@/renderer/services/accountTransfer'
import { formatDate, formatCurrency } from '@/renderer/utils'
import { useTheme, usePartyContext } from '@/renderer/contexts'
import { useSelector } from 'react-redux'
import { IRootState } from '@/renderer/redux'
import { DeleteOutlined } from '@ant-design/icons'
import {
  GetAccountTransfersParams,
  AccountTransfersResponse,
  AccountTransferItem
} from '@/common/types/accountTransfer'

const { RangePicker } = DatePicker
const { TextArea } = Input

interface AccountTransferListProps {
  refreshTrigger: number
}

export const AccountTransferList = ({ refreshTrigger }: AccountTransferListProps) => {
  const { isDarkMode } = useTheme()
  const { vendors, customers, creditors } = usePartyContext()
  const [form] = Form.useForm()
  const [voidForm] = Form.useForm()
  const { message } = App.useApp()
  const user = useSelector((state: IRootState) => state.user.data)

  // Filter states
  const [dateRange, setDateRange] = useState<[Date, Date] | null>(null)
  const [fromPartyId, setFromPartyId] = useState<string | null>(null)
  const [toPartyId, setToPartyId] = useState<string | null>(null)
  const [accountId, setAccountId] = useState<string | null>(null)
  const [status, setStatus] = useState<AccountTransferStatus>(AccountTransferStatus.ACTIVE)
  const [sortOrder, setSortOrder] = useState<AccountTransferSortOrder>(
    AccountTransferSortOrder.OLDEST_FIRST
  )
  const [search, setSearch] = useState<string>('')
  const [amountRange, setAmountRange] = useState<{ fromAmount?: number; toAmount?: number }>({})

  // Modal states
  const [isVoidModalOpen, setIsVoidModalOpen] = useState(false)
  const [selectedTransferId, setSelectedTransferId] = useState<string | null>(null)
  const [voidLoading, setVoidLoading] = useState(false)

  // Pagination
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0
  })

  const {
    data: transfers,
    isLoading,
    request: fetchTransfers
  } = useApi<AccountTransfersResponse, [GetAccountTransfersParams]>(async (params) => {
    return await getAccountTransfers(params)
  })

  useEffect(() => {
    const params: GetAccountTransfersParams = {
      page: pagination.current,
      pageSize: pagination.pageSize,
      startDate: dateRange?.[0],
      endDate: dateRange?.[1],
      fromPartyId: fromPartyId || undefined,
      toPartyId: toPartyId || undefined,
      accountId: accountId || undefined,
      status,
      sortOrder,
      search: search || undefined,
      amountRange:
        amountRange.fromAmount || amountRange.toAmount
          ? {
              fromAmount: amountRange.fromAmount || 0,
              toAmount: amountRange.toAmount || Number.MAX_SAFE_INTEGER
            }
          : undefined
    }

    fetchTransfers(params)
  }, [
    dateRange,
    fromPartyId,
    toPartyId,
    accountId,
    status,
    sortOrder,
    search,
    amountRange,
    refreshTrigger,
    pagination.current,
    pagination.pageSize
  ])

  // Update total when data changes
  useEffect(() => {
    if (transfers) {
      setPagination((prev) => ({
        ...prev,
        total: transfers.total
      }))
    }
  }, [transfers])

  const handleVoidTransfer = async () => {
    if (!selectedTransferId || !user?.id) return

    try {
      setVoidLoading(true)
      const values = await voidForm.validateFields()

      await voidAccountTransfer({
        id: selectedTransferId,
        deletedById: user.id,
        deletionReason: values.reason
      })

      message.success('Account transfer voided successfully')
      setIsVoidModalOpen(false)
      voidForm.resetFields()
      setSelectedTransferId(null)

      // Refresh the list
      const params: GetAccountTransfersParams = {
        page: pagination.current,
        pageSize: pagination.pageSize,
        startDate: dateRange?.[0],
        endDate: dateRange?.[1],
        fromPartyId: fromPartyId || undefined,
        toPartyId: toPartyId || undefined,
        accountId: accountId || undefined,
        status,
        sortOrder,
        search: search || undefined,
        amountRange:
          amountRange.fromAmount || amountRange.toAmount
            ? {
                fromAmount: amountRange.fromAmount || 0,
                toAmount: amountRange.toAmount || Number.MAX_SAFE_INTEGER
              }
            : undefined
      }
      fetchTransfers(params)
    } catch (error: any) {
      message.error(error.message || 'Failed to void account transfer')
    } finally {
      setVoidLoading(false)
    }
  }

  const statusOptions = [
    { label: 'Active', value: AccountTransferStatus.ACTIVE },
    { label: 'Voided', value: AccountTransferStatus.VOIDED },
    { label: 'All', value: AccountTransferStatus.ALL }
  ]

  const sortOptions = [
    { label: 'Oldest First', value: AccountTransferSortOrder.OLDEST_FIRST },
    { label: 'Newest First', value: AccountTransferSortOrder.NEWEST_FIRST },
    { label: 'Amount High to Low', value: AccountTransferSortOrder.AMOUNT_HIGH_TO_LOW },
    { label: 'Amount Low to High', value: AccountTransferSortOrder.AMOUNT_LOW_TO_HIGH }
  ]

  // Combine all parties from context
  const allParties = [...vendors, ...customers, ...creditors]
  const partyOptions = allParties

  const columns = [
    {
      title: 'Date',
      dataIndex: 'transferDate',
      key: 'transferDate',
      render: (date: string) => formatDate(date)
    },
    {
      title: 'Amount',
      dataIndex: 'amount',
      key: 'amount',
      align: 'right' as const,
      render: (amount: number) => formatCurrency(amount)
    },
    {
      title: 'From Party',
      key: 'fromParty',
      render: (_: any, record: AccountTransferItem) => (
        <div>
          <div className="font-medium">{record.fromParty?.name}</div>
          <div className="text-xs text-gray-500">{record.fromParty?.type}</div>
        </div>
      )
    },
    {
      title: 'To Party',
      key: 'toParty',
      render: (_: any, record: AccountTransferItem) => (
        <div>
          <div className="font-medium">{record.toParty?.name}</div>
          <div className="text-xs text-gray-500">{record.toParty?.type}</div>
        </div>
      )
    },
    {
      title: 'Description',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true
    },
    {
      title: 'Status',
      key: 'status',
      render: (_: any, record: AccountTransferItem) => {
        const isVoided = record.isDeleted || record.ledger?.status === 'VOID'
        return <Tag color={isVoided ? 'red' : 'green'}>{isVoided ? 'VOIDED' : 'ACTIVE'}</Tag>
      }
    },
    {
      title: 'Created By',
      dataIndex: ['createdBy', 'name'],
      key: 'createdBy'
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_: any, record: AccountTransferItem) => {
        const isVoided = record.isDeleted || record.ledger?.status === 'VOID'
        return (
          <Space>
            {!isVoided && (
              <Button
                type="text"
                danger
                size="small"
                onClick={() => {
                  setSelectedTransferId(record.id)
                  setIsVoidModalOpen(true)
                }}
                icon={<DeleteOutlined />}
                title="Void Transfer"
              />
            )}
          </Space>
        )
      }
    }
  ]

  return (
    <Card
      className={`bg-[length:200%_200%] bg-[center] shadow-lg ${
        isDarkMode
          ? 'bg-gradient-to-bl from-indigo-950 via-black to-indigo-900 shadow-indigo-950'
          : 'bg-gradient-to-bl from-indigo-200 via-white to-indigo-100 shadow-indigo-200'
      }`}
    >
      <Form form={form} layout="vertical" className="mb-4">
        <Space wrap className="w-full">
          <Form.Item label="Date Range" className="!mb-0">
            <RangePicker
              onChange={(_, dateStrings) => {
                if (dateStrings[0] && dateStrings[1]) {
                  setDateRange([new Date(dateStrings[0]), new Date(dateStrings[1])])
                } else {
                  setDateRange(null)
                }
              }}
            />
          </Form.Item>

          <Form.Item label="Search" className="!mb-0">
            <Input
              placeholder="Search description..."
              value={search}
              onChange={(e) => setSearch(e.target.value)}
              style={{ width: 200 }}
              allowClear
            />
          </Form.Item>

          <Form.Item label="Status" className="!mb-0">
            <Select
              placeholder="Select status"
              options={statusOptions}
              value={status}
              onChange={setStatus}
              style={{ width: 150 }}
            />
          </Form.Item>

          <Form.Item label="Sort Order" className="!mb-0">
            <Select
              placeholder="Select sort order"
              options={sortOptions}
              value={sortOrder}
              onChange={setSortOrder}
              style={{ width: 180 }}
            />
          </Form.Item>
        </Space>

        <Space wrap className="mt-2 w-full">
          <Form.Item label="From Party" className="!mb-0">
            <Select
              allowClear
              placeholder="Select from party"
              options={partyOptions}
              value={fromPartyId}
              onChange={setFromPartyId}
              style={{ width: 200 }}
              showSearch
              filterOption={(input, option) =>
                String(option?.label ?? '')
                  .toLowerCase()
                  .includes(input.toLowerCase())
              }
            />
          </Form.Item>

          <Form.Item label="To Party" className="!mb-0">
            <Select
              allowClear
              placeholder="Select to party"
              options={partyOptions}
              value={toPartyId}
              onChange={setToPartyId}
              style={{ width: 200 }}
              showSearch
              filterOption={(input, option) =>
                String(option?.label ?? '')
                  .toLowerCase()
                  .includes(input.toLowerCase())
              }
            />
          </Form.Item>

          <Form.Item label="Any Party" className="!mb-0">
            <Select
              allowClear
              placeholder="Select any party"
              options={partyOptions}
              value={accountId}
              onChange={setAccountId}
              style={{ width: 200 }}
              showSearch
              filterOption={(input, option) =>
                String(option?.label ?? '')
                  .toLowerCase()
                  .includes(input.toLowerCase())
              }
            />
          </Form.Item>
        </Space>

        <Space wrap className="mt-2 w-full">
          <Form.Item label="Amount From" className="!mb-0">
            <InputNumber
              placeholder="Min amount"
              value={amountRange.fromAmount}
              onChange={(value) =>
                setAmountRange((prev) => ({ ...prev, fromAmount: value || undefined }))
              }
              style={{ width: 120 }}
              min={0}
            />
          </Form.Item>

          <Form.Item label="Amount To" className="!mb-0">
            <InputNumber
              placeholder="Max amount"
              value={amountRange.toAmount}
              onChange={(value) =>
                setAmountRange((prev) => ({ ...prev, toAmount: value || undefined }))
              }
              style={{ width: 120 }}
              min={0}
            />
          </Form.Item>
        </Space>
      </Form>

      <Table
        columns={columns}
        dataSource={transfers?.accountTransfers || []}
        loading={isLoading}
        rowKey="id"
        virtual
        sticky
        size="small"
        pagination={{
          ...pagination,
          onChange: (page, pageSize) =>
            setPagination((prev) => ({
              ...prev,
              current: page,
              pageSize: pageSize || prev.pageSize
            })),
          position: ['topRight'],
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`,
          showPrevNextJumpers: true
        }}
      />

      <Modal
        title="Void Account Transfer"
        open={isVoidModalOpen}
        onOk={handleVoidTransfer}
        onCancel={() => {
          setIsVoidModalOpen(false)
          setSelectedTransferId(null)
          voidForm.resetFields()
        }}
        confirmLoading={voidLoading}
        okText="Void"
        okButtonProps={{ danger: true }}
      >
        <div>
          <p className="mb-4 text-red-500">Warning: This action cannot be undone!</p>
          <Form form={voidForm} layout="vertical">
            <Form.Item
              name="reason"
              label="Reason"
              rules={[{ required: true, message: 'Please enter a reason for voiding' }]}
            >
              <TextArea rows={3} placeholder="Enter reason for voiding" />
            </Form.Item>
          </Form>
        </div>
      </Modal>
    </Card>
  )
}
