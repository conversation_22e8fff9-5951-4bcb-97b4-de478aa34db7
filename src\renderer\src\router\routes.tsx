import { App_Routes, Roles } from '@/common'
import { lazy } from 'react'

export const LazyPages = {
  AuthLayout: lazy(() =>
    import('@/renderer/layouts').then((module) => ({ default: module.AuthLayout }))
  ),
  AppLayout: lazy(() =>
    import('@/renderer/layouts').then((module) => ({ default: module.AppLayout }))
  ),
  Login: lazy(() => import('@/renderer/pages').then((module) => ({ default: module.Login }))),
  NotFound: lazy(() => import('@/renderer/pages').then((module) => ({ default: module.NotFound }))),
  Logout: lazy(() => import('@/renderer/pages').then((module) => ({ default: module.Logout }))),
  Dashboard: lazy(() =>
    import('@/renderer/pages').then((module) => ({ default: module.Dashboard }))
  ),
  User: lazy(() => import('@/renderer/pages').then((module) => ({ default: module.User }))),
  Staff: lazy(() => import('@/renderer/pages').then((module) => ({ default: module.Staff }))),
  Products: lazy(() => import('@/renderer/pages').then((module) => ({ default: module.Products }))),
  Expense: lazy(() => import('@/renderer/pages').then((module) => ({ default: module.Expense }))),
  Vendors: lazy(() => import('@/renderer/pages').then((module) => ({ default: module.Vendors }))),
  Customers: lazy(() =>
    import('@/renderer/pages').then((module) => ({ default: module.Customers }))
  ),
  Creditors: lazy(() =>
    import('@/renderer/pages').then((module) => ({ default: module.Creditors }))
  ),
  Ledger: lazy(() => import('@/renderer/pages').then((module) => ({ default: module.Ledger }))),
  Stock: lazy(() => import('@/renderer/pages').then((module) => ({ default: module.Stock }))),
  PurchaseInvoice: lazy(() =>
    import('@/renderer/pages').then((module) => ({ default: module.PurchaseInvoice }))
  ),
  SaleInvoice: lazy(() =>
    import('@/renderer/pages').then((module) => ({ default: module.SaleInvoice }))
  ),
  Banks: lazy(() => import('@/renderer/pages').then((module) => ({ default: module.Banks }))),
  CashVault: lazy(() =>
    import('@/renderer/pages').then((module) => ({ default: module.CashVault }))
  ),
  Payments: lazy(() => import('@/renderer/pages').then((module) => ({ default: module.Payments }))),
  License: lazy(() => import('@/renderer/pages').then((module) => ({ default: module.License }))),
  Reset: lazy(() => import('@/renderer/pages').then((module) => ({ default: module.Reset }))),
  SmallCounter: lazy(() =>
    import('@/renderer/pages').then((module) => ({ default: module.SmallCounter }))
  ),
  OpeningStock: lazy(() =>
    import('@/renderer/pages').then((module) => ({ default: module.OpeningStock }))
  ),
  StockReturn: lazy(() =>
    import('@/renderer/pages').then((module) => ({ default: module.StockReturn }))
  ),
  ReturnToVendor: lazy(() =>
    import('@/renderer/pages').then((module) => ({ default: module.ReturnToVendor }))
  ),
  Reports: lazy(() => import('@/renderer/pages').then((module) => ({ default: module.Reports }))),
  ManualEntry: lazy(() =>
    import('@/renderer/pages').then((module) => ({ default: module.ManualEntry }))
  )
}

type IAppRoutes = {
  path: `${App_Routes}`
  component: React.LazyExoticComponent<() => JSX.Element | undefined>
  // roles: `${Roles}`[];
}

export const AppRoutes: IAppRoutes[] = [
  {
    path: App_Routes.DASHBOARD,
    component: LazyPages.Dashboard
    // roles: [Roles.USER, Roles.ADMIN]
  },
  {
    path: App_Routes.USERS,
    component: LazyPages.User
    // roles: [Roles.ADMIN],
  },
  {
    path: App_Routes.STAFF,
    component: LazyPages.Staff
    // roles: [Roles.ADMIN, Roles.USER],
  },
  {
    path: App_Routes.PRODUCTS,
    component: LazyPages.Products
    // roles: [Roles.ADMIN, Roles.USER],
  },
  {
    path: App_Routes.EXPENSE,
    component: LazyPages.Expense
    // roles: [Roles.ADMIN, Roles.USER],
  },
  {
    path: App_Routes.VENDORS,
    component: LazyPages.Vendors
    // roles: [Roles.ADMIN, Roles.USER],
  },
  {
    path: App_Routes.CUSTOMERS,
    component: LazyPages.Customers
    // roles: [Roles.ADMIN, Roles.USER],
  },
  {
    path: App_Routes.CREDITORS,
    component: LazyPages.Creditors
    // roles: [Roles.ADMIN, Roles.USER],
  },
  {
    path: App_Routes.LEDGER,
    component: LazyPages.Ledger
    // roles: [Roles.ADMIN, Roles.USER],
  },
  {
    path: App_Routes.STOCK,
    component: LazyPages.Stock
    // roles: [Roles.ADMIN, Roles.USER],
  },

  {
    path: App_Routes.ANY,
    component: LazyPages.NotFound
    // roles: [Roles.ANY],
  },
  {
    path: App_Routes.PURCHASE_INVOICE,
    component: LazyPages.PurchaseInvoice
  },
  {
    path: App_Routes.SALE_INVOICE,
    component: LazyPages.SaleInvoice
  },
  {
    path: App_Routes.BANKS,
    component: LazyPages.Banks
  },
  {
    path: App_Routes.CASH_VAULT,
    component: LazyPages.CashVault
  },
  {
    path: App_Routes.PAYMENTS,
    component: LazyPages.Payments
  },
  {
    path: App_Routes.LICENSE,
    component: LazyPages.License
  },
  {
    path: App_Routes.RESET,
    component: LazyPages.Reset
  },
  {
    path: App_Routes.SMALL_COUNTER,
    component: LazyPages.SmallCounter
  },
  {
    path: App_Routes.OPENING_STOCK,
    component: LazyPages.OpeningStock
  },
  {
    path: App_Routes.STOCK_RETURN,
    component: LazyPages.StockReturn
  },
  {
    path: App_Routes.RETURN_TO_VENDOR,
    component: LazyPages.ReturnToVendor
  },
  {
    path: App_Routes.REPORTS,
    component: LazyPages.Reports
  },
  {
    path: App_Routes.MANUAL_ENTRY,
    component: LazyPages.ManualEntry
  }
]
