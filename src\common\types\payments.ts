import { CashLocation, PaymentMethod, Status } from '@prisma/client';

export type PaymentSource = 'SMALL_COUNTER' | 'CASH_VAULT' | 'BANK';
export type PaymentType = 'RECEIVED' | 'PAID';

export interface CreatePaymentData {
    amount: number;
    date: Date;
    description?: string;
    partyId: string;
    type: 'PAID' | 'RECEIVED';
    source: CashLocation;
    locationId?: string;      // Required for bank
    createdById: string;
    paymentMethod: PaymentMethod;
}

export interface TransferBase {
    amount: number;
    date: Date;
    description: string;
    createdById: string;
}

export interface LocationTransferData extends TransferBase {
    fromLocation: CashLocation;
    toLocation: CashLocation;
    fromLocationId?: string;  // Required if fromLocation is BANK
    toLocationId?: string;    // Required if toLocation is BANK
}

export interface PartyTransferData extends TransferBase {
    fromPartyId: string;
    toPartyId: string;
    paymentMethod: PaymentMethod;
}

export interface GetPaymentsParams {
    startDate?: Date;
    endDate?: Date;
    page?: number;
    limit?: number;
    type?: 'PAID' | 'RECEIVED';
    status?: 'ACTIVE' | 'VOID' | 'ALL';
    partyId?: string;
}

export interface PaymentDateRangeParams {
    startDate?: Date;
    endDate?: Date;
    page?: number;
    limit?: number;
    type?: 'PAID' | 'RECEIVED';
    status?: 'ACTIVE' | 'VOID';
}

export interface GetTransfersParams {
    page?: number;
    limit?: number;
    startDate?: Date;
    endDate?: Date;
    fromLocation?: CashLocation;
    toLocation?: CashLocation;
    status?: Status;
}

export interface VoidTransferParams {
    id: string;
    adminId: string;
    reason: string;
}

export interface TransferResponse {
    id: string;
    date: Date;
    amount: number;
    description: string;
    creditOrDebit: 'CREDIT' | 'DEBIT';
    status: Status;
    cashSource?: CashLocation;
    cashDestination?: CashLocation;
    bankId?: string;
    bank?: {
        name: string;
        accountNo: string;
    };
    createdBy: {
        name: string;
    };
    voidedBy?: {
        name: string;
    };
    // voidedAt?: Date;
    // voidingReason?: string;
}

export interface GetTransfersResponse {
    transfers: TransferResponse[];
    total: number;
    page: number;
    totalPages: number;
}