import { http } from "./http";
import { Channels } from "@/common/constants";
import { CreatePartyData, GetPartiesByTypeParams, GetPartiesParams, UpdatePartyParams } from "@/common/types";


export const createParty = async (params: CreatePartyData) => {
    return await http.post(Channels.CREATE_PARTY, { body: params });
};

export const getParty = async (id: string) => {
    return await http.get(Channels.GET_PARTY, { params: { id } });
};

export const updateParty = async (id: string, params: UpdatePartyParams) => {
    return await http.put(Channels.UPDATE_PARTY, {
        params: { id },
        body: params
    });
};

export const deleteParty = async (id: string) => {
    return await http.delete(Channels.DELETE_PARTY, { params: { id } });
};

export const getParties = async (params: GetPartiesParams) => {
    return await http.get(Channels.GET_PARTIES, { query: params });
};

export const getVendorsForSelect = async () => {
    return await http.get(Channels.GET_VENDORS_SELECT, { query: { type: 'VENDOR' } });
};

export const getCustomersForSelect = async () => {
    return await http.get(Channels.GET_CUSTOMERS_SELECT, { query: { type: 'CUSTOMER' } });
};

export const getCreditorsForSelect = async () => {
    return await http.get(Channels.GET_CREDITORS_SELECT, { query: { type: 'CREDITOR' } });
};

export const getAllPartiesForSelect = async () => {
    return await http.get(Channels.GET_ALL_PARTIES_SELECT);
};

export const getDebtors = async (params: GetPartiesParams) => {
    return await http.get(Channels.GET_DEBTORS, { query: params });
};

export const getCreditors = async (params: GetPartiesParams) => {
    return await http.get(Channels.GET_CREDITORS, { query: params });
};

export const getSettledParties = async (params: GetPartiesParams) => {
    return await http.get(Channels.GET_SETTLED, { query: params });
};

export const getPartiesByTypeForPDF = async (params: GetPartiesByTypeParams) => {
    return await http.get(Channels.GET_PARTIES_BY_TYPE_FOR_PDF, { query: params });
};

