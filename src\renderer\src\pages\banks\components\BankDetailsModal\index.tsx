// src/renderer/src/pages/banks/components/BankDetailsModal/index.tsx
import { useState } from 'react'
import { Modal, Tabs } from 'antd'
import { BankLedger } from './BankLedger'
import { TransactionSummary } from './TransactionSummary'
import { Reconciliation } from './Reconciliation'
import { SearchTransactions } from './SearchTransactions'
import { DailyBalances } from './DailyBalances'
import type { BankDetailsTabKey } from '@/common/types'

interface BankDetailsModalProps {
  bankId: string | null
  open: boolean
  onClose: () => void
}

export const BankDetailsModal = ({ bankId, open, onClose }: BankDetailsModalProps) => {
  const [activeTab, setActiveTab] = useState<BankDetailsTabKey['key']>('ledger')

  console.log(bankId)

  return (
    <Modal title="Bank Details" open={open} onCancel={onClose} width={1000} footer={null}>
      {bankId && (
        <Tabs
          activeKey={activeTab}
          onChange={(key) => setActiveTab(key as BankDetailsTabKey['key'])}
          items={[
            {
              key: 'ledger',
              label: 'Ledger Entries',
              children: <BankLedger bankId={bankId} />
            },
            {
              key: 'summary',
              label: 'Transaction Summary',
              children: <TransactionSummary bankId={bankId} />
            },
            {
              key: 'search',
              label: 'Search Transactions',
              children: <SearchTransactions bankId={bankId} />
            },
            {
              key: 'daily-balances',
              label: 'Daily Balances',
              children: <DailyBalances bankId={bankId} />
            },
            {
              key: 'reconciliation',
              label: 'Reconciliation',
              children: <Reconciliation bankId={bankId} />
            }
          ]}
        />
      )}
    </Modal>
  )
}
