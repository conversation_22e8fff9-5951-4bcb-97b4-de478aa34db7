/*
  Warnings:

  - The values [CASH_COUNTER] on the enum `CashLocation` will be removed. If these variants are still used in the database, this will fail.
  - The values [BankDeposit,BankWithdrawal,VaultDeposit,VaultWithdrawal] on the enum `LedgerType` will be removed. If these variants are still used in the database, this will fail.

*/
-- AlterEnum
BEGIN;
CREATE TYPE "CashLocation_new" AS ENUM ('BANK', 'CASH_VAULT', 'CASH_ACCOUNT');
ALTER TABLE "Ledger" ALTER COLUMN "cashSource" TYPE "CashLocation_new" USING ("cashSource"::text::"CashLocation_new");
ALTER TABLE "Ledger" ALTER COLUMN "cashDestination" TYPE "CashLocation_new" USING ("cashDestination"::text::"CashLocation_new");
ALTER TABLE "Payments" ALTER COLUMN "sourceLocation" TYPE "CashLocation_new" USING ("sourceLocation"::text::"CashLocation_new");
ALTER TABLE "Payments" ALTER COLUMN "destinationLocation" TYPE "CashLocation_new" USING ("destinationLocation"::text::"CashLocation_new");
ALTER TYPE "CashLocation" RENAME TO "CashLocation_old";
ALTER TYPE "CashLocation_new" RENAME TO "CashLocation";
DROP TYPE "CashLocation_old";
COMMIT;

-- AlterEnum
BEGIN;
CREATE TYPE "LedgerType_new" AS ENUM ('PurchaseInvoice', 'SaleInvoice', 'Payment', 'OpeningBalance', 'Expense', 'Bank', 'Vault', 'CashAccount', 'Transfer');
ALTER TABLE "Ledger" ALTER COLUMN "referenceType" TYPE "LedgerType_new" USING ("referenceType"::text::"LedgerType_new");
ALTER TYPE "LedgerType" RENAME TO "LedgerType_old";
ALTER TYPE "LedgerType_new" RENAME TO "LedgerType";
DROP TYPE "LedgerType_old";
COMMIT;
