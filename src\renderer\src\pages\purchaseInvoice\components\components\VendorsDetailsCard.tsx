import { useState, useEffect } from 'react'
import { Tag, Space, Card, Typography, Statistic, Tooltip, message, theme } from 'antd'
import {
  UserOutlined,
  ShopOutlined,
  DollarCircleOutlined,
  ContactsOutlined,
  PhoneOutlined,
  HomeOutlined
} from '@ant-design/icons'

import { partyApi } from '@/renderer/services'
import { useTheme } from '@/renderer/contexts/ThemeContext'

const { Title, Text } = Typography

export const VendorDetailsCard = ({
  selectedVendor,
  vendorDetails,
  setVendorDetails
}: {
  selectedVendor: string | null
  vendorDetails: any
  setVendorDetails: any
}) => {
  const [isVisible, setIsVisible] = useState(false) // New state for controlling visibility
  const { token } = theme.useToken()

  const { isDarkMode } = useTheme()

  // animation for vendor details
  useEffect(() => {
    if (selectedVendor) {
      // First hide the current vendor details
      setIsVisible(false)

      // Wait for the hide transition to complete
      setTimeout(() => {
        // Then fetch new vendor details
        partyApi
          .getParty(selectedVendor)
          .then((response) => {
            setVendorDetails(response.data.data)
            // Show the new vendor details
            setIsVisible(true)
            console.log('vendor details', response.data.data)
          })
          .catch(() => {
            message.error('Failed to fetch vendor details')
          })
      }, 500) // This should match your CSS transition duration
    } else {
      setIsVisible(false)
      setTimeout(() => {
        setVendorDetails(null)
      }, 500)
    }
  }, [selectedVendor])

  return (
    <Card
      className={`animated-section border-l-2 border-r-0 border-t-0 border-blue-400 ${
        isDarkMode
          ? 'bg-gradient-to-br from-blue-950 via-slate-950 to-blue-900 shadow-lg shadow-blue-900'
          : 'bg-gradient-to-br from-sky-300 via-white to-blue-200 shadow-lg shadow-blue-100'
      } ${isVisible ? 'visible' : ''}`}
      style={{
        backgroundSize: '200% 200%',
        backgroundPosition: 'center'
      }}
      title={
        <Space className="w-full justify-between">
          <Space>
            <UserOutlined className="text-lg text-blue-500" />
            <Title level={4} className="!mb-0">
              {vendorDetails?.name || 'N/A'}
            </Title>
          </Space>
          <Tag color={'purple'} icon={<ShopOutlined />}>
            {vendorDetails?.type || 'N/A'}
          </Tag>
        </Space>
      }
    >
      <div className="flex flex-row space-y-4">
        <Statistic
          className="flex-1"
          title={
            <Space>
              <DollarCircleOutlined className="text-green-500" />
              <Text type="secondary">Current Balance</Text>
            </Space>
          }
          value={vendorDetails?.currentBalance || 0}
          precision={2}
          prefix="$"
          valueStyle={{
            color: (vendorDetails?.currentBalance || 0) < 0 ? '#ff4d4f' : '#52c41a'
          }}
        />

        <Space size="large" className="flex-[2] justify-between text-lg">
          <Tooltip title="Contact Person">
            <Space>
              <ContactsOutlined className="text-blue-500" />
              <Text>{vendorDetails?.contact || 'N/A'}</Text>
            </Space>
          </Tooltip>

          <Tooltip title="Phone Number">
            <Space>
              <PhoneOutlined className="text-green-500" />
              <Text>{vendorDetails?.phoneNumber || 'N/A'}</Text>
            </Space>
          </Tooltip>

          <Tooltip title="Address">
            <Space>
              <HomeOutlined className="text-orange-500" />
              <Text className="truncate">{vendorDetails?.address || 'N/A'}</Text>
            </Space>
          </Tooltip>
        </Space>
      </div>
    </Card>
  )
}
