import jsPDF from 'jspdf'
import 'jspdf-autotable'
import dayjs from 'dayjs'

interface LedgerEntry {
    id: string
    date: Date
    description: string
    creditOrDebit: 'CREDIT' | 'DEBIT'
    amount: number
    referenceType: string
    serialNumber: number | string
    balance: number
}

interface Party {
    name: string
    type: string
    contact?: string
    address?: string
    phoneNumber?: string
    currentBalance: number
}

interface GenerateLedgerPDFParams {
    entries: LedgerEntry[]
    party: Party
    totals: {
        credits: number
        debits: number
        net: number
    }
}

export const generateLedgerPDF = async ({ entries, party, totals }: GenerateLedgerPDFParams, shouldSave: boolean = false): Promise<jsPDF | void> => {
    try {
        const doc = new jsPDF()
        const pageHeight = doc.internal.pageSize.height

        // Function to add header to each page
        const addHeader = (pageNumber: number) => {
            doc.setFontSize(16)
            doc.setFont('helvetica', 'bold')
            doc.text('SJ LACE', doc.internal.pageSize.width / 2, 15, { align: 'center' })

            doc.setFontSize(12)
            doc.text('Ledger Statement', doc.internal.pageSize.width / 2, 22, { align: 'center' })

            // Party details
            doc.setFontSize(10)
            doc.text(`Party: ${party.name}`, 15, 32)
            doc.text(`Type: ${party.type}`, 15, 38)
            if (party.contact) doc.text(`Contact: ${party.contact}`, 15, 44)

            // Add page number
            doc.setFontSize(8)
            doc.text(`Page ${pageNumber}`, doc.internal.pageSize.width - 20, 10)

            return 50 // Return starting Y position for table
        }

        // Add first page header
        let startY = addHeader(1)

        // Configure the table
        const tableColumns = [
            { header: 'Sr.', dataKey: 'serialNumber' },
            { header: 'Date', dataKey: 'date' },
            { header: 'Description', dataKey: 'description' },
            { header: 'Credit', dataKey: 'credit', styles: { textColor: [82, 196, 26], fontStyle: 'bold' } },
            { header: 'Debit', dataKey: 'debit', styles: { textColor: [245, 34, 45], fontStyle: 'bold' } },
            { header: 'Balance', dataKey: 'balance' }
        ]

        const tableRows = entries.map(entry => ({
            serialNumber: entry.serialNumber,
            date: dayjs(entry.date).format('DD/MM/YYYY HH:mm'),
            description: entry.description,
            credit: entry.creditOrDebit === 'CREDIT' ? entry.amount.toLocaleString('en-US', { minimumFractionDigits: 0, maximumFractionDigits: 2 }) : '',
            debit: entry.creditOrDebit === 'DEBIT' ? entry.amount.toLocaleString('en-US', { minimumFractionDigits: 0, maximumFractionDigits: 2 }) : '',
            balance: entry.balance.toLocaleString('en-US', { minimumFractionDigits: 0, maximumFractionDigits: 2 })
        }))

        // Add summary row
        tableRows.push({
            serialNumber: '',
            date: '',
            description: 'Total',
            credit: totals.credits.toLocaleString('en-US', { minimumFractionDigits: 0, maximumFractionDigits: 2 }),
            debit: totals.debits.toLocaleString('en-US', { minimumFractionDigits: 0, maximumFractionDigits: 2 }),
            balance: totals.net.toLocaleString('en-US', { minimumFractionDigits: 0, maximumFractionDigits: 2 })
        })

        // @ts-ignore (jspdf-autotable types are not properly recognized)
        doc.autoTable({
            columns: tableColumns,
            body: tableRows,
            startY,
            margin: { top: 50, right: 15, bottom: 20, left: 15 },
            styles: {
                fontSize: 8,
                cellPadding: 2,
                lineColor: [0, 0, 0],
                lineWidth: 0.1,
                fontStyle: 'bold'
            },
            headStyles: {
                fillColor: [51, 51, 51],
                textColor: [255, 255, 255],
                fontStyle: 'bold'
            },
            columnStyles: {
                serialNumber: { cellWidth: 15 },
                date: { cellWidth: 35 },
                description: { cellWidth: 50 },
                credit: { cellWidth: 25, halign: 'right' },
                debit: { cellWidth: 25, halign: 'right' },
                balance: { cellWidth: 25, halign: 'right' }
            },
            didDrawPage: function (data) {
                // Add header to each new page
                if (data.pageNumber > 1) {
                    addHeader(data.pageNumber)
                }
            },
            didParseCell: function (data) {
                // Only style non-header cells
                if (data.section === 'body' || data.section === 'foot') {
                    data.cell.styles.fontStyle = 'bold'
                    if (data.column.dataKey === 'credit' && data.cell.raw) {
                        data.cell.styles.textColor = [82, 196, 26]
                    }
                    if (data.column.dataKey === 'debit' && data.cell.raw) {
                        data.cell.styles.textColor = [245, 34, 45]
                    }
                    if (data.column.dataKey === 'balance' && data.cell.raw) {
                        const balanceValue = parseFloat(data.cell.raw.replace(/,/g, ''))
                        data.cell.styles.textColor = balanceValue >= 0 ? [82, 196, 26] : [245, 34, 45]
                    }
                }
            }
        })

        // Add footer
        doc.setFontSize(8)
        doc.setFont('helvetica', 'bold')
        doc.text(
            'This is a computer generated statement',
            doc.internal.pageSize.width / 2,
            pageHeight - 10,
            { align: 'center' }
        )

        if (shouldSave) {
            // Show save dialog
            const path = await window.electron.ipcRenderer.invoke('show-save-dialog', {
                title: 'Save Ledger PDF',
                defaultPath: `${party.name}_ledger.pdf`,
                filters: [{ name: 'PDF Files', extensions: ['pdf'] }]
            })

            if (!path) {
                return
            }

            // Save the PDF
            await window.electron.ipcRenderer.invoke('save-pdf', {
                path,
                data: doc.output('arraybuffer')
            })
        }

        return doc
    } catch (error) {
        console.error('Error generating PDF:', error)
        throw error
    }
}
