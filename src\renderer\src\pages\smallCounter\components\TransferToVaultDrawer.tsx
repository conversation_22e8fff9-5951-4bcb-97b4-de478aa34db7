// import { <PERSON><PERSON>, <PERSON>er, <PERSON>, InputNumber, Space } from 'antd'
// import { FaMoneyBillTransfer } from 'react-icons/fa6'

// interface TransferToVaultDrawerProps {
//   open: boolean
//   onClose: () => void
//   onTransfer: (amount: number) => Promise<void>
//   loading: boolean
//   currentBalance: number
// }

// export const TransferToVaultDrawer = ({
//   open,
//   onClose,
//   onTransfer,
//   loading,
//   currentBalance
// }: TransferToVaultDrawerProps) => {
//   const [form] = Form.useForm()

//   const handleTransfer = async (values: { amount: number }) => {
//     await onTransfer(values.amount)
//     form.resetFields()
//     onClose()
//   }

//   return (
//     <Drawer
//       title="Transfer to Vault"
//       placement="right"
//       onClose={onClose}
//       open={open}
//       width={400}
//       extra={
//         <Space>
//           <Button onClick={onClose}>Cancel</Button>
//           <Button
//             type="primary"
//             icon={<FaMoneyBillTransfer />}
//             loading={loading}
//             onClick={() => form.submit()}
//           >
//             Transfer
//           </Button>
//         </Space>
//       }
//     >
//       <Form form={form} layout="vertical" onFinish={handleTransfer}>
//         <Form.Item
//           label="Amount"
//           name="amount"
//           rules={[
//             { required: true, message: 'Please enter amount' },
//             {
//               type: 'number',
//               min: 0.01,
//               message: 'Amount must be greater than 0'
//             },
//             {
//               type: 'number',
//               max: currentBalance,
//               message: 'Amount cannot exceed current balance'
//             }
//           ]}
//         >
//           <InputNumber
//             style={{ width: '100%' }}
//             prefix="Rs. "
//             precision={2}
//             placeholder="Enter amount to transfer"
//           />
//         </Form.Item>
//       </Form>
//     </Drawer>
//   )
// }
