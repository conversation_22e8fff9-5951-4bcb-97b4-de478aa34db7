import { Card } from 'antd'
import { useTheme } from '@/renderer/contexts'
import { DailySalesOverview, SalesTimeline } from './components/sales'
import { TopProducts, LowStockProducts } from './components/products'
import { CashFlowOverview, StockValueOverview } from './components/financial'
import { PartyOverview } from './components/party'
import { SalesDistribution, PaymentDistribution } from './components/distribution'

const Dashboard = () => {
  const { isDarkMode } = useTheme()

  return (
    <div className="m-6 grid grid-cols-1 gap-6 lg:grid-cols-2 xl:grid-cols-3">
      {/* Sales Section */}
      <Card
        className={`col-span-1 bg-[length:200%_200%] bg-[center] shadow-lg xl:col-span-2 ${
          isDarkMode
            ? 'bg-gradient-to-bl from-indigo-950 via-black to-indigo-900 shadow-indigo-950'
            : 'bg-gradient-to-bl from-indigo-200 via-white to-indigo-100 shadow-indigo-200'
        }`}
      >
        <div className="grid gap-6 md:grid-cols-2">
          <DailySalesOverview />
          <SalesTimeline />
        </div>
      </Card>

      {/* Party Overview */}
      <Card
        className={`col-span-1 bg-[length:200%_200%] bg-[center] shadow-lg ${
          isDarkMode
            ? 'bg-gradient-to-bl from-indigo-950 via-black to-indigo-900 shadow-indigo-950'
            : 'bg-gradient-to-bl from-indigo-200 via-white to-indigo-100 shadow-indigo-200'
        }`}
      >
        <PartyOverview />
      </Card>

      {/* Products Section */}
      <Card
        className={`col-span-1 bg-[length:200%_200%] bg-[center] shadow-lg xl:col-span-2 ${
          isDarkMode
            ? 'bg-gradient-to-bl from-indigo-950 via-black to-indigo-900 shadow-indigo-950'
            : 'bg-gradient-to-bl from-indigo-200 via-white to-indigo-100 shadow-indigo-200'
        }`}
        bodyStyle={{ height: '100%', display: 'flex', flexDirection: 'column' }}
      >
        <div className="flex h-full flex-col gap-6 overflow-hidden">
          <div className="flex-1">
            <TopProducts />
          </div>
          <div className="flex-1">
            <LowStockProducts />
          </div>
        </div>
      </Card>

      {/* Financial Section */}
      <Card
        className={`col-span-1 bg-[length:200%_200%] bg-[center] shadow-lg ${
          isDarkMode
            ? 'bg-gradient-to-bl from-indigo-950 via-black to-indigo-900 shadow-indigo-950'
            : 'bg-gradient-to-bl from-indigo-200 via-white to-indigo-100 shadow-indigo-200'
        }`}
      >
        <div className="grid gap-6">
          <CashFlowOverview />
          <StockValueOverview />
        </div>
      </Card>

      {/* Distribution Section */}
      <Card
        className={`col-span-1 bg-[length:200%_200%] bg-[center] shadow-lg xl:col-span-2 ${
          isDarkMode
            ? 'bg-gradient-to-bl from-indigo-950 via-black to-indigo-900 shadow-indigo-950'
            : 'bg-gradient-to-bl from-indigo-200 via-white to-indigo-100 shadow-indigo-200'
        }`}
      >
        <div className="grid gap-6 md:grid-cols-2">
          <SalesDistribution />
          <PaymentDistribution />
        </div>
      </Card>
    </div>
  )
}

export default Dashboard
