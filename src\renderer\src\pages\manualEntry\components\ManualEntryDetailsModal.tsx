import { Modal, Descriptions, Tag, Typography } from 'antd'
import type { ManualEntryItem } from '@/common/types/manualEntry'
import dayjs from 'dayjs'

const { Text } = Typography

interface ManualEntryDetailsModalProps {
  entry: ManualEntryItem | null
  open: boolean
  onClose: () => void
}

export const ManualEntryDetailsModal = ({ entry, open, onClose }: ManualEntryDetailsModalProps) => {
  if (!entry) return null

  const getTargetInfo = () => {
    if (entry.party) {
      return {
        type: 'Party',
        name: entry.party.name,
        details: `Type: ${entry.party.type}`
      }
    }

    if (entry.bank) {
      return {
        type: 'Bank',
        name: entry.bank.name,
        details: `Account: ${entry.bank.accountNo}`
      }
    }

    if (entry.targetType) {
      return {
        type: 'Cash Location',
        name: entry.targetType === 'CASH_VAULT' ? 'Cash Vault' : 'Small Counter',
        details: ''
      }
    }

    return { type: 'Unknown', name: '-', details: '' }
  }

  const targetInfo = getTargetInfo()

  const getStatusTag = () => {
    if (entry.isDeleted) {
      return <Tag color="red">Deleted</Tag>
    }
    if (entry.ledger.status === 'VOID') {
      return <Tag color="orange">Voided</Tag>
    }
    return <Tag color="green">Active</Tag>
  }

  return (
    <Modal title="Manual Entry Details" open={open} onCancel={onClose} footer={null} width={700}>
      <Descriptions bordered column={2} size="small">
        <Descriptions.Item label="Entry ID" span={2}>
          <Text code>{entry.id}</Text>
        </Descriptions.Item>

        <Descriptions.Item label="Amount">
          <span
            className={
              entry.entryType === 'DEBIT'
                ? 'font-semibold text-red-600'
                : 'font-semibold text-green-600'
            }
          >
            {entry.entryType === 'DEBIT' ? '-' : '+'}
            {entry.amount.toLocaleString()}
          </span>
        </Descriptions.Item>

        <Descriptions.Item label="Entry Type">
          <Tag color={entry.entryType === 'DEBIT' ? 'red' : 'green'}>{entry.entryType}</Tag>
        </Descriptions.Item>

        <Descriptions.Item label="Transaction Date">
          {dayjs(entry.transactionDate).format('DD/MM/YYYY HH:mm')}
        </Descriptions.Item>

        <Descriptions.Item label="Status">{getStatusTag()}</Descriptions.Item>

        <Descriptions.Item label="Target Type">{targetInfo.type}</Descriptions.Item>

        <Descriptions.Item label="Target Name">{targetInfo.name}</Descriptions.Item>

        {targetInfo.details && (
          <Descriptions.Item label="Target Details" span={2}>
            {targetInfo.details}
          </Descriptions.Item>
        )}

        <Descriptions.Item label="Description" span={2}>
          {entry.description || <Text type="secondary">No description</Text>}
        </Descriptions.Item>

        <Descriptions.Item label="Created By">{entry.createdBy.name}</Descriptions.Item>

        <Descriptions.Item label="Created At">
          {dayjs(entry.createdAt).format('DD/MM/YYYY HH:mm:ss')}
        </Descriptions.Item>

        <Descriptions.Item label="Ledger ID">
          <Text code>{entry.ledgerId}</Text>
        </Descriptions.Item>

        <Descriptions.Item label="Ledger Status">
          <Tag color={entry.ledger.status === 'ACTIVE' ? 'green' : 'red'}>
            {entry.ledger.status}
          </Tag>
        </Descriptions.Item>

        {entry.ledger.cashSource && (
          <Descriptions.Item label="Cash Source">{entry.ledger.cashSource}</Descriptions.Item>
        )}

        {entry.ledger.cashDestination && (
          <Descriptions.Item label="Cash Destination">
            {entry.ledger.cashDestination}
          </Descriptions.Item>
        )}

        {entry.isDeleted && (
          <>
            <Descriptions.Item label="Deleted By">
              {entry.deletedBy?.name || 'Unknown'}
            </Descriptions.Item>

            <Descriptions.Item label="Deleted At">
              {entry.deletedAt ? dayjs(entry.deletedAt).format('DD/MM/YYYY HH:mm:ss') : '-'}
            </Descriptions.Item>

            <Descriptions.Item label="Deletion Reason" span={2}>
              {entry.deletionReason || <Text type="secondary">No reason provided</Text>}
            </Descriptions.Item>
          </>
        )}
      </Descriptions>
    </Modal>
  )
}
