import { Space, Select, DatePicker, Radio } from 'antd'
import { usePartyContext } from '@/renderer/contexts'
import dayjs from 'dayjs'

const { RangePicker } = DatePicker

interface FilterSectionProps {
  selectedVendor: string | null
  setSelectedVendor: (value: string | null) => void
  selectedDate: dayjs.Dayjs | null
  setSelectedDate: (date: dayjs.Dayjs | null) => void
  dateRange: [dayjs.Dayjs | null, dayjs.Dayjs | null] | null
  setDateRange: (range: [dayjs.Dayjs | null, dayjs.Dayjs | null] | null) => void
  selectedStatus: 'ALL' | 'ACTIVE' | 'VOID'
  setSelectedStatus: (status: 'ALL' | 'ACTIVE' | 'VOID') => void
}

export const FilterSection = ({
  selectedVendor,
  setSelectedVendor,
  selectedDate,
  setSelectedDate,
  dateRange,
  setDateRange,
  selectedStatus,
  setSelectedStatus
}: FilterSectionProps) => {
  const { vendors } = usePartyContext()

  return (
    <Space wrap>
      <Select
        allowClear
        showSearch
        // style={{ width: 200 }}
        className="w-96"
        placeholder="Select Vendor"
        onChange={setSelectedVendor}
        options={vendors}
        filterOption={(input, option) =>
          (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
        }
      />

      {/* hiding the date picker in the front end as its functionality is already fulfilled by the range picker by selecting the same day in start and end date */}
      <DatePicker
        className="hidden"
        onChange={(date) => {
          setSelectedDate(date)
          setDateRange(null)
        }}
        value={selectedDate}
      />
      <RangePicker
        onChange={(dates) => {
          setDateRange(dates)
          setSelectedDate(null)
        }}
        value={dateRange}
      />
      <Radio.Group value={selectedStatus} onChange={(e) => setSelectedStatus(e.target.value)}>
        <Radio.Button value="ACTIVE">Active</Radio.Button>
        <Radio.Button value="VOID">Void</Radio.Button>
        <Radio.Button value="ALL">All</Radio.Button>
      </Radio.Group>
    </Space>
  )
}
