import { useState } from 'react'
import { Card, Space, Button, Tabs } from 'antd'
import { PlusOutlined } from '@ant-design/icons'
import { CreateSaleInvoice, RegisteredSaleList, WalkInSaleList } from './components'
import { TransitionWrapper } from '@/renderer/components'
import './SaleInvoice.scss'
import { useTheme } from '@/renderer/contexts'

const SaleInvoice = () => {
  const [createMode, setCreateMode] = useState<'none' | 'REGISTERED' | 'WALK_IN'>('none')
  const [refreshTrigger, setRefreshTrigger] = useState(0)
  const [activeTab, setActiveTab] = useState<'registered' | 'walkIn'>('registered')

  const { isDarkMode } = useTheme()

  return (
    <div className="relative h-[calc(100vh-var(--header-height))] overflow-hidden">
      <TransitionWrapper isVisible={createMode !== 'none'} direction={'right'}>
        <CreateSaleInvoice
          type={createMode === 'REGISTERED' ? 'REGISTERED' : 'WALK_IN'}
          onBack={() => setCreateMode('none')}
          createMode={createMode}
          onSuccess={() => {
            setCreateMode('none')
            setRefreshTrigger((prev) => prev + 1)
          }}
        />
      </TransitionWrapper>

      <TransitionWrapper isVisible={createMode === 'none'} direction={'left'}>
        <Card
          className={`sale-invoice-container m-6 bg-[length:200%_200%] bg-[center] shadow-lg ${
            isDarkMode
              ? 'bg-gradient-to-bl from-indigo-950 via-black to-indigo-900 shadow-indigo-950'
              : 'bg-gradient-to-bl from-indigo-200 via-white to-indigo-100 shadow-indigo-200'
          }`}
        >
          <Space direction="vertical" size="middle" className="w-full">
            <Space className="w-full justify-end">
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => setCreateMode('WALK_IN')}
                className="!border-green-500 !bg-green-500 hover:!border-green-600 hover:!bg-green-600"
              >
                Walk-In Sale
              </Button>

              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => setCreateMode('REGISTERED')}
                className="!border-green-500 !bg-green-500 hover:!border-green-600 hover:!bg-green-600"
              >
                Registered Sale
              </Button>
            </Space>

            <Tabs
              activeKey={activeTab}
              onChange={(key) => setActiveTab(key as 'registered' | 'walkIn')}
              items={[
                {
                  key: 'registered',
                  label: 'Registered Sales',
                  children: <RegisteredSaleList refreshTrigger={refreshTrigger} />
                },
                {
                  key: 'walkIn',
                  label: 'Walk-In Sales',
                  children: <WalkInSaleList refreshTrigger={refreshTrigger} />
                }
              ]}
            />
          </Space>
        </Card>
      </TransitionWrapper>
    </div>
  )
}

export default SaleInvoice
