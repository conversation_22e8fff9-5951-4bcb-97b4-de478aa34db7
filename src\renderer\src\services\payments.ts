import { CreatePaymentData, PaymentDateRangeParams, GetPaymentsParams, LocationTransferData, PartyTransferData, GetTransfersParams, VoidTransferParams } from '@/common/types';
import { http } from './http';
import { Channels } from '@/common/constants';




export const createPayment = async (data: CreatePaymentData) => {
    return await http.post(Channels.CREATE_PAYMENT, {
        body: {
            ...data,
            date: data.date.toISOString()
        }
    });
};

export const getPaymentById = async (id: string) => {
    return await http.get(Channels.GET_PAYMENT_BY_ID, {
        params: { id }
    });
};

export const getPayments = async ({ startDate, endDate, page, limit, type, status, partyId }: GetPaymentsParams) => {
    return await http.get(Channels.GET_PAYMENTS, {
        query: {
            ...(startDate && { startDate: startDate.toISOString() }),
            ...(endDate && { endDate: endDate.toISOString() }),
            page,
            limit,
            type,
            status: status === 'ALL' ? undefined : status,
            partyId
        }
    })
};


// TODO: remove this get payments by date range function it is useless and wont be used
export const getPaymentsByDateRange = async ({ startDate, endDate, page, limit }: PaymentDateRangeParams) => {
    return await http.get(Channels.GET_PAYMENTS_BY_DATE_RANGE, {
        query: {
            ...(startDate && { startDate: startDate.toISOString() }),
            ...(endDate && { endDate: endDate.toISOString() }),
            page,
            limit
        }
    });
};

export const voidPayment = async (id: string, adminId: string, reason: string) => {
    return await http.post(Channels.VOID_PAYMENT, {
        body: { id, adminId, reason }
    });
};

export const transferBetweenLocations = async (data: LocationTransferData) => {
    return await http.post(Channels.TRANSFER_BETWEEN_LOCATIONS, {
        body: data,
    });
};

export const transferBetweenParties = async (data: PartyTransferData) => {
    return await http.post(Channels.TRANSFER_BETWEEN_PARTIES, {
        body: data,
    });
};

export const getTransfers = async (params: GetTransfersParams) => {
    return await http.get(Channels.GET_TRANSFERS, {
        query: {
            ...params,
            startDate: params.startDate?.toISOString(),
            endDate: params.endDate?.toISOString()
        }
    });
};

export const voidTransfer = async (data: VoidTransferParams) => {
    return await http.post(Channels.VOID_TRANSFER, {
        body: data
    });
};