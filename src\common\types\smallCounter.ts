// Small Counter Types
export interface SmallCounterTransactionFilters {
    page?: number;
    pageSize?: number;
    startDate?: Date;
    endDate?: Date;
    includeDeleted?: boolean;
}

export interface SmallCounterTransaction {
    id: string;
    date: Date;
    amount: number;
    description: string;
    creditOrDebit: 'CREDIT' | 'DEBIT';
    referenceType: string;
    status: 'ACTIVE' | 'VOID';
    createdBy: {
        name: string;
    };
}

export interface Pagination {
    total: number;
    pages: number;
    currentPage: number;
    pageSize: number;
}

export interface SmallCounterTransactionsResponse {
    transactions: SmallCounterTransaction[];
    pagination: Pagination;
}

export interface SmallCounterReconcileResponse {
    isReconciled: boolean;
    currentBalance: number;
    calculatedBalance: number;
    difference: number;
}

export interface InitializeSmallCounterData {
    amount: number;
    adminId: string;
}

export interface GetSmallCounterStatementParams {
    startDate: Date;
    endDate: Date;
    page?: number;
    pageSize?: number;
}

export interface SmallCounterStatementEntry {
    id: string;
    date: Date;
    description: string;
    credit: number | null;
    debit: number | null;
    runningBalance: number;
    createdBy: {
        name: string;
    };
}

export interface SmallCounterStatementSummary {
    totalCredits: number;
    totalDebits: number;
    net: number;
}

export interface SmallCounterStatement {
    startDate: Date;
    endDate: Date;
    entries: SmallCounterStatementEntry[];
    openingBalance: number;
    closingBalance: number;
    summary: SmallCounterStatementSummary;
    pagination: {
        total: number;
        pages: number;
        currentPage: number;
        pageSize: number;
    };
}