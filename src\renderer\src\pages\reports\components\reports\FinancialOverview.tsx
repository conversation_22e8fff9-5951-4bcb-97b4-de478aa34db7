import { Card, Row, Col, Skeleton, Alert, Statistic } from 'antd'
import {
  BarChart,
  Bar,
  <PERSON>Axis,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>ltip as Re<PERSON>rts<PERSON><PERSON><PERSON>,
  Responsive<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Cell
} from 'recharts'
import { useTheme } from '@/renderer/contexts'
import useApi from '@/renderer/hooks/useApi'
import { reportsApi } from '@/renderer/services'
import { FinancialOverviewReport, ReportFormat } from '@/common/types'
import { useEffect } from 'react'

interface Props {
  dateRange?: [Date, Date]
  format: ReportFormat
  shouldGenerate: boolean
  onGenerateComplete: () => void
  currentReportId: string
}

const FinancialOverview = ({
  dateRange,
  format,
  shouldGenerate,
  onGenerateComplete,
  currentReportId
}: Props) => {
  const { isDarkMode } = useTheme()
  const { request, data, isLoading, error, errorMessage } = useApi<
    FinancialOverviewReport,
    [{ format: ReportFormat; startDate?: Date; endDate?: Date }]
  >(reportsApi.generateFinancialOverview)

  useEffect(() => {
    if (shouldGenerate && currentReportId === 'financial-overview') {
      request({
        format,
        startDate: dateRange?.[0],
        endDate: dateRange?.[1]
      }).finally(() => {
        onGenerateComplete()
      })
    }
  }, [shouldGenerate])

  if (isLoading) {
    return (
      <div className="space-y-6">
        {/* Summary Cards Loading */}
        <Row gutter={[16, 16]}>
          {[1, 2, 3, 4, 5].map((key) => (
            <Col xs={24} sm={12} lg={8} xl={6} key={key}>
              <Card>
                <Skeleton active paragraph={{ rows: 1 }} />
              </Card>
            </Col>
          ))}
        </Row>

        {/* Charts Loading */}
        <Row gutter={[16, 16]}>
          <Col xs={24} lg={12}>
            <Card>
              <Skeleton.Input style={{ width: '100%', height: '300px' }} active />
            </Card>
          </Col>
          <Col xs={24} lg={12}>
            <Card>
              <Skeleton.Input style={{ width: '100%', height: '300px' }} active />
            </Card>
          </Col>
        </Row>

        {/* Table Loading */}
        <Card>
          <Skeleton active paragraph={{ rows: 4 }} />
        </Card>
      </div>
    )
  }

  if (error) {
    return (
      <Alert
        message="Error Loading Report"
        description={errorMessage || 'An error occurred while loading the report'}
        type="error"
        showIcon
        className="my-4"
      />
    )
  }

  if (!data) return null

  const { summary, bankBalances, cashDistribution, partyBalances } = data

  // Colors for charts
  const colors = ['#6366f1', '#8b5cf6', '#ec4899', '#f43f5e', '#f97316']

  return (
    <div className="space-y-6">
      {/* Summary Cards */}
      <Row gutter={[16, 16]}>
        <Col xs={24} sm={12} lg={8} xl={6}>
          <Card className={isDarkMode ? 'bg-indigo-900/50' : 'bg-indigo-50'}>
            <Statistic
              title={
                <span className={isDarkMode ? 'text-gray-300' : 'text-gray-600'}>
                  Total Cash in Hand
                </span>
              }
              value={summary.totalCashInHand}
              precision={2}
              prefix="Rs."
              valueStyle={{ color: isDarkMode ? '#fff' : '#000' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={8} xl={6}>
          <Card className={isDarkMode ? 'bg-purple-900/50' : 'bg-purple-50'}>
            <Statistic
              title={
                <span className={isDarkMode ? 'text-gray-300' : 'text-gray-600'}>Bank Balance</span>
              }
              value={summary.totalBankBalance}
              precision={2}
              prefix="Rs."
              valueStyle={{ color: isDarkMode ? '#fff' : '#000' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={8} xl={6}>
          <Card className={isDarkMode ? 'bg-pink-900/50' : 'bg-pink-50'}>
            <Statistic
              title={
                <span className={isDarkMode ? 'text-gray-300' : 'text-gray-600'}>Receivables</span>
              }
              value={summary.totalReceivables}
              precision={2}
              prefix="Rs."
              valueStyle={{ color: isDarkMode ? '#fff' : '#000' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={8} xl={6}>
          <Card className={isDarkMode ? 'bg-rose-900/50' : 'bg-rose-50'}>
            <Statistic
              title={
                <span className={isDarkMode ? 'text-gray-300' : 'text-gray-600'}>Payables</span>
              }
              value={summary.totalPayables}
              precision={2}
              prefix="Rs."
              valueStyle={{ color: isDarkMode ? '#fff' : '#000' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={8} xl={6}>
          <Card className={isDarkMode ? 'bg-orange-900/50' : 'bg-orange-50'}>
            <Statistic
              title={
                <span className={isDarkMode ? 'text-gray-300' : 'text-gray-600'}>Net Position</span>
              }
              value={summary.netPosition}
              precision={2}
              prefix="Rs."
              valueStyle={{ color: isDarkMode ? '#fff' : '#000' }}
            />
          </Card>
        </Col>
      </Row>

      {/* Charts */}
      <Row gutter={[16, 16]}>
        {/* Bank Balances Chart */}
        <Col xs={24} lg={12}>
          <Card title="Bank Balances" className="h-full">
            <div style={{ height: 300 }}>
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={bankBalances}>
                  <XAxis dataKey="bankName" />
                  <YAxis />
                  <RechartsTooltip />
                  <Bar dataKey="balance" fill="#6366f1" />
                </BarChart>
              </ResponsiveContainer>
            </div>
          </Card>
        </Col>

        {/* Cash Distribution Chart */}
        <Col xs={24} lg={12}>
          <Card title="Cash Distribution" className="h-full">
            <div style={{ height: 300 }}>
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={cashDistribution}
                    dataKey="amount"
                    nameKey="location"
                    cx="50%"
                    cy="50%"
                    outerRadius={100}
                    label={(entry) => `${entry.location} (${entry.percentage.toFixed(1)}%)`}
                  >
                    {cashDistribution.map((entry, index) => (
                      <Cell key={entry.location} fill={colors[index % colors.length]} />
                    ))}
                  </Pie>
                  <RechartsTooltip />
                </PieChart>
              </ResponsiveContainer>
            </div>
          </Card>
        </Col>
      </Row>

      {/* Party Balances Table */}
      <Card title="Party Balances">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className={isDarkMode ? 'bg-gray-800' : 'bg-gray-50'}>
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">
                  Party Name
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">
                  Type
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">
                  Balance
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">
                  Last Transaction
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200">
              {partyBalances.map((party) => (
                <tr key={party.partyName}>
                  <td className="whitespace-nowrap px-6 py-4">{party.partyName}</td>
                  <td className="whitespace-nowrap px-6 py-4">{party.type}</td>
                  <td className="whitespace-nowrap px-6 py-4">Rs. {party.balance.toFixed(2)}</td>
                  <td className="whitespace-nowrap px-6 py-4">
                    {new Date(party.lastTransactionDate).toLocaleDateString()}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </Card>
    </div>
  )
}

export default FinancialOverview
